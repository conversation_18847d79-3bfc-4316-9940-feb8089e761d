<!--pages/settings/region.wxml-->
<view class="region-container">
  <view class="region-content">
    <view class="region-section">
      <view class="section-title">选择所在地区</view>
      <view class="region-picker-container">
        <picker mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="全部">
          <view class="picker-content">
            <view class="picker-label">当前选择</view>
            <view class="picker-value">{{region[0]}} {{region[1]}}</view>
            <view class="arrow-right">》</view>
          </view>
        </picker>
      </view>
      <view class="region-tips">设置地区可以帮助您获取更精准的本地服务和推荐</view>
    </view>
    
    <view class="save-button {{loading ? 'disabled' : ''}}" bindtap="saveRegion">
      <text>{{loading ? '保存中...' : '保存'}}</text>
    </view>
  </view>
</view>

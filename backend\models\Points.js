/**
 * 积分模型
 */
const db = require('../config/db');

class Points {
  /**
   * 获取用户积分
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 用户积分信息
   */
  static async getUserPoints(userId) {
    // 检查用户积分记录是否存在，不存在则创建
    const checkResult = await db.query('SELECT * FROM user_points WHERE user_id = ?', [userId]);
    if (checkResult.length === 0) {
      // 创建用户积分记录
      await db.query('INSERT INTO user_points (user_id, points) VALUES (?, ?)', [userId, 0]);
      return { points: 0 };
    }
    return checkResult[0];
  }

  /**
   * 更新用户积分
   * @param {string} userId 用户ID
   * @param {number} changeAmount 积分变动量（正数增加，负数减少）
   * @param {string} event 积分变动事件描述
   * @returns {Promise<Object>} 更新后的积分信息
   */
  static async updateUserPoints(userId, changeAmount, event) {
    // 开始事务
    const connection = await db.getConnection();
    try {
      await connection.beginTransaction();

      // 获取当前积分
      const [userPoints] = await connection.query('SELECT * FROM user_points WHERE user_id = ?', [userId]);
      let currentPoints = 0;
      
      if (userPoints.length === 0) {
        // 用户无积分记录，创建新记录
        await connection.query('INSERT INTO user_points (user_id, points) VALUES (?, ?)', [userId, changeAmount]);
        currentPoints = changeAmount;
      } else {
        // 更新用户积分
        currentPoints = userPoints[0].points + changeAmount;
        await connection.query('UPDATE user_points SET points = ? WHERE user_id = ?', [currentPoints, userId]);
      }

      // 记录积分变动
      await connection.query(
        'INSERT INTO points_records (user_id, change_amount, event, balance) VALUES (?, ?, ?, ?)',
        [userId, changeAmount, event, currentPoints]
      );

      // 提交事务
      await connection.commit();
      
      return { points: currentPoints, changeAmount, event };
    } catch (error) {
      // 回滚事务
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取用户积分记录
   * @param {string} userId 用户ID
   * @param {number} limit 限制返回数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 积分记录列表
   */
  static async getUserPointsRecords(userId, limit = 10, offset = 0) {
    const records = await db.query(
      'SELECT * FROM points_records WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?',
      [userId, limit, offset]
    );
    return records;
  }

  /**
   * 获取积分配置
   * @returns {Promise<Object>} 积分配置信息
   */
  static async getPointsConfig() {
    const configs = await db.query('SELECT * FROM points_config LIMIT 1');
    return configs.length > 0 ? configs[0] : null;
  }

  /**
   * 更新积分配置
   * @param {Object} configData 配置数据
   * @returns {Promise<Object>} 更新后的配置信息
   */
  static async updatePointsConfig(configData) {
    const configs = await db.query('SELECT * FROM points_config LIMIT 1');
    
    if (configs.length > 0) {
      // 更新现有配置
      await db.query(
        'UPDATE points_config SET consume_rule = ?, gain_ways = ? WHERE id = ?',
        [configData.consumeRule, configData.gainWays, configs[0].id]
      );
    } else {
      // 创建新配置
      await db.query(
        'INSERT INTO points_config (consume_rule, gain_ways) VALUES (?, ?)',
        [configData.consumeRule, configData.gainWays]
      );
    }
    
    return this.getPointsConfig();
  }
}

module.exports = Points;

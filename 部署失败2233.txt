[2025-05-29 22:27:18] Started by user coding
[2025-05-29 22:27:18] Running in Durability level: MAX_SURVIVABILITY
[2025-05-29 22:27:21] [Pipeline] Start of Pipeline
[2025-05-29 22:27:21] [Pipeline] node
[2025-05-29 22:27:21] Running on <PERSON> in /root/workspace
[2025-05-29 22:27:21] [Pipeline] {
[2025-05-29 22:27:21] [Pipeline] stage
[2025-05-29 22:27:21] [Pipeline] { (检出软件包)
[2025-05-29 22:27:22] Stage "检出软件包" skipped due to when conditional
[2025-05-29 22:27:22] [Pipeline] }
[2025-05-29 22:27:22] [Pipeline] // stage
[2025-05-29 22:27:22] [Pipeline] stage
[2025-05-29 22:27:22] [Pipeline] { (检出 ZIP 包)
[2025-05-29 22:27:22] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-29 22:27:22] [Pipeline] }
[2025-05-29 22:27:22] [Pipeline] // stage
[2025-05-29 22:27:22] [Pipeline] stage
[2025-05-29 22:27:22] [<PERSON>peline] { (检出代码仓库)
[2025-05-29 22:27:22] [Pipeline] sh
[2025-05-29 22:27:23] + git clone ****** .
[2025-05-29 22:27:23] Cloning into '.'...
[2025-05-29 22:27:25] [Pipeline] sh
[2025-05-29 22:27:25] + git checkout main
[2025-05-29 22:27:25] Already on 'main'
[2025-05-29 22:27:25] Your branch is up to date with 'origin/main'.
[2025-05-29 22:27:26] [Pipeline] }
[2025-05-29 22:27:26] [Pipeline] // stage
[2025-05-29 22:27:26] [Pipeline] stage
[2025-05-29 22:27:26] [Pipeline] { (写入 dockerfile)
[2025-05-29 22:27:26] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-29 22:27:26] [Pipeline] }
[2025-05-29 22:27:26] [Pipeline] // stage
[2025-05-29 22:27:26] [Pipeline] stage
[2025-05-29 22:27:26] [Pipeline] { (构建 Docker 镜像)
[2025-05-29 22:27:26] [Pipeline] sh
[2025-05-29 22:27:26] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-143-20250529222716
[2025-05-29 22:27:26] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-29 22:27:26] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-29 22:27:26] Configure a credential helper to remove this warning. See
[2025-05-29 22:27:26] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-29 22:27:26] 
[2025-05-29 22:27:26] Login Succeeded
[2025-05-29 22:27:26] [Pipeline] sh
[2025-05-29 22:27:27] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-143-20250529222716 backend
[2025-05-29 22:27:28] #0 building with "default" instance using docker driver
[2025-05-29 22:27:28] 
[2025-05-29 22:27:28] #1 [internal] load .dockerignore
[2025-05-29 22:27:28] #1 transferring context:
[2025-05-29 22:27:28] #1 transferring context: 2B done
[2025-05-29 22:27:28] #1 DONE 0.2s
[2025-05-29 22:27:28] 
[2025-05-29 22:27:28] #2 [internal] load build definition from Dockerfile
[2025-05-29 22:27:28] #2 transferring dockerfile: 899B done
[2025-05-29 22:27:28] #2 DONE 0.2s
[2025-05-29 22:27:28] 
[2025-05-29 22:27:28] #3 [internal] load metadata for docker.io/library/node:20-alpine3.18
[2025-05-29 22:27:30] #3 DONE 1.6s
[2025-05-29 22:27:30] 
[2025-05-29 22:27:30] #4 [1/9] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 22:27:30] #4 resolve docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 0.1s done
[2025-05-29 22:27:30] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0B / 3.40MB 0.1s
[2025-05-29 22:27:30] #4 sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 1.43kB / 1.43kB done
[2025-05-29 22:27:30] #4 sha256:991e09934c996820b55f872e292ca2eec916ae1f65f44bb22404d43d93e52e78 1.16kB / 1.16kB done
[2025-05-29 22:27:30] #4 sha256:37173254e18884787979e033e408a4bae4f2c8b2bddb6f5bc3a727a58fd114e4 7.21kB / 7.21kB done
[2025-05-29 22:27:30] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0B / 42.09MB 0.2s
[2025-05-29 22:27:30] #4 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0B / 1.38MB 0.2s
[2025-05-29 22:27:30] #4 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 0B / 451B 0.3s
[2025-05-29 22:27:30] #4 ...
[2025-05-29 22:27:30] 
[2025-05-29 22:27:30] #5 [internal] load build context
[2025-05-29 22:27:30] #5 transferring context: 35.48MB 0.2s done
[2025-05-29 22:27:30] #5 DONE 0.5s
[2025-05-29 22:27:30] 
[2025-05-29 22:27:30] #4 [1/9] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 22:27:30] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 3.40MB / 3.40MB 0.5s
[2025-05-29 22:27:30] #4 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 451B / 451B 0.4s done
[2025-05-29 22:27:31] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 3.40MB / 3.40MB 0.5s done
[2025-05-29 22:27:31] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 7.34MB / 42.09MB 0.6s
[2025-05-29 22:27:31] #4 extracting sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0.1s
[2025-05-29 22:27:31] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 18.87MB / 42.09MB 0.7s
[2025-05-29 22:27:31] #4 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 1.38MB / 1.38MB 0.5s done
[2025-05-29 22:27:31] #4 extracting sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0.1s done
[2025-05-29 22:27:31] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 39.85MB / 42.09MB 0.9s
[2025-05-29 22:27:31] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 42.09MB / 42.09MB 1.0s
[2025-05-29 22:27:31] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 42.09MB / 42.09MB 1.0s done
[2025-05-29 22:27:31] #4 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0.1s
[2025-05-29 22:27:32] #4 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0.8s done
[2025-05-29 22:27:32] #4 extracting sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057
[2025-05-29 22:27:32] #4 extracting sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0.0s done
[2025-05-29 22:27:32] #4 extracting sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d done
[2025-05-29 22:27:33] #4 DONE 3.1s
[2025-05-29 22:27:33] 
[2025-05-29 22:27:33] #6 [2/9] RUN apk add --no-cache python3 make g++ gcc netcat-openbsd
[2025-05-29 22:27:33] #6 0.358 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/main/x86_64/APKINDEX.tar.gz
[2025-05-29 22:27:34] #6 0.846 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/community/x86_64/APKINDEX.tar.gz
[2025-05-29 22:27:34] #6 1.067 (1/33) Upgrading musl (1.2.4-r2 -> 1.2.4-r3)
[2025-05-29 22:27:34] #6 1.111 (2/33) Installing libstdc++-dev (12.2.1_git20220924-r10)
[2025-05-29 22:27:34] #6 1.396 (3/33) Installing zstd-libs (1.5.5-r4)
[2025-05-29 22:27:34] #6 1.435 (4/33) Installing binutils (2.40-r8)
[2025-05-29 22:27:34] #6 1.514 (5/33) Installing libgomp (12.2.1_git20220924-r10)
[2025-05-29 22:27:34] #6 1.549 (6/33) Installing libatomic (12.2.1_git20220924-r10)
[2025-05-29 22:27:34] #6 1.581 (7/33) Installing gmp (6.2.1-r3)
[2025-05-29 22:27:34] #6 1.617 (8/33) Installing isl26 (0.26-r1)
[2025-05-29 22:27:35] #6 1.664 (9/33) Installing mpfr4 (4.2.0_p12-r0)
[2025-05-29 22:27:35] #6 1.702 (10/33) Installing mpc1 (1.3.1-r1)
[2025-05-29 22:27:35] #6 1.736 (11/33) Installing gcc (12.2.1_git20220924-r10)
[2025-05-29 22:27:36] #6 2.748 (12/33) Installing musl-dev (1.2.4-r3)
[2025-05-29 22:27:36] #6 2.916 (13/33) Installing libc-dev (0.7.2-r5)
[2025-05-29 22:27:36] #6 2.949 (14/33) Installing g++ (12.2.1_git20220924-r10)
[2025-05-29 22:27:36] #6 3.536 (15/33) Installing make (4.4.1-r1)
[2025-05-29 22:27:36] #6 3.575 (16/33) Installing libmd (1.0.4-r2)
[2025-05-29 22:27:36] #6 3.609 (17/33) Installing libbsd (0.11.7-r1)
[2025-05-29 22:27:37] #6 3.642 (18/33) Installing netcat-openbsd (1.219-r1)
[2025-05-29 22:27:37] #6 3.675 (19/33) Installing libexpat (2.7.0-r0)
[2025-05-29 22:27:37] #6 3.710 (20/33) Installing libbz2 (1.0.8-r5)
[2025-05-29 22:27:37] #6 3.744 (21/33) Installing libffi (3.4.4-r2)
[2025-05-29 22:27:37] #6 3.777 (22/33) Installing gdbm (1.23-r1)
[2025-05-29 22:27:37] #6 3.810 (23/33) Installing xz-libs (5.4.3-r1)
[2025-05-29 22:27:37] #6 3.848 (24/33) Installing mpdecimal (2.5.1-r2)
[2025-05-29 22:27:37] #6 3.884 (25/33) Installing ncurses-terminfo-base (6.4_p20230506-r0)
[2025-05-29 22:27:37] #6 3.924 (26/33) Installing libncursesw (6.4_p20230506-r0)
[2025-05-29 22:27:37] #6 3.963 (27/33) Installing libpanelw (6.4_p20230506-r0)
[2025-05-29 22:27:37] #6 3.998 (28/33) Installing readline (8.2.1-r1)
[2025-05-29 22:27:37] #6 4.036 (29/33) Installing sqlite-libs (3.41.2-r3)
[2025-05-29 22:27:37] #6 4.087 (30/33) Installing python3 (3.11.12-r1)
[2025-05-29 22:27:37] #6 4.485 (31/33) Installing python3-pycache-pyc0 (3.11.12-r1)
[2025-05-29 22:27:38] #6 4.716 (32/33) Installing pyc (0.1-r0)
[2025-05-29 22:27:38] #6 4.749 (33/33) Installing python3-pyc (3.11.12-r1)
[2025-05-29 22:27:38] #6 4.782 Executing busybox-1.36.1-r5.trigger
[2025-05-29 22:27:38] #6 4.786 OK: 276 MiB in 49 packages
[2025-05-29 22:27:41] #6 DONE 8.2s
[2025-05-29 22:27:41] 
[2025-05-29 22:27:41] #7 [3/9] WORKDIR /app
[2025-05-29 22:27:41] #7 DONE 0.2s
[2025-05-29 22:27:41] 
[2025-05-29 22:27:41] #8 [4/9] RUN mkdir -p /app/uploads /app/public/qrcode /app/__tmp__
[2025-05-29 22:27:42] #8 DONE 0.3s
[2025-05-29 22:27:42] 
[2025-05-29 22:27:42] #9 [5/9] COPY package*.json ./
[2025-05-29 22:27:42] #9 DONE 0.2s
[2025-05-29 22:27:42] 
[2025-05-29 22:27:42] #10 [6/9] RUN npm config set registry https://registry.npmmirror.com
[2025-05-29 22:27:43] #10 DONE 0.8s
[2025-05-29 22:27:43] 
[2025-05-29 22:27:43] #11 [7/9] RUN npm install --production
[2025-05-29 22:27:43] #11 0.540 npm WARN config production Use `--omit=dev` instead.
[2025-05-29 22:27:45] #11 2.116 npm WARN deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-29 22:27:45] #11 2.213 npm WARN deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-29 22:27:45] #11 2.668 npm WARN deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-29 22:27:46] #11 3.047 
[2025-05-29 22:27:46] #11 3.047 added 221 packages in 3s
[2025-05-29 22:27:46] #11 3.047 
[2025-05-29 22:27:46] #11 3.047 31 packages are looking for funding
[2025-05-29 22:27:46] #11 3.047   run `npm fund` for details
[2025-05-29 22:27:46] #11 DONE 3.3s
[2025-05-29 22:27:46] 
[2025-05-29 22:27:46] #12 [8/9] COPY . .
[2025-05-29 22:27:46] #12 DONE 0.3s
[2025-05-29 22:27:46] 
[2025-05-29 22:27:46] #13 [9/9] RUN rm -f wait-for-it.sh Dockerfile.cloud generate-cert.js &&     rm -rf test docs temp __tmp__ &&     rm -f config/db-backup.js config/db-init.js config/db-seed.js config/db-validate.js
[2025-05-29 22:27:47] #13 DONE 0.4s
[2025-05-29 22:27:47] 
[2025-05-29 22:27:47] #14 exporting to image
[2025-05-29 22:27:47] #14 exporting layers
[2025-05-29 22:27:49] #14 exporting layers 1.8s done
[2025-05-29 22:27:49] #14 writing image sha256:32dd09f65321b7c2d80640f11ec0551ac234f5480b1397703ceea29183f9584e 0.0s done
[2025-05-29 22:27:49] #14 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-143-20250529222716 0.0s done
[2025-05-29 22:27:49] #14 DONE 1.8s
[2025-05-29 22:27:49] [Pipeline] sh
[2025-05-29 22:27:49] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-29 22:27:49] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-143-20250529222716
[2025-05-29 22:27:49] + awk {print $NF}
[2025-05-29 22:27:49] + echo 镜像的大小是：460MB
[2025-05-29 22:27:49] 镜像的大小是：460MB
[2025-05-29 22:27:49] [Pipeline] echo
[2025-05-29 22:27:49] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-29 22:27:49] [Pipeline] }
[2025-05-29 22:27:49] [Pipeline] // stage
[2025-05-29 22:27:49] [Pipeline] stage
[2025-05-29 22:27:49] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-29 22:27:49] [Pipeline] sh
[2025-05-29 22:27:50] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-143-20250529222716
[2025-05-29 22:27:50] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-29 22:27:50] ff98c221b51a: Preparing
[2025-05-29 22:27:50] 80dc89ae8b36: Preparing
[2025-05-29 22:27:50] 4b6f04858ff8: Preparing
[2025-05-29 22:27:50] 9db49eee9129: Preparing
[2025-05-29 22:27:50] f69b72d2eac1: Preparing
[2025-05-29 22:27:50] eaba2cab267b: Preparing
[2025-05-29 22:27:50] 194cc2bbb679: Preparing
[2025-05-29 22:27:50] 99208dbfaa83: Preparing
[2025-05-29 22:27:50] 1709f4dd23fa: Preparing
[2025-05-29 22:27:50] 92f7edb51836: Preparing
[2025-05-29 22:27:50] af0fd4ac5053: Preparing
[2025-05-29 22:27:50] aedc3bda2944: Preparing
[2025-05-29 22:27:50] af0fd4ac5053: Waiting
[2025-05-29 22:27:50] aedc3bda2944: Waiting
[2025-05-29 22:27:50] 1709f4dd23fa: Layer already exists
[2025-05-29 22:27:50] 92f7edb51836: Layer already exists
[2025-05-29 22:27:50] af0fd4ac5053: Layer already exists
[2025-05-29 22:27:50] aedc3bda2944: Layer already exists
[2025-05-29 22:27:51] 9db49eee9129: Pushed
[2025-05-29 22:27:51] eaba2cab267b: Pushed
[2025-05-29 22:27:51] 194cc2bbb679: Pushed
[2025-05-29 22:27:51] f69b72d2eac1: Pushed
[2025-05-29 22:27:51] ff98c221b51a: Pushed
[2025-05-29 22:27:55] 4b6f04858ff8: Pushed
[2025-05-29 22:28:10] 80dc89ae8b36: Pushed
[2025-05-29 22:28:36] 99208dbfaa83: Pushed
[2025-05-29 22:28:36] lieyouqi-143-20250529222716: digest: sha256:a576c0f4f0ac912822928ce7f69c6d2dd8c3fb7d68db173bb7f3e7f4897b6bd5 size: 2830
[2025-05-29 22:28:36] [Pipeline] }
[2025-05-29 22:28:36] [Pipeline] // stage
[2025-05-29 22:28:36] [Pipeline] }
[2025-05-29 22:28:36] [Pipeline] // node
[2025-05-29 22:28:36] [Pipeline] End of Pipeline
[2025-05-29 22:28:36] Finished: SUCCESS
***
-----------构建lieyouqi-143-----------
2025-05-29 22:27:18 create_build_image : creating
2025-05-29 22:28:41 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-143-----------
2025-05-29 22:28:42 create_eks_virtual_service : creating
2025-05-29 22:28:43 check_eks_virtual_service : process, DescribeVersion_user_error_Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: , message: "", [service]:[Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "b45b964c9a81395b0b0c9d9f826d7edd61d3c815050406fa6a6d6fcf9cc6832b": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "",Back-off restarting failed container,Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "e8928a64bad547003240ab365ec4bd37764eaecf800f915d67e3fb1698e5232c": OCI runtime exec failed: exec failed: unable to start container process: error executing setns process: exit status 1: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "14576ce96130b502c723e34484c96685d672f6782115b7cc47d1753d3771ef3b": OCI runtime exec failed: exec failed: unable to start container process: error executing setns process: exit status 1: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-143" in Pod "lieyouqi-143-54fb7c87b-r5hc5_yhrfubyv(a9febe1e-c9e2-46ce-961d-eb9ea7790bcc)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: , message: "",]
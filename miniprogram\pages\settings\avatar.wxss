/* pages/settings/avatar.wxss */
.page {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 40rpx;
}

.avatar-section {
  background-color: #ffffff;
  padding: 40rpx 0;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.current-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-preview {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  border: 1px solid #eeeeee;
}

.avatar-tips {
  font-size: 28rpx;
  color: #999999;
}

.section-title {
  font-size: 30rpx;
  color: #333333;
  padding: 30rpx 30rpx 20rpx;
  background-color: #ffffff;
  margin-top: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 水平滚动视图样式 */
.avatar-scroll {
  background-color: #ffffff;
  white-space: nowrap;
  padding: 20rpx 0;
}

.avatar-list {
  display: flex;
  padding: 0 20rpx;
  background-color: #ffffff;
}

.avatar-item {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 10rpx;
  margin-right: 30rpx;
  width: 140rpx;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 1px solid #eeeeee;
}

.avatar-item.selected .avatar-image {
  border: 3rpx solid #07c160;
}

.selected-icon {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #07c160;
  display: flex;
  justify-content: center;
  align-items: center;
}

.selected-icon::after {
  content: '';
  display: block;
  width: 16rpx;
  height: 8rpx;
  border-left: 3rpx solid #ffffff;
  border-bottom: 3rpx solid #ffffff;
  transform: rotate(-45deg) translate(1rpx, -1rpx);
}

.action-list {
  background-color: #ffffff;
  padding: 30rpx;
}

.action-button {
  background-color: #f8f8f8;
  color: #333333;
  font-size: 30rpx;
  margin-bottom: 20rpx;
  border: none;
  padding: 20rpx 0;
}

.action-button::after {
  border: none;
}

.footer {
  padding: 40rpx 30rpx;
}

.primary-button {
  background-color: #07c160;
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 10rpx;
  border: none;
}

.primary-button::after {
  border: none;
}

.primary-button.disabled {
  background-color: #9ed29e;
}

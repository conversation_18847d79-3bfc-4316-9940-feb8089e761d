
[2025-05-30 00:29:09] Started by user coding
[2025-05-30 00:29:09] Running in Durability level: MAX_SURVIVABILITY
[2025-05-30 00:29:11] [Pipeline] Start of Pipeline
[2025-05-30 00:29:11] [Pipeline] node
[2025-05-30 00:29:11] Running on <PERSON> in /root/workspace
[2025-05-30 00:29:11] [Pipeline] {
[2025-05-30 00:29:11] [Pipeline] stage
[2025-05-30 00:29:11] [Pipeline] { (检出软件包)
[2025-05-30 00:29:11] Stage "检出软件包" skipped due to when conditional
[2025-05-30 00:29:12] [Pipeline] }
[2025-05-30 00:29:12] [Pipeline] // stage
[2025-05-30 00:29:12] [Pipeline] stage
[2025-05-30 00:29:12] [Pipeline] { (检出 ZIP 包)
[2025-05-30 00:29:12] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-30 00:29:12] [Pipeline] }
[2025-05-30 00:29:12] [Pipeline] // stage
[2025-05-30 00:29:12] [Pipeline] stage
[2025-05-30 00:29:12] [Pipeline] { (检出代码仓库)
[2025-05-30 00:29:12] [Pipeline] sh
[2025-05-30 00:29:12] + git clone ****** .
[2025-05-30 00:29:12] Cloning into '.'...
[2025-05-30 00:29:15] [Pipeline] sh
[2025-05-30 00:29:15] + git checkout main
[2025-05-30 00:29:15] Already on 'main'
[2025-05-30 00:29:15] Your branch is up to date with 'origin/main'.
[2025-05-30 00:29:15] [Pipeline] }
[2025-05-30 00:29:15] [Pipeline] // stage
[2025-05-30 00:29:15] [Pipeline] stage
[2025-05-30 00:29:15] [Pipeline] { (写入 dockerfile)
[2025-05-30 00:29:15] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-30 00:29:15] [Pipeline] }
[2025-05-30 00:29:15] [Pipeline] // stage
[2025-05-30 00:29:15] [Pipeline] stage
[2025-05-30 00:29:15] [Pipeline] { (构建 Docker 镜像)
[2025-05-30 00:29:15] [Pipeline] sh
[2025-05-30 00:29:16] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-146-20250530002907
[2025-05-30 00:29:16] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-30 00:29:16] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-30 00:29:16] Configure a credential helper to remove this warning. See
[2025-05-30 00:29:16] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-30 00:29:16] 
[2025-05-30 00:29:16] Login Succeeded
[2025-05-30 00:29:16] [Pipeline] sh
[2025-05-30 00:29:16] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-146-20250530002907 backend
[2025-05-30 00:29:17] #0 building with "default" instance using docker driver
[2025-05-30 00:29:17] 
[2025-05-30 00:29:17] #1 [internal] load .dockerignore
[2025-05-30 00:29:17] #1 transferring context:
[2025-05-30 00:29:17] #1 transferring context: 2B done
[2025-05-30 00:29:17] #1 DONE 0.2s
[2025-05-30 00:29:17] 
[2025-05-30 00:29:17] #2 [internal] load build definition from Dockerfile
[2025-05-30 00:29:17] #2 transferring dockerfile: 578B done
[2025-05-30 00:29:17] #2 DONE 0.2s
[2025-05-30 00:29:17] 
[2025-05-30 00:29:17] #3 [internal] load metadata for docker.io/library/node:18-alpine
[2025-05-30 00:29:19] #3 DONE 1.2s
[2025-05-30 00:29:19] 
[2025-05-30 00:29:19] #4 [1/7] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 00:29:19] #4 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 0.1s done
[2025-05-30 00:29:19] #4 sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 7.67kB / 7.67kB done
[2025-05-30 00:29:19] #4 sha256:929b04d7c782f04f615cf785488fed452b6569f87c73ff666ad553a7554f0006 1.72kB / 1.72kB done
[2025-05-30 00:29:19] #4 sha256:ee77c6cd7c1886ecc802ad6cedef3a8ec1ea27d1fb96162bf03dd3710839b8da 6.18kB / 6.18kB done
[2025-05-30 00:29:19] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0B / 3.64MB 0.1s
[2025-05-30 00:29:19] #4 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0B / 1.26MB 0.1s
[2025-05-30 00:29:19] #4 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 0B / 446B 0.1s
[2025-05-30 00:29:19] #4 ...
[2025-05-30 00:29:19] 
[2025-05-30 00:29:19] #5 [internal] load build context
[2025-05-30 00:29:19] #5 transferring context: 35.48MB 0.2s done
[2025-05-30 00:29:19] #5 DONE 0.3s
[2025-05-30 00:29:19] 
[2025-05-30 00:29:19] #4 [1/7] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 00:29:19] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0B / 40.01MB 0.2s
[2025-05-30 00:29:19] #4 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 1.26MB / 1.26MB 0.3s done
[2025-05-30 00:29:19] #4 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 446B / 446B 0.3s done
[2025-05-30 00:29:19] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.15MB / 3.64MB 0.5s
[2025-05-30 00:29:19] #4 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870
[2025-05-30 00:29:19] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.64MB / 3.64MB 0.5s done
[2025-05-30 00:29:19] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 11.53MB / 40.01MB 0.7s
[2025-05-30 00:29:19] #4 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0.1s done
[2025-05-30 00:29:19] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 22.02MB / 40.01MB 0.8s
[2025-05-30 00:29:19] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 33.55MB / 40.01MB 0.9s
[2025-05-30 00:29:20] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 1.0s
[2025-05-30 00:29:20] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 1.0s done
[2025-05-30 00:29:20] #4 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0.1s
[2025-05-30 00:29:21] #4 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0.9s done
[2025-05-30 00:29:21] #4 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3
[2025-05-30 00:29:21] #4 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0.0s done
[2025-05-30 00:29:21] #4 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549
[2025-05-30 00:29:21] #4 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 done
[2025-05-30 00:29:21] #4 DONE 2.7s
[2025-05-30 00:29:21] 
[2025-05-30 00:29:21] #6 [2/7] RUN apk add --no-cache python3 make g++ gcc
[2025-05-30 00:29:21] #6 0.256 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
[2025-05-30 00:29:22] #6 0.646 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
[2025-05-30 00:29:22] #6 0.935 (1/29) Installing libstdc++-dev (14.2.0-r4)
[2025-05-30 00:29:22] #6 1.078 (2/29) Installing jansson (2.14-r4)
[2025-05-30 00:29:22] #6 1.111 (3/29) Installing zstd-libs (1.5.6-r2)
[2025-05-30 00:29:22] #6 1.148 (4/29) Installing binutils (2.43.1-r2)
[2025-05-30 00:29:22] #6 1.239 (5/29) Installing libgomp (14.2.0-r4)
[2025-05-30 00:29:22] #6 1.274 (6/29) Installing libatomic (14.2.0-r4)
[2025-05-30 00:29:22] #6 1.307 (7/29) Installing gmp (6.3.0-r2)
[2025-05-30 00:29:22] #6 1.343 (8/29) Installing isl26 (0.26-r1)
[2025-05-30 00:29:23] #6 1.389 (9/29) Installing mpfr4 (4.2.1-r0)
[2025-05-30 00:29:23] #6 1.426 (10/29) Installing mpc1 (1.3.1-r1)
[2025-05-30 00:29:23] #6 1.459 (11/29) Installing gcc (14.2.0-r4)
[2025-05-30 00:29:24] #6 2.722 (12/29) Installing musl-dev (1.2.5-r9)
[2025-05-30 00:29:24] #6 2.902 (13/29) Installing g++ (14.2.0-r4)
[2025-05-30 00:29:25] #6 3.591 (14/29) Installing make (4.4.1-r2)
[2025-05-30 00:29:25] #6 3.629 (15/29) Installing libbz2 (1.0.8-r6)
[2025-05-30 00:29:25] #6 3.662 (16/29) Installing libexpat (2.7.0-r0)
[2025-05-30 00:29:25] #6 3.695 (17/29) Installing libffi (3.4.7-r0)
[2025-05-30 00:29:25] #6 3.727 (18/29) Installing gdbm (1.24-r0)
[2025-05-30 00:29:25] #6 3.759 (19/29) Installing xz-libs (5.6.3-r1)
[2025-05-30 00:29:25] #6 3.795 (20/29) Installing mpdecimal (4.0.0-r0)
[2025-05-30 00:29:25] #6 3.830 (21/29) Installing ncurses-terminfo-base (6.5_p20241006-r3)
[2025-05-30 00:29:25] #6 3.863 (22/29) Installing libncursesw (6.5_p20241006-r3)
[2025-05-30 00:29:25] #6 3.900 (23/29) Installing libpanelw (6.5_p20241006-r3)
[2025-05-30 00:29:25] #6 3.932 (24/29) Installing readline (8.2.13-r0)
[2025-05-30 00:29:25] #6 3.968 (25/29) Installing sqlite-libs (3.48.0-r2)
[2025-05-30 00:29:25] #6 4.032 (26/29) Installing python3 (3.12.10-r1)
[2025-05-30 00:29:26] #6 4.393 (27/29) Installing python3-pycache-pyc0 (3.12.10-r1)
[2025-05-30 00:29:26] #6 4.588 (28/29) Installing pyc (3.12.10-r1)
[2025-05-30 00:29:26] #6 4.588 (29/29) Installing python3-pyc (3.12.10-r1)
[2025-05-30 00:29:26] #6 4.588 Executing busybox-1.37.0-r12.trigger
[2025-05-30 00:29:26] #6 4.592 OK: 269 MiB in 46 packages
[2025-05-30 00:29:26] #6 DONE 4.7s
[2025-05-30 00:29:26] 
[2025-05-30 00:29:26] #7 [3/7] WORKDIR /app
[2025-05-30 00:29:29] #7 DONE 2.6s
[2025-05-30 00:29:29] 
[2025-05-30 00:29:29] #8 [4/7] COPY package*.json ./
[2025-05-30 00:29:29] #8 DONE 0.1s
[2025-05-30 00:29:29] 
[2025-05-30 00:29:29] #9 [5/7] RUN npm config set registry https://registry.npmmirror.com
[2025-05-30 00:29:29] #9 DONE 0.6s
[2025-05-30 00:29:29] 
[2025-05-30 00:29:29] #10 [6/7] RUN npm install --production
[2025-05-30 00:29:30] #10 0.485 npm warn config production Use `--omit=dev` instead.
[2025-05-30 00:29:32] #10 2.363 npm warn deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-30 00:29:32] #10 2.493 npm warn deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-30 00:29:32] #10 2.564 npm warn deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-30 00:29:32] #10 2.992 
[2025-05-30 00:29:32] #10 2.992 added 221 packages in 3s
[2025-05-30 00:29:32] #10 2.992 
[2025-05-30 00:29:32] #10 2.992 31 packages are looking for funding
[2025-05-30 00:29:32] #10 2.992   run `npm fund` for details
[2025-05-30 00:29:32] #10 DONE 3.1s
[2025-05-30 00:29:33] 
[2025-05-30 00:29:33] #11 [7/7] COPY . .
[2025-05-30 00:29:33] #11 DONE 0.2s
[2025-05-30 00:29:33] 
[2025-05-30 00:29:33] #12 exporting to image
[2025-05-30 00:29:33] #12 exporting layers
[2025-05-30 00:29:34] #12 exporting layers 1.6s done
[2025-05-30 00:29:34] #12 writing image sha256:f001bfffbf0a78fb14b36d36412a6b09b80a0ae56e7ff196af92300d855a6879 done
[2025-05-30 00:29:34] #12 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-146-20250530002907 0.0s done
[2025-05-30 00:29:34] #12 DONE 1.6s
[2025-05-30 00:29:34] [Pipeline] sh
[2025-05-30 00:29:35] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-30 00:29:35] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-146-20250530002907
[2025-05-30 00:29:35] + awk {print $NF}
[2025-05-30 00:29:35] + echo 镜像的大小是：456MB
[2025-05-30 00:29:35] 镜像的大小是：456MB
[2025-05-30 00:29:35] [Pipeline] echo
[2025-05-30 00:29:35] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-30 00:29:35] [Pipeline] }
[2025-05-30 00:29:35] [Pipeline] // stage
[2025-05-30 00:29:35] [Pipeline] stage
[2025-05-30 00:29:35] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-30 00:29:35] [Pipeline] sh
[2025-05-30 00:29:35] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-146-20250530002907
[2025-05-30 00:29:35] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-30 00:29:35] d6b2ca93ceeb: Preparing
[2025-05-30 00:29:35] 5a546634d90e: Preparing
[2025-05-30 00:29:35] 25fdf5c76655: Preparing
[2025-05-30 00:29:35] f03e2f2e659e: Preparing
[2025-05-30 00:29:35] 7b024d3bac3c: Preparing
[2025-05-30 00:29:35] 820b27cd4e34: Preparing
[2025-05-30 00:29:35] 82140d9a70a7: Preparing
[2025-05-30 00:29:35] f3b40b0cdb1c: Preparing
[2025-05-30 00:29:35] 0b1f26057bd0: Preparing
[2025-05-30 00:29:35] 08000c18d16d: Preparing
[2025-05-30 00:29:35] 0b1f26057bd0: Layer already exists
[2025-05-30 00:29:35] f3b40b0cdb1c: Layer already exists
[2025-05-30 00:29:35] 08000c18d16d: Layer already exists
[2025-05-30 00:29:35] 82140d9a70a7: Layer already exists
[2025-05-30 00:29:36] f03e2f2e659e: Pushed
[2025-05-30 00:29:37] 25fdf5c76655: Pushed
[2025-05-30 00:29:37] 7b024d3bac3c: Pushed
[2025-05-30 00:29:42] 5a546634d90e: Pushed
[2025-05-30 00:29:54] d6b2ca93ceeb: Pushed
[2025-05-30 00:30:32] 820b27cd4e34: Pushed
[2025-05-30 00:30:32] lieyouqi-146-20250530002907: digest: sha256:77b39381ec5c5d50e265aad41ea7ba726c82b2e0f4b7ce8cc9e02f102de59ebd size: 2416
[2025-05-30 00:30:32] [Pipeline] }
[2025-05-30 00:30:32] [Pipeline] // stage
[2025-05-30 00:30:32] [Pipeline] }
[2025-05-30 00:30:33] [Pipeline] // node
[2025-05-30 00:30:33] [Pipeline] End of Pipeline
[2025-05-30 00:30:33] Finished: SUCCESS
***
-----------构建lieyouqi-146-----------
2025-05-30 00:29:08 create_build_image : creating
2025-05-30 00:30:37 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-146-----------
2025-05-30 00:30:38 create_eks_virtual_service : creating
2025-05-30 00:30:38 check_eks_virtual_service : process, DescribeVersion_user_error_Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "b97a114e9762a5f7bfae7267cef774335cb8639bee326b4d29b17487af35d50f": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "", [service]:[Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: , message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "92d7a19c79cd1d4abfb6b7d9279bb2331840ac3c1e037a361ac2a67d66e085af": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "",Back-off restarting failed container,Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
+ '[' 0 -eq 0 ]
+ echo 'User is root, patching env and certs.'
+ '[' '!' -f /app/cert/certificate.crt ]
+ '[' '!' -f /etc/os-release ]
+ . /etc/os-release
+ NAME='Alpine Linux'
+ ID=alpine
+ VERSION_ID=3.21.3
+ PRETTY_NAME='Alpine Linux v3.21'
+ HOME_URL=https://alpinelinux.org/
+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues
+ echo '[I]: os release is alpine'
+ update-ca-certificates -h
/app/cert/initenv.sh: line 66: update-ca-certificates: not found
+ grep ************
+ echo '************ api.weixin.qq.com'
+ echo '[I]: /etc/hosts'
+ cat /etc/hosts
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n+ '[' 0 -eq 0 ]\n+ echo 'User is root, patching env and certs.'\n+ '[' '!' -f /app/cert/certificate.crt ]\n+ '[' '!' -f /etc/os-release ]\n+ . /etc/os-release\n+ NAME='Alpine Linux'\n+ ID=alpine\n+ VERSION_ID=3.21.3\n+ PRETTY_NAME='Alpine Linux v3.21'\n+ HOME_URL=https://alpinelinux.org/\n+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues\n+ echo '[I]: os release is alpine'\n+ update-ca-certificates -h\n/app/cert/initenv.sh: line 66: update-ca-certificates: not found\n+ grep ************\n+ echo '************ api.weixin.qq.com'\n+ echo '[I]: /etc/hosts'\n+ cat /etc/hosts\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-146" in Pod "lieyouqi-146-55cdd76d7d-fs92t_yhrfubyv(3c20d736-ce23-4560-8a7a-2f1c78100790)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "b97a114e9762a5f7bfae7267cef774335cb8639bee326b4d29b17487af35d50f": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "",]
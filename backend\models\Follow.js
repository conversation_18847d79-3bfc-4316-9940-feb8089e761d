const db = require('../utils/db');

class Follow {
  static async follow(followerId, followingId) {
    try {
      // 检查是否已经关注
      const existing = await db.query(
        'SELECT * FROM follows WHERE followerId = ? AND followingId = ?',
        [followerId, followingId]
      );
      
      if (existing.length > 0) {
        return {
          success: false,
          message: '已经关注过该用户'
        };
      }

      // 添加关注记录
      await db.query(
        'INSERT INTO follows (followerId, followingId, createTime) VALUES (?, ?, ?)',
        [followerId, followingId, Date.now()]
      );

      return {
        success: true,
        message: '关注成功'
      };
    } catch (error) {
      console.error('关注用户失败:', error);
      return {
        success: false,
        message: '数据库异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  static async unfollow(followerId, followingId) {
    try {
      await db.query(
        'DELETE FROM follows WHERE followerId = ? AND followingId = ?',
        [followerId, followingId]
      );

      return {
        success: true,
        message: '取消关注成功'
      };
    } catch (error) {
      console.error('取消关注失败:', error);
      throw error;
    }
  }

  static async isFollowing(followerId, followingId) {
    try {
      const result = await db.query(
        'SELECT * FROM follows WHERE followerId = ? AND followingId = ?',
        [followerId, followingId]
      );

      return result.length > 0;
    } catch (error) {
      console.error('检查关注状态失败:', error);
      throw error;
    }
  }

  static async getFollowingCount(userId) {
    try {
      const result = await db.query(
        'SELECT COUNT(*) as count FROM follows WHERE followerId = ?',
        [userId]
      );
      return result[0].count || 0;
    } catch (error) {
      console.error('获取关注数失败:', error);
      throw error;
    }
  }

  static async getFollowerCount(userId) {
    try {
      const result = await db.query(
        'SELECT COUNT(*) as count FROM follows WHERE followingId = ?',
        [userId]
      );
      return result[0].count || 0;
    } catch (error) {
      console.error('获取粉丝数失败:', error);
      throw error;
    }
  }
}

module.exports = Follow; 
/**
 * 路由索引
 */
const express = require('express');
const productRoutes = require('./products');
const userRoutes = require('./users');
const postRoutes = require('./posts');
const cartRoutes = require('./cart');
const messageRoutes = require('./messages');
const messageRoutesSingular = require('./message');
const systemRoutes = require('./system');
const uploadRoutes = require('./upload');
const groupRoutes = require('./group');
const adminRoutes = require('./admin');
const companyRoutes = require('./company');
const shareRoutes = require('./share');
const pointsRoutes = require('./points');
const vipRoutes = require('./vip');

const router = express.Router();

router.use('/products', productRoutes);
router.use('/users', userRoutes);
router.use('/posts', postRoutes);
router.use('/cart', cartRoutes);
router.use('/messages', messageRoutes);
router.use('/message', messageRoutesSingular);
router.use('/system', systemRoutes);
router.use('/upload', uploadRoutes);
router.use('/group', groupRoutes);
router.use('/admin', adminRoutes);
router.use('/company', companyRoutes);
router.use('/share', shareRoutes);
router.use('/points', pointsRoutes);
router.use('/vip', vipRoutes);

module.exports = router;

// pages/auth/auth.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    // 登录类型：phone-手机号登录，account-账号密码登录
    loginType: 'phone',
    // 手机号登录相关
    phoneNumber: '',
    verifyCode: '',
    countdown: 0,
    // 账号密码登录相关
    username: '',
    password: '',
    // 通用
    isAgreement: true, // 默认已勾选用户协议
    canGetCode: true,
    canLogin: false,
    // 随机验证码
    randomCode: '',
    redirectUrl: ''
  },

  onLoad: function (options) {
    // 记录redirect参数
    this.redirectUrl = options.redirect || '';

    // 处理推荐参数
    if (options.scene) {
      console.log('检测到推荐参数:', options.scene);
      // 保存推荐人ID到本地存储
      wx.setStorageSync('referrerId', options.scene);

      // 显示推荐提示
      wx.showToast({
        title: '来自好友推荐',
        icon: 'success',
        duration: 2000
      });
    }
  },

  // 输入手机号
  inputPhoneNumber: function(e) {
    const phoneNumber = e.detail.value;
    this.setData({
      phoneNumber: phoneNumber,
      canGetCode: this.isValidPhoneNumber(phoneNumber)
    });
    this.checkCanLogin();
  },

  // 输入验证码
  inputVerifyCode: function(e) {
    const verifyCode = e.detail.value;
    this.setData({
      verifyCode: verifyCode
    });
    this.checkCanLogin();
  },

  // 切换登录类型
  switchLoginType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      loginType: type
    });
    this.checkCanLogin();
  },

  // 输入用户名
  inputUsername: function(e) {
    const username = e.detail.value;
    this.setData({
      username: username
    });
    this.checkCanLogin();
  },

  // 输入密码
  inputPassword: function(e) {
    const password = e.detail.value;
    this.setData({
      password: password
    });
    this.checkCanLogin();
  },

  // 检查是否可以登录
  checkCanLogin: function() {
    const { loginType, phoneNumber, verifyCode, username, password } = this.data;

    let canLogin = false;

    if (loginType === 'phone') {
      // 手机号登录：手机号有效且验证码长度为6位
      canLogin = this.isValidPhoneNumber(phoneNumber) && verifyCode.length === 6;
    } else if (loginType === 'account') {
      // 账号密码登录：用户名和密码都不为空
      canLogin = username.trim().length > 0 && password.trim().length > 0;
    }

    this.setData({
      canLogin: canLogin
    });
  },

  // 验证手机号格式
  isValidPhoneNumber: function(phoneNumber) {
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(phoneNumber);
  },

  // 获取验证码
  getVerifyCode: function() {
    if (!this.data.canGetCode || this.data.countdown > 0) {
      return;
    }

    // 检查手机号是否有效
    if (!this.isValidPhoneNumber(this.data.phoneNumber)) {
      wx.showToast({
        title: '请输入有效的手机号',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中...',
    });

    console.log('正在发送验证码到手机:', this.data.phoneNumber);

    // 调用API发送验证码
    userApi.sendVerifyCode(this.data.phoneNumber)
      .then(res => {
        wx.hideLoading();
        console.log('验证码发送结果:', res);

        if (res.success) {
          // 使用服务器返回的验证码（仅用于测试环境）
          let randomCode = '';

          if (res.data && res.data.code) {
            // 如果服务器返回了验证码，使用服务器的验证码
            randomCode = res.data.code;
            console.log('使用服务器返回的验证码:', randomCode);
          } else {
            // 否则生成本地验证码
            randomCode = this.generateRandomCode();
            console.log('使用本地生成的验证码:', randomCode);
          }

          this.setData({
            randomCode: randomCode
          });

          // 显示验证码（仅用于测试）
          wx.showModal({
            title: '验证码',
            content: `您的验证码是：${randomCode}`,
            showCancel: false,
            success: () => {
              wx.hideLoading();
            }
          });

          // 开始倒计时
          this.startCountdown();
        } else {
          wx.showToast({
            title: res.message || '发送验证码失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('发送验证码失败:', err);

        // 如果API调用失败，使用本地生成的验证码
        const randomCode = this.generateRandomCode();
        console.log('API调用失败，使用本地生成的验证码:', randomCode);

        this.setData({
          randomCode: randomCode
        });

        // 显示随机验证码
        wx.showModal({
          title: '验证码（本地生成）',
          content: `您的验证码是：${randomCode}`,
          showCancel: false,
          success: () => {
            wx.hideLoading();
          }
        });

        // 开始倒计时
        this.startCountdown();
      });
  },

  // 生成6位随机验证码
  generateRandomCode: function() {
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += Math.floor(Math.random() * 10);
    }
    return code;
  },

  // 生成10位用户ID（以8开头，后面9位随机数）
  generateUserId: function() {
    // 已生成的用户ID集合，用于防止重复
    if (!getApp().globalData.userIdSet) {
      getApp().globalData.userIdSet = new Set();
    }

    let userId;
    do {
      // 生成9位随机数
      let randomPart = '';
      for (let i = 0; i < 9; i++) {
        randomPart += Math.floor(Math.random() * 10);
      }

      // 组合成10位用户ID（以8开头）
      userId = '8' + randomPart;
    } while (getApp().globalData.userIdSet.has(userId)); // 如果ID已存在，重新生成

    // 将新生成的ID添加到集合中
    getApp().globalData.userIdSet.add(userId);

    return userId;
  },

  // 开始倒计时
  startCountdown: function() {
    let countdown = 60;
    this.setData({
      countdown: countdown,
      canGetCode: false
    });

    const timer = setInterval(() => {
      countdown--;
      this.setData({
        countdown: countdown
      });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canGetCode: this.isValidPhoneNumber(this.data.phoneNumber)
        });
      }
    }, 1000);
  },

  // 切换协议同意状态
  toggleAgreement: function() {
    this.setData({
      isAgreement: !this.data.isAgreement
    });
    this.checkCanLogin();
  },

  // 登录
  login: function() {
    if (!this.data.canLogin) {
      return;
    }

    const { loginType, phoneNumber, verifyCode, username, password } = this.data;

    wx.showLoading({
      title: '登录中...',
    });

    if (loginType === 'phone') {
      // 手机号登录
      console.log('开始手机号登录流程，手机号:', phoneNumber, '验证码:', verifyCode);

      // 验证验证码是否正确
      // 如果验证码是123456，则始终通过验证（用于测试）
      console.log('验证码验证: 输入=', verifyCode, '随机码=', this.data.randomCode);
      if (verifyCode === this.data.randomCode || verifyCode === '123456') {
        // 使用API登录
        userApi.loginByPhone(phoneNumber, verifyCode)
          .then(res => {
            this.handleLoginResponse(res);
          })
          .catch(err => {
            this.handleLoginError(err);
          });
      } else {
        wx.hideLoading();

        // 验证码错误
        wx.showToast({
          title: '验证码错误',
          icon: 'none'
        });
      }
    } else if (loginType === 'account') {
      // 账号密码登录
      console.log('开始账号密码登录流程，用户名:', username);

      // 使用API登录
      userApi.loginByAccount(username, password)
        .then(res => {
          console.log('账号密码登录响应:', res);
          this.handleLoginResponse(res);
        })
        .catch(err => {
          // 如果API调用失败，使用模拟登录
          console.error('账号密码登录失败:', err);

          // 显示错误信息
          // 显示登录失败提示
          this.handleLoginError(err);
        });
    }
  },

  // 处理登录响应
  handleLoginResponse: function(res) {
    wx.hideLoading();
    console.log('登录响应:', res);

    if (res.success) {
      // 保存用户信息 - 确保使用正确的字段名
      const userInfo = res.data.user || res.data.userInfo;
      const token = res.data.token;

      if (!userInfo || !token) {
        console.error('登录响应中缺少用户信息或令牌');
        wx.showToast({
          title: '登录响应格式错误',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 确保用户信息中有id字段
      if (!userInfo.id && userInfo._id) {
        console.log('用户信息中使用_id替代id，正在规范化');
        userInfo.id = userInfo._id;
      }

      // 使用登录状态管理器保存登录状态
      const loginStateManager = require('../../utils/login-state-manager');
      const saveResult = loginStateManager.saveLoginState(userInfo, token, true);
      console.log('保存登录状态结果:', saveResult);

      if (!saveResult) {
        console.error('保存登录状态失败');
        wx.showToast({
          title: '保存登录状态失败，请重试',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      console.log('保存的用户信息:', userInfo);

      // 更新全局数据
      const app = getApp();
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      app.globalData.needRefreshProfile = true;

      // 登录成功后，立即toast并跳转，不等待getUserInfo
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1000,
        success: () => {
          setTimeout(() => {
            const redirectUrl = decodeURIComponent(this.redirectUrl);
            if (redirectUrl) {
              wx.redirectTo({
                url: redirectUrl,
                fail: (err) => {
                  wx.reLaunch({
                    url: redirectUrl,
                    fail: () => {
                      wx.showToast({ title: '跳转失败，请手动返回', icon: 'none', duration: 2500 });
                    }
                  });
                }
              });
            } else {
              this.navigateBackToProfile();
            }
          }, 1000);
        }
      });

      // 异步获取最新用户信息，不影响跳转
      const userInfoFromStorage = wx.getStorageSync('userInfo');
      userApi.getUserInfo(userInfoFromStorage && userInfoFromStorage.id)
        .then(infoRes => {
          if (infoRes.success) {
            console.log('登录后获取最新用户信息成功:', infoRes.data);
            if (!infoRes.data.id && infoRes.data._id) {
              infoRes.data.id = infoRes.data._id;
            }
            loginStateManager.saveLoginState(infoRes.data, token, true);
            app.globalData.userInfo = infoRes.data;
            app.globalData.isLogin = true;
            app.globalData.needRefreshProfile = true;
          }
        })
        .catch(err => {
          console.error('登录后获取用户信息失败:', err);
        });
    } else {
      // 登录失败，显示提示语，2秒后自动消失，并清空验证码输入框
      wx.showToast({
        title: res.message || '登录失败，请重试',
        icon: 'none',
        duration: 2500
      });
      this.setData({ verifyCode: '' });
    }
  },

  // 处理登录错误
  handleLoginError: function(err) {
    wx.hideLoading();
    console.error('登录失败:', err);

    // 显示提示语，2秒后自动消失，并清空验证码输入框
    wx.showToast({
      title: (err && err.data && err.data.message) || '网络错误，请重试',
      icon: 'none',
      duration: 2500
    });
    this.setData({ verifyCode: '' });
  },

  // 微信登录
  wxLogin: function(e) {
    console.log('微信登录事件触发', e);

    wx.showLoading({
      title: '登录中...',
    });

    // 调用微信登录接口获取code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('微信登录成功，code:', loginRes.code);

          // 使用API登录
          userApi.loginByWechat(loginRes.code)
            .then(res => {
              wx.hideLoading();

              if (res.success) {
                // 保存token
                wx.setStorage({
                  key: 'token',
                  data: res.data.token
                });

                // 保存用户信息 - 确保使用正确的字段名
                const userInfo = res.data.user || res.data.userInfo;

                wx.setStorage({
                  key: 'userInfo',
                  data: userInfo
                });

                // 更新全局数据
                const app = getApp();
                app.globalData.userInfo = userInfo;
                app.globalData.isLogin = true;

                console.log('微信登录保存的用户信息:', userInfo);

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '登录成功',
                  icon: 'success',
                  success: () => {
                    // 登录成功后返回到个人中心页面
                    setTimeout(() => {
                      // 使用专门的方法返回到个人中心页面，确保刷新状态
                      this.navigateBackToProfile();
                    }, 1000); // 延迟1秒，让用户看到成功提示
                  }
                });
              } else {
                // 登录失败，显示提示语，2秒后自动消失
                wx.showToast({
                  title: res.message || '微信登录失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('微信登录失败:', err);

              // 显示提示语，2秒后自动消失
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none',
                duration: 2000
              });
            });
        } else {
          console.error('获取微信code失败', loginRes);
          wx.hideLoading();

          // 显示提示语，2秒后自动消失
          wx.showToast({
            title: '获取授权失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error('微信登录接口调用失败', err);
        wx.hideLoading();

        // 显示提示语，2秒后自动消失
        wx.showToast({
          title: '接口调用失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 使用默认的微信用户信息（模拟数据）- 已禁用，强制使用数据库验证
  useDefaultWxUserInfo: function() {
    console.warn('模拟微信登录已禁用，必须使用数据库验证');

    // 显示提示语，2秒后自动消失
    wx.showToast({
      title: '必须使用数据库验证登录',
      icon: 'none',
      duration: 2000
    });
  },

  // 使用本地模拟数据 - 已禁用，强制使用数据库验证
  useLocalMockData: function(userType = 'wx') {
    console.warn('本地模拟登录已禁用，必须使用数据库验证');

    // 显示提示语，2秒后自动消失
    wx.showToast({
      title: '必须使用数据库验证登录',
      icon: 'none',
      duration: 2000
    });
  },

  // 返回到个人中心页面
  navigateBackToProfile: function() {
    const app = getApp(); // 修正：使用正确的方式获取应用实例

    // 确保全局登录状态正确设置
    if (app.globalData.userInfo) {
      app.globalData.isLogin = true;
    }

    // 标记需要刷新个人中心页面
    app.globalData.needRefreshProfile = true;

    console.log('准备返回个人中心页面，当前登录状态:', app.globalData.isLogin);
    console.log('当前用户信息:', app.globalData.userInfo);

    // 确保登录状态已保存到本地存储
    const loginStateManager = require('../../utils/login-state-manager');
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    // 如果本地存储中没有登录状态，但全局状态中有，则保存到本地存储
    if ((!loginState || !loginState.isLogin) && app.globalData.userInfo && token) {
      console.log('本地存储中没有登录状态，但全局状态中有，保存到本地存储');
      loginStateManager.saveLoginState(app.globalData.userInfo, token, true);
    }

    // 不再进行验证，直接返回，避免网络问题导致登录状态丢失
    console.log('直接返回个人中心页面，不再验证登录状态');

    // 返回到上一页（通常是个人中心页面）
    wx.navigateBack({
      success: function() {
        console.log('成功返回到个人中心页面');
      },
      fail: function(err) {
        console.error('返回个人中心页面失败:', err);
        // 如果返回失败（可能是从分享链接直接进入登录页），则跳转到个人中心页面
        wx.switchTab({
          url: '/pages/profile/profile'
        });
      }
    });
  },

  // 保留原方法以兼容可能的其他调用
  navigateToHomeAndRefresh: function() {
    console.warn('已弃用的方法: navigateToHomeAndRefresh，改为返回个人中心页面');
    this.navigateBackToProfile();
  },

  // 查看用户协议
  viewUserAgreement: function() {
    wx.navigateTo({
      url: '/pages/agreement/user'
    });
  },

  // 查看隐私政策
  viewPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/agreement/privacy'
    });
  },

  // 跳转到重置密码页面
  navigateToResetPassword: function() {
    wx.navigateTo({
      url: '/pages/auth/reset-password'
    });
  }
});

/* pages/points/points.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading {
  font-size: 16px;
  color: #666;
}

.points-center-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  min-height: 100vh;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 24rpx 24rpx;
  border-bottom: 2rpx solid #e5e5e5;
  background: #f9f9f9;
}
.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: #17637a;
  margin-right: 32rpx;
}
.user-details {
  flex: 1;
}
.nickname {
  font-size: 38rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.user-id {
  font-size: 26rpx;
  color: #666;
}
.points-block {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.points-label {
  font-size: 28rpx;
  color: #333;
}
.points-value {
  font-size: 44rpx;
  font-weight: bold;
  color: #222;
}

/* 轮播图 */
.banner-section {
  padding: 0 15px 10px;
  background-color: #FFFFFF;
  margin-top: 10px;
  margin-bottom: 0;
  position: relative;
}

.banner-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  height: 2rpx;
  background: linear-gradient(to right, transparent, rgba(30, 106, 158, 0.1), transparent);
}

.banner-swiper {
  width: 100%;
  height: 140px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateZ(0);
}

.banner-swiper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 2;
  pointer-events: none;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  display: block; /* 确保图片正确显示 */
  transform: scale(1.02);
  transition: transform 0.5s ease;
}

.wx-swiper-dot {
  width: 12rpx !important;
  height: 12rpx !important;
  border-radius: 6rpx !important;
  margin-left: 8rpx !important;
  margin-right: 8rpx !important;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
  color: #FFFFFF;
  font-size: 15px;
  line-height: 1.4;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
  z-index: 3;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.rules-section {
  display: flex;
  align-items: flex-start;
  padding: 32rpx 24rpx;
  font-size: 32rpx;
  color: #222;
  justify-content: space-between;
}
.rule-title {
  font-weight: 500;
  margin-bottom: 8rpx;
}
.rule-content {
  font-size: 28rpx;
  color: #444;
  margin-bottom: 16rpx;
}
.divider {
  font-size: 36rpx;
  color: #888;
  margin: 0 24rpx;
  align-self: center;
}

.rules-tabs {
  display: flex;
  justify-content: center;
  margin: 32rpx 24rpx 0 24rpx;
}
.tab-btn {
  flex: 1;
  text-align: center;
  padding: 18rpx 0;
  font-size: 32rpx;
  color: #888;
  background: #f5f5f5;
  border-radius: 16rpx 16rpx 0 0;
  margin-right: 8rpx;
  transition: background 0.2s, color 0.2s;
}
.tab-btn:last-child {
  margin-right: 0;
}
.tab-btn.active {
  color: #17637a;
  background: #fff;
  font-weight: bold;
  box-shadow: 0 -2rpx 8rpx rgba(23,99,122,0.08);
}
.rules-card {
  background: #fff;
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(23,99,122,0.06);
  padding: 36rpx 32rpx;
  font-size: 30rpx;
  color: #333;
  min-height: 120rpx;
  margin: 0 24rpx 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-top: none;
}
.rules-divider {
  height: 2rpx;
  background: #e5e5e5;
  margin: 0 24rpx 32rpx 24rpx;
  border-radius: 2rpx;
}

.points-swiper {
  width: 100%;
  height: 200rpx;
  min-height: 200rpx;
  background: #e5e5e5;
}

.points-record-section {
  margin: 32rpx 24rpx 0 24rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(23,99,122,0.06);
}
.record-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 18rpx;
  color: #222;
}
.record-item {
  border-bottom: 1rpx solid #e5e5e5;
  padding: 18rpx 0 8rpx 0;
}
.record-item:last-child {
  border-bottom: none;
}
.record-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.record-time {
  font-size: 26rpx;
  color: #888;
  flex: 1.5;
}
.record-event {
  font-size: 28rpx;
  color: #333;
  flex: 2;
  text-align: left;
  margin-left: 16rpx;
}
.record-change {
  font-size: 32rpx;
  font-weight: bold;
  flex: 1;
  text-align: right;
}
.record-change.minus {
  color: #FF4D4F;
}
.record-change.plus {
  color: #52C41A;
}
.record-balance {
  font-size: 26rpx;
  color: #666;
  text-align: right;
  margin-top: 4rpx;
}
.record-empty {
  color: #aaa;
  font-size: 28rpx;
  text-align: center;
  padding: 32rpx 0;
}

.loading-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E6A9E;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

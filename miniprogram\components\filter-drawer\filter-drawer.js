// components/filter-drawer/filter-drawer.js
Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 接收外部传入的初始筛选参数
    initialFilter: {
      type: Object,
      value: null,
      observer: function(newVal) {
        console.log('接收到筛选参数:', newVal);
        if (newVal) {
          this._initializeFilterData(newVal);
        }
      }
    }
  },

  data: {
    // 主题数据 - 与发布页保持一致
    topics: [
      { id: 1, name: '企业服务', selected: false },
      { id: 2, name: '工商注册', selected: false },
      { id: 3, name: '税务合规', selected: false },
      { id: 4, name: '企业转让', selected: false },
      { id: 5, name: '地址托管', selected: false },
      { id: 6, name: '疑难业务', selected: false },
      { id: 7, name: '企业信用', selected: false },
      { id: 8, name: '异常处理', selected: false },
      { id: 9, name: '资质代办', selected: false },
      { id: 10, name: '知识产权', selected: false },
      { id: 11, name: '高新技术', selected: false },
      { id: 12, name: '老板圈层', selected: false }
    ],
    // 区域选择
    region: ['全部', '全部', '全部'],
    regionText: '请选择地区'
  },

  lifetimes: {
    attached: function() {
      // 组件创建时，尝试加载之前保存的筛选条件
      try {
        const filterParams = wx.getStorageSync('filterParams');
        if (filterParams) {
          this._initializeFilterData(filterParams);
        }
      } catch (error) {
        console.error('加载筛选参数出错:', error);
      }
    }
  },

  methods: {
    // 初始化筛选数据
    _initializeFilterData: function(filterParams) {
      try {
        // 恢复区域选择
        if (filterParams.region && Array.isArray(filterParams.region) && filterParams.region.length === 3) {
          this.setData({
            region: filterParams.region,
            regionText: this.formatRegionText(filterParams.region)
          });
        }

        // 恢复主题选择
        const selectedTopicIds = filterParams.selectedTopicIds || filterParams.topics || [];
        if (Array.isArray(selectedTopicIds) && selectedTopicIds.length > 0) {
          const topics = this.data.topics.map(topic => {
            return {
              ...topic,
              selected: selectedTopicIds.includes(topic.id)
            };
          });

          this.setData({ topics });
        }
      } catch (error) {
        console.error('初始化筛选参数出错:', error);
      }
    },

    // 防止滑动穿透
    preventTouchMove: function() {
      return false;
    },

    // 允许抽屉内容区域滚动
    catchTouchMove: function(e) {
      // 什么都不做，但阻止事件冒泡
    },

    // 区域选择器变化
    bindRegionChange: function(e) {
      const region = e.detail.value;
      this.setData({
        region: region,
        regionText: this.formatRegionText(region)
      });
    },

    // 格式化区域文本
    formatRegionText: function(region) {
      if (region[0] === '全部') {
        return '全国';
      } else if (region[1] === '全部') {
        return region[0];
      } else if (region[2] === '全部') {
        return region[0] + ' ' + region[1];
      } else {
        return region[0] + ' ' + region[1] + ' ' + region[2];
      }
    },

    // 切换主题选择
    toggleTopic: function(e) {
      const topicId = parseInt(e.currentTarget.dataset.id);

      const topics = this.data.topics.map(topic => {
        if (topic.id === topicId) {
          return {
            ...topic,
            selected: !topic.selected
          };
        }
        return topic;
      });

      this.setData({ topics });
    },

    // 重置筛选条件
    resetFilter: function() {
      const topics = this.data.topics.map(topic => {
        return {
          ...topic,
          selected: false
        };
      });

      this.setData({
        region: ['全部', '全部', '全部'],
        regionText: '请选择地区',
        topics: topics
      });
      
      // 震动反馈
      wx.vibrateShort({
        type: 'light'
      });
    },

    // 取消筛选
    cancelFilter: function() {
      // 触发关闭事件
      this.triggerEvent('close');
    },

    // 确认筛选
    confirmFilter: function() {
      try {
        // 获取选中的主题ID
        const selectedTopicIds = this.data.topics
          .filter(topic => topic.selected)
          .map(topic => topic.id);
        
        // 获取选中的主题名称（用于日志和显示）
        const selectedTopicNames = this.data.topics
          .filter(topic => topic.selected)
          .map(topic => topic.name);
        
        // 重要：获取主题名称字符串，确保使用名称而不仅仅是ID
        const topicNamesString = selectedTopicNames.join(',');

        // 简化日志输出
        console.log('Processing filter options');
        
        // 检查地区选择状态
        const hasRegionSelected = this.data.region[0] !== '全部';
        
        // 将选中的主题ID转换为逗号分隔的字符串
        const topicsIdString = selectedTopicIds.join(',');
        // 将选中的主题名称转换为逗号分隔的字符串（重要：确保传递主题名称）
        const topicsNameString = selectedTopicNames.join(',');
        
        // 判断是否两者都不选
        const noTopicSelected = selectedTopicIds.length === 0;
        const noRegionSelected = !hasRegionSelected;
        const noFilterSelected = noTopicSelected && noRegionSelected;
        
        // 构建筛选参数
        const filterParams = {
          // 基本信息
          region: this.data.region,
          regionText: this.data.regionText,
          selectedTopicIds: selectedTopicIds,
          selectedTopicNames: selectedTopicNames,
          
          // 同时传递主题ID和主题名称，确保后端能正确处理所有主题
          topicIds: topicsIdString,
          topics: topicsNameString,
          // 明确传递主题名称数组，确保所有主题类型都能被正确处理
          topicNames: selectedTopicNames,
          
          // 筛选关系设置
          topicFilterType: 'OR',  // 主题是"或"关系
          topicAndRegionRelation: 'AND',  // 主题与地区是"与"关系
          
          // 地区筛选标记 - 只在选择了具体地区时才设置
          useRegionFilter: hasRegionSelected,
          
          // 特别标记：当地区和主题都不选时，设置不筛选标志
          noFilterSelected: noFilterSelected
        };
        
        // 日志输出详细筛选状态
        if (selectedTopicIds.length > 0) {
          console.log('已选主题：', selectedTopicNames.join(','));
        } else {
          console.log('未选择主题，不进行主题筛选');
        }
        
        if (hasRegionSelected) {
          console.log('已选地区：', this.data.regionText, '，启用地区筛选');
        } else {
          console.log('未选择具体地区，不限制地区');
        }

        // 保存筛选参数到本地存储
        wx.setStorageSync('filterParams', filterParams);

        console.log('筛选组件发送参数:', JSON.stringify(filterParams));

        // 触发确认事件，将筛选参数传递给父组件
        this.triggerEvent('confirm', { filterParams });
        
        // 压力反馈
        wx.vibrateShort({
          type: 'light'
        });
      } catch (error) {
        console.error('保存筛选参数出错:', error);
        wx.showToast({
          title: '筛选应用失败，请重试',
          icon: 'none'
        });
      }
    }
  }
});

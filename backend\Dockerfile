FROM node:20-alpine3.18

# 安装Python和编译工具
RUN apk add --no-cache python3 make g++ gcc netcat-openbsd

# 创建工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/uploads /app/public/qrcode /app/__tmp__

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖前切换npm源为淘宝镜像，加速安装
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖
RUN npm install --production

# 复制所有文件
COPY . .

# 设置文件权限
RUN chmod +x /app/cert/initenv.sh && \
    chmod 777 /app/uploads /app/public/qrcode /app/__tmp__ && \
    dos2unix /app/cert/initenv.sh

# 删除不需要的文件
RUN rm -f wait-for-it.sh Dockerfile.cloud generate-cert.js && \
    rm -rf test docs temp __tmp__ && \
    rm -f config/db-backup.js config/db-init.js config/db-seed.js config/db-validate.js

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3001 \
    DB_HOST=localhost \
    DB_PORT=3306

# 确保脚本使用 LF 行尾
RUN sed -i 's/\r$//' /app/cert/initenv.sh

# 暴露端口
EXPOSE 3001

# 启动命令
CMD ["node", "server.js"]

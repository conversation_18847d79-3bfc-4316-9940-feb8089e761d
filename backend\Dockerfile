# 使用官方 Node.js 镜像作为构建阶段
FROM node:20.11.1-slim@sha256:c20e4e0b5ae9c75a8db1d7a13f2c77670c3036019825de5ca863314c7310c7b2 AS builder

# 创建工作目录
WORKDIR /app

# 只复制package文件进行依赖安装
COPY package*.json ./
RUN npm config set registry https://registry.npmmirror.com && \
    npm ci --only=production

# 使用相同的基础镜像作为运行时
FROM node:20.11.1-slim@sha256:c20e4e0b5ae9c75a8db1d7a13f2c77670c3036019825de5ca863314c7310c7b2

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3-minimal \
    dos2unix \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3001 \
    DISABLE_CERT_INIT=true

# 创建非root用户和必要的目录
RUN groupadd -r nodeapp && \
    useradd -r -g nodeapp -s /bin/false nodeapp && \
    mkdir -p /app/uploads /app/public/qrcode /app/__tmp__ /app/cert && \
    chown -R nodeapp:nodeapp /app

# 从builder阶段复制node_modules
COPY --chown=nodeapp:nodeapp --from=builder /app/node_modules ./node_modules

# 复制应用代码
COPY --chown=nodeapp:nodeapp . .

# 设置文件权限和行尾格式
RUN chmod +x /app/cert/initenv.sh && \
    chmod +x /app/cert/app-init.sh && \
    chmod 777 /app/uploads /app/public/qrcode /app/__tmp__ && \
    dos2unix /app/cert/initenv.sh && \
    dos2unix /app/cert/app-init.sh && \
    touch /app/cert/certificate.crt && \
    touch /app/cert.log && \
    chmod 644 /app/cert/certificate.crt /app/cert.log && \
    # 删除不需要的文件
    rm -f wait-for-it.sh Dockerfile.cloud generate-cert.js && \
    rm -rf test docs temp __tmp__ && \
    rm -f config/db-backup.js config/db-init.js config/db-seed.js config/db-validate.js

# 切换到非root用户
USER nodeapp

# 暴露端口
EXPOSE 3001

# 启动命令
CMD ["node", "server.js"]

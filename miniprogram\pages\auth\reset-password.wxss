/* pages/auth/reset-password.wxss */
.reset-container {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 60rpx;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 0;
  top: 0;
  padding: 10rpx;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.input-area {
  margin-bottom: 40rpx;
}

.input-group {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
  position: relative;
}

.input-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  font-size: 30rpx;
}

.verify-btn {
  position: absolute;
  right: 0;
  background-color: #f5f5f5;
  color: #999;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

.verify-btn.active {
  background-color: #FF4D4F;
  color: #fff;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 60rpx;
  padding: 0 20rpx;
}

.submit-btn {
  background-color: #f5f5f5;
  color: #999;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 40rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}

.submit-btn.active {
  background-color: #FF4D4F;
  color: #fff;
}

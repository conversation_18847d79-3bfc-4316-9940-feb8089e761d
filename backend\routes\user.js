const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middleware/auth');

// 关注用户
router.post('/follow', auth, userController.followUser);

// 取消关注
router.post('/unfollow', auth, userController.unfollowUser);

// 检查关注状态
router.get('/follow/status', auth, userController.checkFollowStatus);

module.exports = router; 
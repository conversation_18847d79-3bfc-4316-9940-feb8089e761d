/**
 * 中间件配置
 */
const cors = require('cors');
const morgan = require('morgan');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const compression = require('compression');

module.exports = (app) => {
  // CORS配置
  app.use(cors({
    origin: '*', // 允许所有来源的请求
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }));

  // 日志中间件
  app.use(morgan('dev'));

  // 安全中间件
  app.use(helmet());

  // 压缩中间件
  app.use(compression());

  // 请求体解析
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));
};

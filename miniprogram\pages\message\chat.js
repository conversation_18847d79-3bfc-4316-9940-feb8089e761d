const { messageApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    targetUserId: '',
    targetUser: {},
    userInfo: {},
    messages: [],
    inputMessage: '',
    scrollToView: '',
    page: 1,
    pageSize: 20,
    hasMore: true
  },

  onLoad: function(options) {
    console.log('聊天页面参数:', options);
    // 从options中获取targetId
    const targetUserId = options.targetId;

    if (!targetUserId) {
      wx.showToast({
        title: '缺少聊天对象ID',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    this.setData({
      targetUserId,
      userInfo: app.globalData.userInfo || {}
    });

    console.log('当前用户:', this.data.userInfo);
    console.log('目标用户ID:', targetUserId);

    this.loadTargetUserInfo(targetUserId);
    this.loadMessages();

    // 启动定时刷新
    this.startAutoRefresh();
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    this.stopAutoRefresh();
  },

  onHide: function() {
    // 页面隐藏时清除定时器
    this.stopAutoRefresh();
  },

  onShow: function() {
    // 页面显示时重新启动定时器
    this.startAutoRefresh();
  },

  // 启动定时刷新
  startAutoRefresh: function() {
    console.log('启动定时刷新');
    // 清除可能存在的旧定时器
    this.stopAutoRefresh();

    // 设置新的定时器，每5秒刷新一次
    this.refreshTimer = setInterval(() => {
      console.log('定时刷新消息');
      // 重置页码，只获取最新消息
      this.setData({ page: 1 });
      this.loadMessages();
    }, 5000); // 5秒刷新一次
  },

  // 停止定时刷新
  stopAutoRefresh: function() {
    console.log('停止定时刷新');
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  },

  loadTargetUserInfo: function(targetUserId) {
    console.log('加载目标用户信息:', targetUserId);

    // 使用API获取用户信息
    const { userApi } = require('../../utils/api');
    userApi.getUserInfo(targetUserId).then(res => {
      console.log('获取用户信息结果:', res);
      if (res.success && res.data) {
        // 处理用户信息
        this.setData({
          targetUser: res.data
        });
        // 设置导航栏标题为对方昵称
        wx.setNavigationBarTitle({
          title: res.data.nickname || '聊天'
        });
      } else {
        // 如果API请求失败，使用默认值
        const defaultUser = {
          id: targetUserId,
          nickName: '用户' + targetUserId.substring(0, 4),
          avatarUrl: '/images/icons2/男头像.png'
        };
        console.log('使用默认用户信息:', defaultUser);
        this.setData({ targetUser: defaultUser });

        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      console.error('获取用户信息出错:', err);
      // 使用默认值
      const defaultUser = {
        id: targetUserId,
        nickName: '用户' + targetUserId.substring(0, 4),
        avatarUrl: '/images/icons2/男头像.png'
      };
      this.setData({ targetUser: defaultUser });
    });
  },

  loadMessages: function() {
    const { targetUserId, page, pageSize } = this.data;
    console.log('加载消息:', targetUserId, page, pageSize);

    // 判断是否是自动刷新
    const isAutoRefresh = page === 1 && this.data.messages.length > 0;
    if (isAutoRefresh) {
      console.log('这是自动刷新，将只添加新消息');
    }

    // 使用API获取消息
    const { messageApi } = require('../../utils/api');
    console.log('调用messageApi.getMessages，参数:', { targetUserId, page, pageSize });
    messageApi.getMessages({ targetUserId, page, pageSize }).then(res => {
      console.log('获取消息结果:', res);
      if (res.success && res.data) {
        // 处理消息数据
        const messages = Array.isArray(res.data) ? res.data : (res.data.list || []);

        // 格式化消息
        const formattedMessages = messages.map(msg => ({
          id: msg.id,
          senderId: msg.senderId,
          receiverId: msg.receiverId,
          content: msg.content,
          type: msg.type || 'text',
          createTime: msg.createTime,
          isRead: msg.isRead === 1,
          displayTime: this.formatTime(msg.createTime)
        }));

        // 只保留当前用户与目标用户之间的消息
        const userId = this.data.userInfo && this.data.userInfo.id;
        const targetUserId = this.data.targetUserId;
        const filteredMessages = formattedMessages.filter(msg =>
          (String(msg.senderId) === String(userId) && String(msg.receiverId) === String(targetUserId)) ||
          (String(msg.senderId) === String(targetUserId) && String(msg.receiverId) === String(userId))
        );

        // 按照时间顺序排序，最早的消息在前面，最新的消息在后面
        filteredMessages.sort((a, b) => a.createTime - b.createTime);

        console.log('过滤后的消息:', filteredMessages);

        if (isAutoRefresh) {
          // 自动刷新时，只添加新消息
          // 找出当前消息列表中不存在的新消息
          const existingIds = this.data.messages.map(msg => msg.id);
          const newMessages = filteredMessages.filter(msg => !existingIds.includes(msg.id));

          if (newMessages.length > 0) {
            console.log('发现新消息:', newMessages.length, '条');

            // 将新消息添加到列表末尾
            const updatedMessages = [...this.data.messages, ...newMessages];

            // 按时间排序，确保最早的消息在前面，最新的消息在后面
            updatedMessages.sort((a, b) => a.createTime - b.createTime);

            this.setData({
              messages: updatedMessages,
              hasMore: filteredMessages.length === pageSize
            });

            // 滚动到最新消息
            if (newMessages.length > 0) {
              const lastMsgId = updatedMessages[updatedMessages.length - 1].id;
              this.setData({
                scrollToView: `msg-${lastMsgId}`
              });
            }
          } else {
            console.log('没有新消息');
          }
        } else {
          // 首次加载或加载更多时，直接替换或追加消息
          this.setData({
            messages: filteredMessages,
            hasMore: filteredMessages.length === pageSize
          });
        }
      } else {
        console.warn('获取消息失败或消息为空');
        if (page === 1 && !isAutoRefresh) {
          this.setData({ messages: [] });

          wx.showToast({
            title: res.message || '获取消息失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    }).catch(err => {
      console.error('获取消息出错:', err);
      if (page === 1 && !isAutoRefresh) {
        this.setData({ messages: [] });

        wx.showToast({
          title: '获取消息失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  loadMoreMessages: function() {
    if (!this.data.hasMore) {
      console.log('没有更多消息了');
      return;
    }

    console.log('加载更多消息');
    wx.showLoading({
      title: '加载更多...',
      mask: true
    });

    this.setData({
      page: this.data.page + 1
    }, () => {
      this.loadMessages();
      wx.hideLoading();
    });
  },

  onInput: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  sendMessage: function() {
    const { inputMessage, targetUserId } = this.data;
    if (!inputMessage.trim()) return;

    console.log('发送消息:', targetUserId, inputMessage);

    // 显示发送中提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 使用API发送消息
    const { messageApi } = require('../../utils/api');
    console.log('调用messageApi.sendMessage，参数:', { targetUserId, content: inputMessage.trim(), type: 'text' });
    messageApi.sendMessage(targetUserId, inputMessage.trim(), 'text').then(res => {
      wx.hideLoading();
      console.log('发送消息结果:', res);

      if (res.success && res.data) {
        // 构建新消息对象
        const newMessage = {
          id: res.data.id,
          senderId: this.data.userInfo.id,
          receiverId: targetUserId,
          content: inputMessage.trim(),
          type: 'text',
          createTime: res.data.createTime || Date.now(),
          isRead: false,
          displayTime: this.formatTime(res.data.createTime || Date.now())
        };

        console.log('添加新消息:', newMessage);

        // 更新消息列表和清空输入框
        // 新消息添加到末尾，符合时间顺序（最新的消息在最下面）
        this.setData({
          messages: [...this.data.messages, newMessage],
          inputMessage: '',
          scrollToView: `msg-${newMessage.id}`
        });
      } else {
        wx.showToast({
          title: res.message || '发送失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('发送消息出错:', err);

      wx.showToast({
        title: '发送失败',
        icon: 'none',
        duration: 2000
      });
    });
  },

  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImage(tempFilePath);
      }
    });
  },

  uploadImage: function(filePath) {
    console.log('上传图片:', filePath);

    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    // 使用API上传图片
    const { uploadFile } = require('../../utils/api');
    uploadFile(filePath).then(url => {
      console.log('上传图片成功:', url);
      wx.hideLoading();

      // 发送图片消息
      this.sendImageMessage(url);
    }).catch(err => {
      wx.hideLoading();
      console.error('上传图片失败:', err);

      wx.showToast({
        title: '上传图片失败',
        icon: 'none',
        duration: 2000
      });
    });
  },

  sendImageMessage: function(imageUrl) {
    const { targetUserId } = this.data;
    console.log('发送图片消息:', targetUserId, imageUrl);

    // 显示发送中提示
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 使用API发送图片消息
    const { messageApi } = require('../../utils/api');
    messageApi.sendMessage(targetUserId, imageUrl, 'image').then(res => {
      wx.hideLoading();
      console.log('发送图片消息结果:', res);

      if (res.success && res.data) {
        // 构建新消息对象
        const newMessage = {
          id: res.data.id,
          senderId: this.data.userInfo.id,
          receiverId: targetUserId,
          content: imageUrl,
          type: 'image',
          createTime: res.data.createTime || Date.now(),
          isRead: false,
          displayTime: this.formatTime(res.data.createTime || Date.now())
        };

        console.log('添加新图片消息:', newMessage);

        // 更新消息列表
        // 新消息添加到末尾，符合时间顺序（最新的消息在最下面）
        this.setData({
          messages: [...this.data.messages, newMessage],
          scrollToView: `msg-${newMessage.id}`
        });
      } else {
        wx.showToast({
          title: res.message || '发送图片失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('发送图片消息出错:', err);

      wx.showToast({
        title: '发送图片失败',
        icon: 'none',
        duration: 2000
      });
    });
  },

  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      urls: [url],
      current: url
    });
  },

  formatTime: function(ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    const now = new Date();
    const diff = now - date;

    // 今天
    if (diff < 24 * 60 * 60 * 1000) {
      return date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    }
    // 昨天
    if (diff < 48 * 60 * 60 * 1000) {
      return '昨天 ' +
             date.getHours().toString().padStart(2, '0') + ':' +
             date.getMinutes().toString().padStart(2, '0');
    }
    // 更早
    return date.getFullYear() + '-' +
           (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
           date.getDate().toString().padStart(2, '0') + ' ' +
           date.getHours().toString().padStart(2, '0') + ':' +
           date.getMinutes().toString().padStart(2, '0');
  }
});
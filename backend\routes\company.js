/**
 * 公司信息路由
 */
const express = require('express');
const CompanyInfo = require('../models/CompanyInfo');
const { checkAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取公司信息（无参数版本）
 * GET /api/company/info
 */
router.get('/info', async (req, res) => {
  try {
    // 获取第一条记录
    const allCompanies = await CompanyInfo.findAll();
    const companyInfo = allCompanies && allCompanies.length > 0 ? allCompanies[0] : null;

    if (companyInfo) {
      res.json({
        success: true,
        data: companyInfo
      });
    } else {
      res.status(404).json({
        success: false,
        message: '暂无公司信息'
      });
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 获取公司信息（带ID版本）
 * GET /api/company/info/:id
 */
router.get('/info/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const companyInfo = await CompanyInfo.findById(id);

    if (companyInfo) {
      res.json({
        success: true,
        data: companyInfo
      });
    } else {
      res.status(404).json({
        success: false,
        message: '公司信息不存在'
      });
    }
  } catch (error) {
    console.error('获取公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 获取所有公司信息
 * GET /api/company/list
 */
router.get('/list', async (req, res) => {
  try {
    const companyList = await CompanyInfo.findAll();
    res.json({
      success: true,
      data: companyList
    });
  } catch (error) {
    console.error('获取公司信息列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 创建公司信息
 * POST /api/company/create
 * 需要管理员权限
 */
router.post('/create', checkAuth, async (req, res) => {
  try {
    const companyData = req.body;

    // 验证必填字段
    if (!companyData.company_name) {
      return res.status(400).json({
        success: false,
        message: '公司名称不能为空'
      });
    }

    const result = await CompanyInfo.create(companyData);
    res.json(result);
  } catch (error) {
    console.error('创建公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 更新公司信息
 * PUT /api/company/update/:id
 * 需要管理员权限
 */
router.put('/update/:id', checkAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const companyData = req.body;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '公司信息ID不能为空'
      });
    }

    const result = await CompanyInfo.update(id, companyData);
    res.json(result);
  } catch (error) {
    console.error('更新公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 删除公司信息
 * DELETE /api/company/delete/:id
 * 需要管理员权限
 */
router.delete('/delete/:id', checkAuth, async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '公司信息ID不能为空'
      });
    }

    const result = await CompanyInfo.delete(id);
    res.json(result);
  } catch (error) {
    console.error('删除公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 搜索公司信息
 * GET /api/company/search?name=关键词
 */
router.get('/search', async (req, res) => {
  try {
    const { name } = req.query;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    const results = await CompanyInfo.searchByName(name);
    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('搜索公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

module.exports = router;

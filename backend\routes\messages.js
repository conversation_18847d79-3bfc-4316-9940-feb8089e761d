/**
 * 消息路由
 */
const express = require('express');
const { body, query, param } = require('express-validator');
const messageController = require('../controllers/messageController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取最近聊天列表
router.get('/recent', checkAuth, messageController.getRecentChats);

// 获取未读消息数量
router.get('/unread/count', checkAuth, messageController.getUnreadCount);

// 获取消息列表
router.get('/', [
  checkAuth,
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
  query('type').optional().isString().withMessage('类型必须是字符串'),
  validate
], messageController.getMessages);

// 获取消息详情
router.get('/:id', [
  checkAuth,
  param('id').isString().withMessage('消息ID必须是字符串'),
  validate
], messageController.getMessageById);

// 创建消息
router.post('/', [
  body('userId').isString().withMessage('用户ID必须是字符串'),
  body('type').isString().withMessage('类型必须是字符串'),
  body('title').isString().withMessage('标题必须是字符串'),
  body('content').isString().withMessage('内容必须是字符串'),
  validate
], messageController.createMessage);

// 标记消息为已读
router.put('/:id/read', [
  checkAuth,
  param('id').isString().withMessage('消息ID必须是字符串'),
  validate
], messageController.markAsRead);

// 标记所有消息为已读
router.put('/read/all', [
  checkAuth,
  body('type').optional().isString().withMessage('类型必须是字符串'),
  validate
], messageController.markAllAsRead);

// 发送消息
router.post('/send', [
  checkAuth,
  body('receiverId').isString().withMessage('接收者ID必须是字符串'),
  body('content').isString().withMessage('内容必须是字符串'),
  body('type').optional().isString().withMessage('类型必须是字符串'),
  validate
], messageController.sendMessage);

module.exports = router;

/* pages/publish/publish.wxss */
.publish-container {
  min-height: 100vh;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* 移除自定义顶部导航栏相关样式 */

.publish-btn {
  padding: 6px 12px;
  background-color: #CCCCCC;
  color: #FFFFFF;
  font-size: 14px;
  border-radius: 4px;
}

.publish-btn.active {
  background-color: #FF4D4F;
}

.publish-btn.disabled {
  background-color: #CCCCCC;
  color: #FFFFFF;
}

/* 内容编辑区 */
.content-area {
  padding: 16px;
  position: relative;
}

.content-input {
  width: 100%;
  font-size: 16px;
  line-height: 1.5;
  color: #333333;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.word-count {
  position: absolute;
  right: 16px;
  bottom: 0;
  font-size: 12px;
  color: #999999;
}

.word-count-limit {
  color: #FF4D4F;
  font-weight: bold;
}

/* 媒体添加区 */
.media-area {
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 8px;
  margin: 0 16px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.media-area.media-area-tight {
  padding: 12px 16px 12px 16px;
  margin: 0 8px 16px 8px;
}

.media-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.media-row {
  display: flex;
  gap: 8px;
}

.media-upload-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.media-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  padding-left: 2px;
  padding-right: 2px;
  min-height: 20px;
}

.image-upload-section .media-upload-header,
.video-upload-section .media-upload-header {
  margin-bottom: 2px;
}

.media-upload-title {
  font-size: 13px;
  color: #333333;
  font-weight: bold;
}

.media-upload-desc {
  font-size: 11px;
  color: #999999;
}

.image-container-wrapper,
.large-video-container-wrapper {
  margin-top: 0;
}

.image-container-wrapper {
  width: 100%;
  height: 80px;
  min-height: 80px;
  max-height: 80px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 4px;
  box-sizing: border-box;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 50%; /* 缩小50% */
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.preview-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain，确保图片完全显示在容器内 */
  background-color: #f5f5f5; /* 添加背景色，使图片边界更清晰 */
}

.image-thumbnails {
  margin-top: 8px;
  height: 60px;
}

.thumbnails-scroll {
  white-space: nowrap;
  height: 100%;
}

.thumbnail-item {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  margin-right: 8px;
  border-radius: 6px;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain，确保图片完全显示在容器内 */
  background-color: #f5f5f5; /* 添加背景色，使图片边界更清晰 */
}

.thumbnail-delete {
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
}

.thumbnail-delete image {
  width: 16px;
  height: 16px;
}

.grid-image {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: contain; /* 确保图片完全显示在容器内 */
  background-color: #f5f5f5; /* 添加背景色，使图片边界更清晰 */
}

.delete-icon {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  z-index: 10;
  background-color: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon image {
  width: 20px;
  height: 20px;
}

.delete-icon:active {
  background-color: #F5F5F5;
}

.add-image-btn {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed #DDDDDD;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F9F9F9;
}

.add-image-btn image {
  width: 20px;
  height: 20px;
}

.video-container-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 50%; /* 缩小50% */
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 50%; /* 缩小50% */
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.video-player {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain，确保视频完全显示在容器内 */
  background-color: #000000; /* 添加黑色背景，视频播放更美观 */
}

.video-duration {
  position: absolute;
  right: 6px;
  bottom: 6px;
  padding: 1px 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 10px;
  border-radius: 2px;
}

.add-video-btn {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed #DDDDDD;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F9F9F9;
}

.add-video-btn image {
  width: 20px;
  height: 20px;
}

/* 主题标签 */
.topic-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between; /* 两端对齐，使标签均匀分布 */
  padding: 4px 16px; /* 增加左右内边距，与屏幕边距保持一致 */
  margin-top: 8px; /* 增加顶部间距 */
  margin-bottom: 8px; /* 增加底部间距 */
}

/* 添加一个伪元素，确保最后一行的标签也能均匀分布 */
.topic-tags::after {
  content: "";
  flex: auto;
  max-width: 30%;
  min-width: 30%;
  visibility: hidden;
}

.topic-tag {
  display: inline-block;
  padding: 6px 12px;
  margin: 4px;
  /* background-color: #F5F5F5; */
  border-radius: 16px;
  font-size: 14px;
  color: #666666;
  border: 1px solid #F0F0F0;
  transition: all 0.3s ease;
}

.topic-tag.active {
  background-color: #333333 !important;
  color: #FFFFFF !important;
  border: 1px solid #333333 !important;
  font-size: 14px !important;
  box-shadow: none !important;
}

.topic-selected-count {
  margin-top: 12px;
  padding: 0 16px;
  font-size: 14px;
  color: #999999;
  text-align: right;
}

/* 地区选择器 */
.picker-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  background-color: #F5F5F5;
  border-radius: 8px;
}

.picker-arrow {
  width: 16px;
  height: 16px;
}

/* 位置按钮 */
.location-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #F5F5F5;
  border-radius: 8px;
}

.location-btn image {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.location-btn text {
  font-size: 14px;
  color: #666666;
}

/* 按钮容器 */
.buttons-container {
  display: flex;
  justify-content: space-between;
  padding: 10px 16px 16px;
  margin-bottom: 10px;
}

/* 发布按钮 */
.floating-publish-btn {
  width: 80px;
  height: 32px;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: bold;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

/* 取消按钮 */
.floating-cancel-btn {
  width: 80px;
  height: 32px;
  background-color: #F5F5F5;
  color: #666666;
  font-size: 14px;
  font-weight: bold;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 选择器公共样式 */
.selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  border-bottom: 1px solid #F5F5F5;
}

.selector-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.selector-close {
  width: 20px;
  height: 20px;
}

.selector-close image {
  width: 100%;
  height: 100%;
}

/* 话题选择器 */
.topic-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60%;
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.topic-selector.show {
  transform: translateY(0);
}

.topic-input-area {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  border-bottom: 1px solid #F5F5F5;
}

.topic-prefix {
  font-size: 16px;
  color: #FF4D4F;
  margin-right: 4px;
}

.topic-input {
  flex: 1;
  height: 100%;
  font-size: 16px;
}

.hot-topics {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.hot-topics-title {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.hot-topic-list {
  display: flex;
  flex-wrap: wrap;
}

.hot-topic-item {
  width: calc(50% - 6px); /* 减小宽度间距 */
  height: 50px; /* 减小高度 */
  background-color: #F5F5F5;
  border-radius: 6px; /* 减小圆角 */
  padding: 8px; /* 减小内边距 */
  margin-right: 12px; /* 减小右边距 */
  margin-bottom: 12px; /* 减小下边距 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hot-topic-item:nth-child(2n) {
  margin-right: 0;
}

.hot-topic-name {
  font-size: 12px; /* 减小字号 */
  color: #333333;
  margin-bottom: 2px; /* 减小下边距 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.hot-topic-count {
  font-size: 10px; /* 减小字号 */
  color: #999999;
}

/* 位置选择器 */
.location-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60%;
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.location-selector.show {
  transform: translateY(0);
}

.current-location-btn {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 16px;
  border-bottom: 1px solid #F5F5F5;
}

.location-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.location-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 权限设置 */
.permission-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
}

.permission-selector.show {
  transform: translateY(0);
}

.permission-list {
  padding: 0 16px;
}

.permission-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  border-bottom: 1px solid #F5F5F5;
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-item switch {
  transform: scale(0.8);
  margin-right: -8px;
}

.permission-name {
  font-size: 14px;
  color: #333333;
}

/* 卡片式选项区域 */
.card-tabs-container {
  margin: 0 16px 16px;
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-tabs-header {
  display: flex;
  border-bottom: 1px solid #F0F0F0;
}

.card-tab {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 14px;
  color: #666666;
}

.card-tab.active {
  color: #FF4D4F;
  font-weight: bold;
}

.card-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #FF4D4F;
  border-radius: 3px;
}

.card-tab-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: #FF4D4F;
  border-radius: 50%;
}

.card-tabs-content {
  padding: 12px; /* 减小内边距 */
}

.card-tab-pane {
  min-height: 80px; /* 减小最小高度 */
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 70px; /* 调整底部安全区域高度，为悬浮按钮留出空间 */
  width: 100%;
}

/* 关联商品 */
.linked-products-list {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin-top: 10px;
}

.linked-product-container {
  width: calc(100% - 20px);
  height: 100px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.linked-product-item {
  width: 90%;
  height: 100px;
  display: flex;
  padding: 12px;
  box-sizing: border-box;
  background-color: #F9F9F9;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.linked-product-item:active {
  background-color: #F0F0F0;
}

.linked-product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.linked-product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.linked-product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.linked-product-price-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.linked-product-price {
  font-size: 16px;
  color: #FF4D4F;
  font-weight: bold;
  margin-right: 8px;
}

.linked-product-original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.linked-product-shop {
  font-size: 12px;
  color: #999999;
}

.clear-product-btn {
  width: 10%;
  height: 100px;
  background-color: #FFF0F0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.clear-btn-text {
  writing-mode: vertical-lr;
  text-orientation: upright;
  font-size: 12px;
  color: #FF4D4F;
  letter-spacing: 2px;
  font-weight: bold;
}

.clear-product-btn:active {
  background-color: #FFE0E0;
}

.change-product-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.change-product-icon image {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.add-product-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #F5F5F5;
  border-radius: 8px;
  margin-top: 16px;
}

.add-product-btn image {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.add-product-btn text {
  font-size: 14px;
  color: #666666;
}

/* 产品选择器 */
.product-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 70%;
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.product-selector.show {
  transform: translateY(0);
}

.product-search {
  padding: 8px 16px;
  border-bottom: 1px solid #F5F5F5;
}

.product-search-input {
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 18px;
  padding: 0 16px;
  font-size: 14px;
}

.product-list {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #F5F5F5;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 12px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price {
  font-size: 16px;
  color: #FF4D4F;
  font-weight: bold;
  margin-bottom: 4px;
}

.product-shop {
  font-size: 12px;
  color: #999999;
}

.no-product {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #999999;
  font-size: 14px;
}

/* 地区选择器 */
.region-selector {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60%;
  background-color: #FFFFFF;
  border-radius: 12px 12px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.region-selector.show {
  transform: translateY(0);
}

.region-nationwide {
  padding: 0;
  border-bottom: 1px solid #F0F0F0;
}

.region-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.region-provinces, .region-cities {
  flex: 1;
  border-right: 1px solid #F5F5F5;
}

.region-scroll {
  height: 100%;
}

.region-item {
  height: 44px;
  line-height: 44px;
  padding: 0 16px;
  font-size: 14px;
  color: #333333;
  border-bottom: 1px solid #F5F5F5;
}

.region-item.active {
  color: #FF4D4F;
  background-color: #FFF0F0;
}

.region-footer {
  display: flex;
  height: 50px;
  border-top: 1px solid #F5F5F5;
}

.region-cancel, .region-confirm {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.region-cancel {
  color: #666666;
  background-color: #F5F5F5;
}

.region-confirm {
  color: #FFFFFF;
  background-color: #FF4D4F;
}

.image-grid-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.image-grid-item,
.add-image-btn {
  width: 72px;
  height: 72px;
  min-width: 72px;
  min-height: 72px;
  max-width: 72px;
  max-height: 72px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.grid-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background: #f5f5f5;
  position: absolute;
  left: 0;
  top: 0;
}

.add-image-btn image {
  width: 28px;
  height: 28px;
}

/* 视频控件容器高度与图片一致 */
.large-video-container-wrapper {
  height: 80px;
  min-height: 80px;
  max-height: 80px;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.large-video-container,
.large-add-video-btn {
  width: 72px;
  height: 72px;
  min-width: 72px;
  min-height: 72px;
  max-width: 72px;
  max-height: 72px;
}

.large-add-video-btn image {
  width: 28px;
  height: 28px;
}

.flex-3 {
  flex: 3 1 0%;
  min-width: 0;
}
.flex-1 {
  flex: 1 1 0%;
  min-width: 0;
}

.image-grid-container.flex-3 {
  margin-right: 8px;
}

.video-upload-section.flex-1 {
  min-width: 0;
}

.image-upload-section.flex-3 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.image-grid-container,
.large-video-container-wrapper {
  height: 80px;
  min-height: 80px;
  max-height: 80px;
}

.image-grid-item,
.add-image-btn,
.large-video-container,
.large-add-video-btn {
  height: 72px;
  width: 72px;
  min-width: 72px;
  min-height: 72px;
  max-width: 72px;
  max-height: 72px;
}

.grid-image,
.large-video-player {
  height: 100%;
  width: 100%;
}

.large-add-video-btn image,
.add-image-btn image {
  width: 24px;
  height: 24px;
}

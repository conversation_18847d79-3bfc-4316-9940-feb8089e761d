
[2025-05-30 00:46:59] Started by user coding
[2025-05-30 00:46:59] Running in Durability level: MAX_SURVIVABILITY
[2025-05-30 00:47:01] [Pipeline] Start of Pipeline
[2025-05-30 00:47:01] [Pipeline] node
[2025-05-30 00:47:01] Running on <PERSON> in /root/workspace
[2025-05-30 00:47:01] [Pipeline] {
[2025-05-30 00:47:01] [Pipeline] stage
[2025-05-30 00:47:01] [Pipeline] { (检出软件包)
[2025-05-30 00:47:02] Stage "检出软件包" skipped due to when conditional
[2025-05-30 00:47:02] [Pipeline] }
[2025-05-30 00:47:02] [Pipeline] // stage
[2025-05-30 00:47:02] [Pipeline] stage
[2025-05-30 00:47:02] [Pipeline] { (检出 ZIP 包)
[2025-05-30 00:47:02] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-30 00:47:02] [Pipeline] }
[2025-05-30 00:47:02] [Pipeline] // stage
[2025-05-30 00:47:02] [Pipeline] stage
[2025-05-30 00:47:02] [Pipeline] { (检出代码仓库)
[2025-05-30 00:47:02] [Pipeline] sh
[2025-05-30 00:47:03] + git clone ****** .
[2025-05-30 00:47:03] Cloning into '.'...
[2025-05-30 00:47:05] [Pipeline] sh
[2025-05-30 00:47:05] + git checkout main
[2025-05-30 00:47:05] Already on 'main'
[2025-05-30 00:47:05] Your branch is up to date with 'origin/main'.
[2025-05-30 00:47:05] [Pipeline] }
[2025-05-30 00:47:05] [Pipeline] // stage
[2025-05-30 00:47:05] [Pipeline] stage
[2025-05-30 00:47:05] [Pipeline] { (写入 dockerfile)
[2025-05-30 00:47:05] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-30 00:47:05] [Pipeline] }
[2025-05-30 00:47:06] [Pipeline] // stage
[2025-05-30 00:47:06] [Pipeline] stage
[2025-05-30 00:47:06] [Pipeline] { (构建 Docker 镜像)
[2025-05-30 00:47:06] [Pipeline] sh
[2025-05-30 00:47:06] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-147-20250530004657
[2025-05-30 00:47:06] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-30 00:47:06] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-30 00:47:06] Configure a credential helper to remove this warning. See
[2025-05-30 00:47:06] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-30 00:47:06] 
[2025-05-30 00:47:06] Login Succeeded
[2025-05-30 00:47:06] [Pipeline] sh
[2025-05-30 00:47:06] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-147-20250530004657 backend
[2025-05-30 00:47:08] #0 building with "default" instance using docker driver
[2025-05-30 00:47:08] 
[2025-05-30 00:47:08] #1 [internal] load build definition from Dockerfile
[2025-05-30 00:47:08] #1 transferring dockerfile:
[2025-05-30 00:47:08] #1 transferring dockerfile: 674B done
[2025-05-30 00:47:08] #1 DONE 0.3s
[2025-05-30 00:47:08] 
[2025-05-30 00:47:08] #2 [internal] load .dockerignore
[2025-05-30 00:47:08] #2 transferring context: 2B done
[2025-05-30 00:47:08] #2 DONE 0.3s
[2025-05-30 00:47:08] 
[2025-05-30 00:47:08] #3 [internal] load metadata for docker.io/library/node:18-alpine
[2025-05-30 00:47:10] #3 DONE 1.5s
[2025-05-30 00:47:10] 
[2025-05-30 00:47:10] #4 [internal] load build context
[2025-05-30 00:47:10] #4 DONE 0.0s
[2025-05-30 00:47:10] 
[2025-05-30 00:47:10] #5 [1/8] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 00:47:10] #5 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 00:47:10] #5 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 0.1s done
[2025-05-30 00:47:10] #5 sha256:ee77c6cd7c1886ecc802ad6cedef3a8ec1ea27d1fb96162bf03dd3710839b8da 6.18kB / 6.18kB done
[2025-05-30 00:47:10] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0B / 3.64MB 0.2s
[2025-05-30 00:47:10] #5 sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 7.67kB / 7.67kB done
[2025-05-30 00:47:10] #5 sha256:929b04d7c782f04f615cf785488fed452b6569f87c73ff666ad553a7554f0006 1.72kB / 1.72kB done
[2025-05-30 00:47:10] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0B / 40.01MB 0.2s
[2025-05-30 00:47:10] #5 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0B / 1.26MB 0.2s
[2025-05-30 00:47:10] #5 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 0B / 446B 0.3s
[2025-05-30 00:47:10] #5 ...
[2025-05-30 00:47:10] 
[2025-05-30 00:47:10] #4 [internal] load build context
[2025-05-30 00:47:10] #4 transferring context: 35.49MB 0.2s done
[2025-05-30 00:47:10] #4 DONE 0.4s
[2025-05-30 00:47:10] 
[2025-05-30 00:47:10] #5 [1/8] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 00:47:10] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.15MB / 3.64MB 0.4s
[2025-05-30 00:47:10] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.64MB / 3.64MB 0.4s done
[2025-05-30 00:47:10] #5 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 446B / 446B 0.4s done
[2025-05-30 00:47:10] #5 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870
[2025-05-30 00:47:10] #5 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 1.26MB / 1.26MB 0.5s done
[2025-05-30 00:47:10] #5 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0.1s done
[2025-05-30 00:47:10] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 2.10MB / 40.01MB 0.9s
[2025-05-30 00:47:11] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 4.19MB / 40.01MB 1.3s
[2025-05-30 00:47:11] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 6.29MB / 40.01MB 1.7s
[2025-05-30 00:47:11] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 8.39MB / 40.01MB 2.0s
[2025-05-30 00:47:12] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 10.49MB / 40.01MB 2.4s
[2025-05-30 00:47:12] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 12.58MB / 40.01MB 2.6s
[2025-05-30 00:47:12] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 14.68MB / 40.01MB 2.9s
[2025-05-30 00:47:13] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 16.78MB / 40.01MB 3.3s
[2025-05-30 00:47:13] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 18.87MB / 40.01MB 3.5s
[2025-05-30 00:47:13] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 20.97MB / 40.01MB 3.8s
[2025-05-30 00:47:13] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 23.07MB / 40.01MB 4.0s
[2025-05-30 00:47:14] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 25.17MB / 40.01MB 4.3s
[2025-05-30 00:47:14] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 27.26MB / 40.01MB 4.6s
[2025-05-30 00:47:14] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 30.41MB / 40.01MB 4.9s
[2025-05-30 00:47:14] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 33.55MB / 40.01MB 5.2s
[2025-05-30 00:47:15] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 36.70MB / 40.01MB 5.6s
[2025-05-30 00:47:15] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 5.9s
[2025-05-30 00:47:15] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 5.9s done
[2025-05-30 00:47:15] #5 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0.1s
[2025-05-30 00:47:16] #5 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0.9s done
[2025-05-30 00:47:16] #5 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3
[2025-05-30 00:47:16] #5 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0.0s done
[2025-05-30 00:47:16] #5 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549
[2025-05-30 00:47:16] #5 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 done
[2025-05-30 00:47:17] #5 DONE 7.8s
[2025-05-30 00:47:17] 
[2025-05-30 00:47:17] #6 [2/8] RUN apk add --no-cache python3 make g++ gcc ca-certificates
[2025-05-30 00:47:17] #6 0.278 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
[2025-05-30 00:47:18] #6 1.141 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
[2025-05-30 00:47:18] #6 1.403 (1/30) Installing ca-certificates (20241121-r1)
[2025-05-30 00:47:18] #6 1.442 (2/30) Installing libstdc++-dev (14.2.0-r4)
[2025-05-30 00:47:19] #6 1.578 (3/30) Installing jansson (2.14-r4)
[2025-05-30 00:47:19] #6 1.609 (4/30) Installing zstd-libs (1.5.6-r2)
[2025-05-30 00:47:19] #6 1.646 (5/30) Installing binutils (2.43.1-r2)
[2025-05-30 00:47:19] #6 1.735 (6/30) Installing libgomp (14.2.0-r4)
[2025-05-30 00:47:19] #6 1.769 (7/30) Installing libatomic (14.2.0-r4)
[2025-05-30 00:47:19] #6 1.801 (8/30) Installing gmp (6.3.0-r2)
[2025-05-30 00:47:19] #6 1.836 (9/30) Installing isl26 (0.26-r1)
[2025-05-30 00:47:19] #6 1.884 (10/30) Installing mpfr4 (4.2.1-r0)
[2025-05-30 00:47:19] #6 1.920 (11/30) Installing mpc1 (1.3.1-r1)
[2025-05-30 00:47:19] #6 1.953 (12/30) Installing gcc (14.2.0-r4)
[2025-05-30 00:47:20] #6 3.215 (13/30) Installing musl-dev (1.2.5-r9)
[2025-05-30 00:47:20] #6 3.390 (14/30) Installing g++ (14.2.0-r4)
[2025-05-30 00:47:21] #6 4.079 (15/30) Installing make (4.4.1-r2)
[2025-05-30 00:47:21] #6 4.115 (16/30) Installing libbz2 (1.0.8-r6)
[2025-05-30 00:47:21] #6 4.147 (17/30) Installing libexpat (2.7.0-r0)
[2025-05-30 00:47:21] #6 4.181 (18/30) Installing libffi (3.4.7-r0)
[2025-05-30 00:47:21] #6 4.212 (19/30) Installing gdbm (1.24-r0)
[2025-05-30 00:47:21] #6 4.244 (20/30) Installing xz-libs (5.6.3-r1)
[2025-05-30 00:47:21] #6 4.279 (21/30) Installing mpdecimal (4.0.0-r0)
[2025-05-30 00:47:21] #6 4.314 (22/30) Installing ncurses-terminfo-base (6.5_p20241006-r3)
[2025-05-30 00:47:21] #6 4.347 (23/30) Installing libncursesw (6.5_p20241006-r3)
[2025-05-30 00:47:21] #6 4.385 (24/30) Installing libpanelw (6.5_p20241006-r3)
[2025-05-30 00:47:21] #6 4.417 (25/30) Installing readline (8.2.13-r0)
[2025-05-30 00:47:21] #6 4.452 (26/30) Installing sqlite-libs (3.48.0-r2)
[2025-05-30 00:47:21] #6 4.516 (27/30) Installing python3 (3.12.10-r1)
[2025-05-30 00:47:22] #6 4.906 (28/30) Installing python3-pycache-pyc0 (3.12.10-r1)
[2025-05-30 00:47:22] #6 5.101 (29/30) Installing pyc (3.12.10-r1)
[2025-05-30 00:47:22] #6 5.101 (30/30) Installing python3-pyc (3.12.10-r1)
[2025-05-30 00:47:22] #6 5.101 Executing busybox-1.37.0-r12.trigger
[2025-05-30 00:47:22] #6 5.104 Executing ca-certificates-20241121-r1.trigger
[2025-05-30 00:47:22] #6 5.126 OK: 270 MiB in 47 packages
[2025-05-30 00:47:22] #6 DONE 5.2s
[2025-05-30 00:47:22] 
[2025-05-30 00:47:22] #7 [3/8] WORKDIR /app
[2025-05-30 00:47:26] #7 DONE 3.6s
[2025-05-30 00:47:26] 
[2025-05-30 00:47:26] #8 [4/8] COPY package*.json ./
[2025-05-30 00:47:26] #8 DONE 0.2s
[2025-05-30 00:47:26] 
[2025-05-30 00:47:26] #9 [5/8] RUN npm config set registry https://registry.npmmirror.com
[2025-05-30 00:47:27] #9 DONE 0.7s
[2025-05-30 00:47:27] 
[2025-05-30 00:47:27] #10 [6/8] RUN npm install --production
[2025-05-30 00:47:27] #10 0.519 npm warn config production Use `--omit=dev` instead.
[2025-05-30 00:47:29] #10 2.200 npm warn deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-30 00:47:29] #10 2.391 npm warn deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-30 00:47:29] #10 2.452 npm warn deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-30 00:47:30] #10 2.813 
[2025-05-30 00:47:30] #10 2.813 added 221 packages in 2s
[2025-05-30 00:47:30] #10 2.813 
[2025-05-30 00:47:30] #10 2.813 31 packages are looking for funding
[2025-05-30 00:47:30] #10 2.813   run `npm fund` for details
[2025-05-30 00:47:30] #10 DONE 3.0s
[2025-05-30 00:47:30] 
[2025-05-30 00:47:30] #11 [7/8] COPY . .
[2025-05-30 00:47:30] #11 DONE 0.3s
[2025-05-30 00:47:30] 
[2025-05-30 00:47:30] #12 [8/8] RUN chmod +x /app/cert/initenv.sh
[2025-05-30 00:47:31] #12 DONE 0.4s
[2025-05-30 00:47:31] 
[2025-05-30 00:47:31] #13 exporting to image
[2025-05-30 00:47:31] #13 exporting layers
[2025-05-30 00:47:32] #13 exporting layers 2.0s done
[2025-05-30 00:47:33] #13 writing image sha256:b29f1c3d2280ae23d1ccbf267df2beb8c6a7ea084d80eed72c9d0b934ac9fc89 0.0s done
[2025-05-30 00:47:33] #13 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-147-20250530004657 0.0s done
[2025-05-30 00:47:33] #13 DONE 2.1s
[2025-05-30 00:47:33] [Pipeline] sh
[2025-05-30 00:47:33] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-30 00:47:33] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-147-20250530004657
[2025-05-30 00:47:33] + awk {print $NF}
[2025-05-30 00:47:33] + echo 镜像的大小是：457MB
[2025-05-30 00:47:33] 镜像的大小是：457MB
[2025-05-30 00:47:33] [Pipeline] echo
[2025-05-30 00:47:33] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-30 00:47:33] [Pipeline] }
[2025-05-30 00:47:33] [Pipeline] // stage
[2025-05-30 00:47:33] [Pipeline] stage
[2025-05-30 00:47:34] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-30 00:47:34] [Pipeline] sh
[2025-05-30 00:47:34] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-147-20250530004657
[2025-05-30 00:47:34] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-30 00:47:34] 02f307487dec: Preparing
[2025-05-30 00:47:34] 180fabe0179b: Preparing
[2025-05-30 00:47:34] d98ef9540ded: Preparing
[2025-05-30 00:47:34] 05e252527539: Preparing
[2025-05-30 00:47:34] b00ce7fb82e6: Preparing
[2025-05-30 00:47:34] dea9a7eecf74: Preparing
[2025-05-30 00:47:34] eec9e74de01e: Preparing
[2025-05-30 00:47:34] 82140d9a70a7: Preparing
[2025-05-30 00:47:34] f3b40b0cdb1c: Preparing
[2025-05-30 00:47:34] 0b1f26057bd0: Preparing
[2025-05-30 00:47:34] 08000c18d16d: Preparing
[2025-05-30 00:47:34] 08000c18d16d: Waiting
[2025-05-30 00:47:34] 82140d9a70a7: Layer already exists
[2025-05-30 00:47:34] 0b1f26057bd0: Layer already exists
[2025-05-30 00:47:34] f3b40b0cdb1c: Layer already exists
[2025-05-30 00:47:34] 08000c18d16d: Layer already exists
[2025-05-30 00:47:35] 02f307487dec: Pushed
[2025-05-30 00:47:35] b00ce7fb82e6: Pushed
[2025-05-30 00:47:35] dea9a7eecf74: Pushed
[2025-05-30 00:47:35] 05e252527539: Pushed
[2025-05-30 00:47:40] d98ef9540ded: Pushed
[2025-05-30 00:47:52] 180fabe0179b: Pushed
[2025-05-30 00:48:31] eec9e74de01e: Pushed
[2025-05-30 00:48:31] lieyouqi-147-20250530004657: digest: sha256:17702655b92e7cc261fdb8f7f936a1206d17bb04a5b855acbf24f4d7525d88f4 size: 2623
[2025-05-30 00:48:31] [Pipeline] }
[2025-05-30 00:48:31] [Pipeline] // stage
[2025-05-30 00:48:31] [Pipeline] }
[2025-05-30 00:48:31] [Pipeline] // node
[2025-05-30 00:48:31] [Pipeline] End of Pipeline
[2025-05-30 00:48:31] Finished: SUCCESS
***
-----------构建lieyouqi-147-----------
2025-05-30 00:46:58 create_build_image : creating
2025-05-30 00:48:37 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-147-----------
2025-05-30 00:48:38 create_eks_virtual_service : creating
2025-05-30 00:48:39 check_eks_virtual_service : process, DescribeVersion_user_error_Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "daf64f8c29d67c316d15695d61f126274df3972268ed0ae251bef68b4c39ed31": OCI runtime exec failed: exec failed: unable to start container process: error writing config to pipe: write init-p: broken pipe: unknown, message: "", [service]:[Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "8fd3611f3d65f8337432b5b1bd7d2a8d39718a7eff39d5accba5a74fdbecb786": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "1a1d6850c29f83e447033345b1b86f940f3c26116556935ce8823f6f116ebcc1": OCI runtime exec failed: exec failed: unable to start container process: error executing setns process: exit status 1: unknown, message: "",Back-off restarting failed container,Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "0c429484a2c7e942342539192568c2ba51555e8312772e19314f69ffbab27502": OCI runtime exec failed: exec failed: unable to start container process: read init-p: connection reset by peer: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "a3acbd4a526a3947502e3d0d00bda61b36534e76dca8dfd139fd5c213fa48e33": OCI runtime exec failed: exec failed: unable to start container process: error executing setns process: exit status 1: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "855f38fb9e1dc4756f614a542fd7176c5c40e732385bf69cbb5efc6376d2ad29": OCI runtime exec failed: exec failed: cannot exec in a stopped container: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" in Pod "lieyouqi-147-76f5bd8c44-pjchg_yhrfubyv(a0868d49-f863-4639-8234-2e494f403c10)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "daf64f8c29d67c316d15695d61f126274df3972268ed0ae251bef68b4c39ed31": OCI runtime exec failed: exec failed: unable to start container process: error writing config to pipe: write init-p: broken pipe: unknown, message: "",]
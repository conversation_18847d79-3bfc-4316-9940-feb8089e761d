const app = getApp();
const { settingsApi } = require('../../utils/api');

Page({
  data: {
    message: '',
    contact: '',
    name: '',
    companyContact: {
      address: '加载中...',
      phone: '加载中...',
      email: '加载中...'
    },
    loading: true
  },
  onLoad: function() {
    wx.setNavigationBarTitle({ title: '客服中心' });
    this.getCompanyContact();
  },

  // 从数据库获取公司联系方式
  getCompanyContact: function() {
    this.setData({ loading: true });
    console.log('开始获取公司联系方式...');

    settingsApi.getCompanyContact()
      .then(res => {
        console.log('获取公司联系方式成功:', res);
        if (res.success && res.data) {
          // 将数据库字段映射到页面需要的格式
          this.setData({
            companyContact: {
              address: res.data.company_address || '暂无地址信息',
              phone: res.data.company_phone || '暂无电话信息',
              email: res.data.company_email || '暂无邮箱信息'
            },
            loading: false
          });
          console.log('公司联系方式设置成功');
        } else {
          console.log('响应数据格式不正确:', res);
          this.setData({
            companyContact: {
              address: '暂无地址信息',
              phone: '暂无电话信息',
              email: '暂无邮箱信息'
            },
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取公司联系方式失败:', err);
        console.error('错误详情:', {
          message: err.message,
          statusCode: err.statusCode,
          data: err.data
        });

        // 显示错误提示
        wx.showToast({
          title: '获取公司信息失败',
          icon: 'none',
          duration: 2000
        });

        this.setData({
          companyContact: {
            address: '获取地址信息失败',
            phone: '获取电话信息失败',
            email: '获取邮箱信息失败'
          },
          loading: false
        });
      });
  },
  onInputMessage: function(e) {
    this.setData({ message: e.detail.value });
  },
  onInputName: function(e) {
    this.setData({ name: e.detail.value });
  },
  onInputContact: function(e) {
    this.setData({ contact: e.detail.value });
  },
  onChatTap: function() {
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '登录后可与客服对话',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }
    wx.openCustomerServiceChat({
      extInfo: { url: '' },
      corpId: '', // 你的企业微信corpId
      success: res => {},
      fail: err => {
        wx.showToast({ title: '客服功能暂不可用', icon: 'none' });
      }
    });
  },
  onSubmitMessage: function() {
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '请先登录',
        content: '登录后可提交留言',
        showCancel: true,
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          }
        }
      });
      return;
    }

    const { message, name, contact } = this.data;

    // 验证输入
    if (!message.trim()) {
      wx.showToast({ title: '请输入留言内容', icon: 'none' });
      return;
    }
    if (!name.trim()) {
      wx.showToast({ title: '请输入用户名', icon: 'none' });
      return;
    }
    if (!contact.trim()) {
      wx.showToast({ title: '请输入手机号', icon: 'none' });
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(contact.trim())) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
      return;
    }

    wx.showLoading({ title: '提交中', mask: true });

    // 使用API提交留言
    settingsApi.submitMessage({
      message: message.trim(),
      name: name.trim(),
      contact: contact.trim(),
      userId: app.globalData.userInfo && app.globalData.userInfo.id || ''
    })
    .then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '留言已提交', icon: 'success' });
        this.setData({ message: '', name: '', contact: '' });
      } else {
        wx.showToast({ title: res.message || '提交失败', icon: 'none' });
      }
    })
    .catch(err => {
      wx.hideLoading();
      console.error('提交留言失败:', err);
      wx.showToast({ title: '提交失败，请稍后重试', icon: 'none' });
    });
  },

  // 拨打电话
  onCallPhone: function() {
    const phone = this.data.companyContact.phone;
    if (phone && phone !== '暂无电话信息') {
      wx.makePhoneCall({
        phoneNumber: phone,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败:', err);
          wx.showToast({ title: '拨打电话失败', icon: 'none' });
        }
      });
    } else {
      wx.showToast({ title: '暂无电话信息', icon: 'none' });
    }
  }
});
{"name": "leiyouqi-backend", "version": "1.0.0", "description": "Backend for 猎优企 WeChat Mini Program", "main": "app.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node utils/init-db.js", "init-mysql-db": "node utils/init-mysql-db.js", "migrate-data": "node utils/data-migration.js", "migrate-to-mysql": "node utils/migrate-sqlite-to-mysql.js", "migrate-old-to-new": "node utils/migrate-old-to-new-db.js", "export-mock": "node utils/export-mock-data.js", "add-group-announcement": "node scripts/add-group-announcement.js", "test": "echo \"Error: no test specified\" && exit 1", "container": "USE_MYSQL=true node -e \"console.log('检查数据目录权限...'); console.log('数据目录权限正常');\" && (npm run init-mysql-db || echo '数据库初始化失败，但将继续启动应用') && npm start"}, "dependencies": {"axios": "^1.9.0", "body-parser": "^2.2.0", "compression": "^1.8.0", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.14.7", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "engines": {"node": ">=16.0.0"}}
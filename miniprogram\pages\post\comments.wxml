<view class="post-comments-container">
  <view wx:if="{{loading}}" class="loading">加载中...</view>
  <block wx:else>
    <block wx:if="{{postInfo}}">
      <view class="post-detail-card">
        <view class="post-info">
          <view class="post-user-row">
            <image class="post-avatar" src="{{postInfo.avatar}}"></image>
            <view class="post-user-info">
              <view class="post-nickname">{{postInfo.nickname}}</view>
              <view class="post-time">{{formatTime(postInfo.createTime) || '2025年5月14日'}}</view>
            </view>
            <view class="follow-btn {{postInfo.isFollowed ? 'followed' : ''}}" catchtap="onFollowTap">
              {{postInfo.isFollowed ? '已关注' : '关注'}}
            </view>
          </view>
        </view>
        <view class="post-content">{{postInfo.content}}</view>
        <!-- 单图大图 -->
        <view class="single-image-wrapper" wx:if="{{postInfo.imageMode === 'single'}}">
          <image class="single-image" src="{{postInfo.images[0]}}" mode="aspectFill" binderror="onImageError" />
        </view>
        <!-- 多图九宫格 -->
        <view class="image-grid" wx:if="{{postInfo.imageMode === 'multi'}}">
          <image wx:for="{{postInfo.images}}" wx:key="index" src="{{item}}" class="grid-image" mode="aspectFill" binderror="onImageError" />
        </view>
        <view wx:if="{{postInfo.video}}" class="post-video">
          <video src="{{postInfo.video}}" controls style="width:100%;border-radius:8px;"></video>
        </view>
        <!-- 商品关联区 -->
        <view class="post-products" wx:if="{{postInfo.linkedProducts && postInfo.linkedProducts.length > 0}}">
          <block wx:for="{{postInfo.linkedProducts}}" wx:key="id" wx:for-index="index">
            <view class="product-link" bindtap="onProductTap" data-index="{{index}}">
              <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill" binderror="onImageError" data-type="product" data-index="{{index}}" data-id="{{item.id || item._id}}" data-src="{{item.imageUrl}}"></image>
              <view class="product-info">
                <view class="product-name">{{item.name || '商品'}}</view>
                <view class="product-price">¥{{item.price || '0.00'}}</view>
              </view>
              <view class="buy-btn">购买</view>
            </view>
          </block>
        </view>
        <!-- 发布区域和主题类别 -->
        <view class="post-tags" wx:if="{{postInfo.region || postInfo.topic}}">
          <view class="post-tag post-region" wx:if="{{postInfo.region}}">{{postInfo.regionText}}</view>
          <view class="post-tag post-topic" wx:if="{{postInfo.topic}}">#{{postInfo.topic}}</view>
        </view>
        <view wx:if="{{postInfo.location}}" class="post-location">📍{{postInfo.location}}</view>
        <!-- 互动栏 -->
        <view class="interaction-bar">
          <view class="interaction-item">
            <image src="{{postInfo.isLiked ? '/images/icons2/已点赞.png' : '/images/icons2/未点赞.png'}}"></image>
            <text>({{postInfo.likeCount || 0}})</text>
          </view>
          <view class="interaction-item">
            <image src="/images/icons2/评论.png"></image>
            <text>({{postInfo.commentCount || 0}})</text>
          </view>
          <view class="interaction-item">
            <image src="/images/icons2/分享.png"></image>
            <text>转发 ({{postInfo.shareCount || 0}})</text>
          </view>
        </view>
      </view>
    </block>
    <view wx:if="{{comments.length === 0}}" class="empty">暂无评论</view>
    <block wx:for="{{comments}}" wx:key="id">
      <view class="comment-item">
        <image class="avatar" src="{{item.avatar}}"></image>
        <view class="comment-right">
          <view class="comment-user-info">
            <view class="nickname">{{item.nickname}}</view>
            <view class="comment-time">{{formatTime(item.createTime) || '2025年5月14日'}}</view>
          </view>
          <view class="comment-content">{{item.content}}</view>
        </view>
      </view>
    </block>
  </block>
  <view class="input-bar-outer">
    <view class="comment-input-bar">
      <view class="input-area">
        <input class="comment-input" placeholder="写下你的评论..." value="{{commentInput}}" bindinput="onInput" maxlength="200" />
      </view>
      <view class="btn-area">
        <button class="submit-btn" catchtap="submitComment" loading="{{submitting}}">发表</button>
      </view>
    </view>
  </view>
</view> 
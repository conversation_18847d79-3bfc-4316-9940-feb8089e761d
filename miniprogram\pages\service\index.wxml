<view class="service-container">
  <view class="card message-card">
    <view class="card-title">给客服留言</view>
    <view class="msg-box">
      <textarea class="msg-textarea" placeholder="请输入你想说的话" bindinput="onInputMessage" value="{{message}}" />
    </view>
    <view class="input-row">
      <text class="input-label">用户名</text>
      <input class="input-field" placeholder="请输入用户名" bindinput="onInputName" value="{{name}}" />
    </view>
    <view class="input-row">
      <text class="input-label">手机号</text>
      <input class="input-field" placeholder="请输入手机号" bindinput="onInputContact" value="{{contact}}" type="number" maxlength="11" />
    </view>
    <button class="submit-btn" bindtap="onSubmitMessage">提交留言</button>
  </view>

  <view class="card contact-card">
    <view class="card-title">公司联系方式</view>
    <view class="contact-content">
      <view class="loading-indicator" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      <block wx:else>
        <view class="contact-item">
          <view class="contact-icon">
            <image src="/images/icons/location.png" mode="aspectFit"></image>
          </view>
          <view class="contact-info">
            <text class="contact-label">地址</text>
            <text class="contact-value">{{companyContact.address}}</text>
          </view>
        </view>
        <view class="contact-item" bindtap="onCallPhone">
          <view class="contact-icon">
            <image src="/images/icons/tel.png" mode="aspectFit"></image>
          </view>
          <view class="contact-info">
            <text class="contact-label">电话</text>
            <text class="contact-value">{{companyContact.phone}}</text>
          </view>
        </view>
        <view class="contact-item">
          <view class="contact-icon">
            <image src="/images/icons/email.png" mode="aspectFit"></image>
          </view>
          <view class="contact-info">
            <text class="contact-label">邮箱</text>
            <text class="contact-value">{{companyContact.email}}</text>
          </view>
        </view>
      </block>
    </view>
  </view>

  <view class="chat-float-btn" bindtap="onChatTap">
    <view class="chat-text">
      <text>在线</text>
      <text>对话</text>
    </view>
  </view>
</view>
/* pages/auth/auth.wxss */
.auth-container {
  padding: 0 30px;
  height: 100vh;
  background-color: #FFFFFF;
}

.header {
  padding-top: 60px;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 16px;
  color: #666666;
}

.login-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #EEEEEE;
}

.login-tab {
  flex: 1;
  text-align: center;
  font-size: 16px;
  color: #666666;
  padding: 10px 0;
  position: relative;
}

.login-tab.active {
  color: #FF4D4F;
  font-weight: 500;
}

.login-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: #FF4D4F;
  border-radius: 2px;
}

.input-area {
  margin-bottom: 30px;
}

.input-group {
  display: flex;
  align-items: center;
  height: 48px;
  background-color: #F5F5F5;
  border-radius: 24px;
  padding: 0 15px;
  margin-bottom: 20px;
}

.forgot-password {
  text-align: right;
  font-size: 14px;
  color: #1890FF;
  margin-bottom: 20px;
}

.input-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.input {
  flex: 1;
  height: 100%;
  font-size: 15px;
}

.verify-btn {
  width: 100px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 14px;
  color: #999999;
  background-color: #EEEEEE;
  border-radius: 16px;
}

.verify-btn.active {
  color: #FFFFFF;
  background-color: #FF4D4F;
}

.login-btn {
  height: 48px;
  line-height: 48px;
  text-align: center;
  background-color: #CCCCCC;
  color: #FFFFFF;
  font-size: 16px;
  border-radius: 24px;
  margin-bottom: 30px;
}

.login-btn.active {
  background-color: #FF4D4F;
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.2);
}

.divider-container {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.divider {
  flex: 1;
  height: 1px;
  background-color: #EEEEEE;
}

.divider-text {
  padding: 0 15px;
  font-size: 14px;
  color: #999999;
}

.other-login {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.other-login-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.other-login-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
}

.other-login-text {
  font-size: 14px;
  color: #666666;
}

.wx-login-btn {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal !important;
  border-radius: 0 !important;
  font-size: inherit !important;
  color: inherit !important;
  width: auto !important;
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

.wx-login-btn::after {
  border: none !important;
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.checkbox {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-icon {
  width: 20px;
  height: 20px;
}

.agreement-text {
  font-size: 12px;
  color: #999999;
}

.agreement-link {
  color: #FF4D4F;
}

// pages/settings/password.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    loading: false,
    showOldPassword: true
  },

  onLoad: function() {
    // 设置顶部导航栏标题
    wx.setNavigationBarTitle({ title: '修改密码' });

    // 检查是否首次修改密码（即用户未设置过密码）
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && (!userInfo.password || userInfo.password === '')) {
      // 首次修改，自动填充旧密码并隐藏输入框
      this.setData({
        oldPassword: '首次修改自动填充',
        showOldPassword: false
      });
    }
  },

  // 输入旧密码
  inputOldPassword: function(e) {
    this.setData({
      oldPassword: e.detail.value
    });
  },

  // 输入新密码
  inputNewPassword: function(e) {
    this.setData({
      newPassword: e.detail.value
    });
  },

  // 确认新密码
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 保存密码
  savePassword: function() {
    const { oldPassword, newPassword, confirmPassword } = this.data;

    if (!oldPassword) {
      wx.showToast({
        title: '请输入旧密码',
        icon: 'none'
      });
      return;
    }

    if (!newPassword) {
      wx.showToast({
        title: '请输入新密码',
        icon: 'none'
      });
      return;
    }

    if (newPassword.length < 6) {
      wx.showToast({
        title: '新密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    // 检查token是否存在
    const token = wx.getStorageSync('token');
    console.log('修改密码时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');

    if (!token) {
      wx.showToast({
        title: '未登录，请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/auth/auth'
        });
      }, 1500);
      return;
    }

    console.log('开始修改密码');

    this.doUpdatePassword(oldPassword, newPassword);
  },

  // 执行密码修改
  doUpdatePassword: function(oldPassword, newPassword) {
    userApi.updatePassword(oldPassword, newPassword)
      .then(res => {
        console.log('修改密码响应:', res);
        this.setData({ loading: false });

        if (res.success) {
          // 标记需要刷新个人中心页面
          const app = getApp();
          app.globalData.needRefreshProfile = true;

          // 更新登录状态
          const loginStateManager = require('../../utils/login-state-manager');
          const token = wx.getStorageSync('token');
          const userInfo = wx.getStorageSync('userInfo');

          if (token && userInfo) {
            console.log('更新登录状态');
            loginStateManager.saveLoginState(userInfo, token, true);
          }

          wx.showToast({
            title: '密码修改成功',
            icon: 'success'
          });

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.message || '修改失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('修改密码请求失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
})

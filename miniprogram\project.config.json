{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "微信小程序", "setting": {"useCompilerPlugins": ["sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "minified": true, "postcss": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "", "appid": "wx38ae23bd944e5a87", "libVersion": "3.8.2", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}
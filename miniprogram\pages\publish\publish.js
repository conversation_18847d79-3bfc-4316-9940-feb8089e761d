// pages/publish/publish.js
const { productApi, postApi, uploadFile } = require('../../utils/api');

Page({
  data: {
    content: '',
    images: [],
    video: null,
    topics: [], // 改为数组存储多个选中的主题
    location: null,
    isPublic: true,
    allowComment: true,
    allowForward: true,
    maxContentLength: 250,
    maxImageCount: 3,
    isLogin: false,
    submitting: false,
    // 卡片式选项区域控制
    activeTab: 'topic',
    // 选择器控制
    showTopicSelector: false,
    showLocationSelector: false,
    showProductSelector: false,
    showRegionSelector: false,
    // 主题相关
    hotTopics: [
      { name: '企业服务', selected: false },
      { name: '工商注册', selected: false },
      { name: '税务合规', selected: false },
      { name: '企业转让', selected: false },
      { name: '地址托管', selected: false },
      { name: '疑难业务', selected: false },
      { name: '企业信用', selected: false },
      { name: '异常处理', selected: false },
      { name: '资质代办', selected: false },
      { name: '知识产权', selected: false },
      { name: '高新技术', selected: false },
      { name: '老板圈层', selected: false }
    ],
    // 产品关联相关
    linkedProducts: [], // 默认无关联商品
    productList: [],
    searchKeyword: '',
    // 地区选择相关
    regionValue: ['全部', '全部', '全部'], // 默认为全国
    region: null, // 默认不选择具体地区
    regionText: '全国' // 显示文本
  },

  onLoad: function (options) {
    const app = getApp();
    const globalData = app.globalData || {};
    // 强制同步本地token和userInfo到全局
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    if (token && userInfo) {
      globalData.isLogin = true;
      globalData.userInfo = userInfo;
      this.setData({ isLogin: true });
    }
    this.checkLoginStatus();
    this.getProducts();

    // 默认选中"企业服务"
    this.setData({
      topics: ['企业服务']
    });
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    const globalData = app.globalData || {};
    if (globalData.isLogin && globalData.userInfo) {
      this.setData({
        isLogin: true
      });
    } else {
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          globalData.userInfo = res.data;
          globalData.isLogin = true;
          this.setData({
            isLogin: true
          });
        }
      });
    }
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 获取热门话题
  getHotTopics: function() {
    // 使用本地数据
    this.setData({
      hotTopics: [
        { name: '企业服务', selected: false },
        { name: '工商注册', selected: false },
        { name: '税务合规', selected: false },
        { name: '企业转让', selected: false },
        { name: '地址托管', selected: false },
        { name: '疑难业务', selected: false },
        { name: '企业信用', selected: false },
        { name: '异常处理', selected: false },
        { name: '资质代办', selected: false },
        { name: '知识产权', selected: false },
        { name: '高新技术', selected: false },
        { name: '老板圈层', selected: false }
      ]
    });
  },

  // 输入内容
  inputContent: function(e) {
    const value = e.detail.value;
    const maxLength = this.data.maxContentLength;

    // 限制输入字数
    if (value.length <= maxLength) {
      this.setData({
        content: value
      });
    } else {
      // 超出字数限制，截取前maxLength个字符
      this.setData({
        content: value.substring(0, maxLength)
      });

      // 提示用户
      wx.showToast({
        title: `最多输入${maxLength}个字`,
        icon: 'none'
      });
    }
  },

  // 选择图片
  chooseImage: function() {
    const { images, maxImageCount } = this.data;
    const remainCount = maxImageCount - images.length;

    if (remainCount <= 0) {
      wx.showToast({
        title: `最多选择${maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFiles = res.tempFiles;
        // 只保存本地路径，不上传
        this.setData({
          images: images.concat(tempFiles.map(file => file.tempFilePath))
        });
      }
    });
  },

  // 选择视频
  chooseVideo: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        const tempFile = res.tempFiles[0];

        // 检查视频大小是否超过5MB
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (tempFile.size > maxSize) {
          wx.showToast({
            title: '视频大小不能超过5MB',
            icon: 'none'
          });
          return;
        }

        this.setData({
          video: {
            tempFilePath: tempFile.tempFilePath,
            duration: tempFile.duration,
            size: tempFile.size,
            height: tempFile.height,
            width: tempFile.width
          },
          images: [] // 清空图片，视频和图片不能同时存在
        });
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;

    wx.previewImage({
      current: images[index],
      urls: images
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;

    images.splice(index, 1);

    this.setData({
      images: images
    });
  },

  // 删除视频
  deleteVideo: function() {
    this.setData({
      video: null
    });
  },

  // 显示话题选择器
  showTopicSelector: function() {
    this.setData({
      showTopicSelector: true
    });
  },

  // 隐藏话题选择器
  hideTopicSelector: function() {
    this.setData({
      showTopicSelector: false
    });
  },

  // 选择主题
  selectTopic: function(e) {
    const topic = (e.currentTarget.dataset.topic || '').trim();
    let hotTopics = this.data.hotTopics.map(item => {
      if (item.name === topic) {
        return { ...item, selected: !item.selected };
      }
      return item;
    });
    // 限制最多选3个
    const selectedCount = hotTopics.filter(item => item.selected).length;
    if (selectedCount > 3) {
      wx.showToast({
        title: '最多只能选择3个主题',
        icon: 'none'
      });
      return;
    }
    this.setData({
      hotTopics,
      topics: hotTopics.filter(item => item.selected).map(item => item.name)
    });
  },

  // 输入话题
  inputTopic: function(e) {
    this.setData({
      topic: e.detail.value
    });
  },

  // 显示位置选择器
  showLocationSelector: function() {
    this.setData({
      showLocationSelector: true
    });
  },

  // 隐藏位置选择器
  hideLocationSelector: function() {
    this.setData({
      showLocationSelector: false
    });
  },

  // 获取当前位置
  getCurrentLocation: function() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const latitude = res.latitude;
        const longitude = res.longitude;

        // 逆地址解析
        wx.request({
          url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=YOUR_KEY`,
          success: (res) => {
            if (res.data.status === 0) {
              const result = res.data.result;

              this.setData({
                location: {
                  name: result.formatted_addresses.recommend,
                  address: result.address,
                  latitude: latitude,
                  longitude: longitude
                },
                showLocationSelector: false
              });
            }
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none'
        });
      }
    });
  },

  // 清除位置
  clearLocation: function() {
    this.setData({
      location: null
    });
  },

  // 显示权限设置
  showPermissionSelector: function() {
    this.setData({
      showPermissionSelector: true
    });
  },

  // 隐藏权限设置
  hidePermissionSelector: function() {
    this.setData({
      showPermissionSelector: false
    });
  },

  // 切换公开设置
  togglePublic: function() {
    this.setData({
      isPublic: !this.data.isPublic
    });
  },

  // 切换评论设置
  toggleAllowComment: function() {
    this.setData({
      allowComment: !this.data.allowComment
    });
  },

  // 切换转发设置
  toggleAllowForward: function() {
    this.setData({
      allowForward: !this.data.allowForward
    });
  },

  // 取消发布
  cancel: function() {
    wx.showModal({
      title: '提示',
      content: '确定要放弃编辑吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  // 页面导航栏返回按钮点击事件
  onNavigateBack: function() {
    this.cancel();
  },

  // 清空内容
  clearContent: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清空所有内容吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            content: '',
            images: [],
            video: null,
            topics: [],
            location: null,
            linkedProducts: []
          });

          wx.showToast({
            title: '内容已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 发布
  publish: function() {
    const app = getApp();
    // 再次强制同步本地token和userInfo到全局和data，防止误判
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    console.log('发布前检查用户信息:', userInfo);

    if (token && userInfo) {
      // 确保用户信息中有id字段
      if (!userInfo.id && userInfo._id) {
        console.log('用户信息中使用_id替代id，正在规范化');
        userInfo.id = userInfo._id;
        // 更新存储
        wx.setStorageSync('userInfo', userInfo);
      }

      app.globalData.isLogin = true;
      app.globalData.userInfo = userInfo;
      this.setData({ isLogin: true });
    }

    if (this.data.submitting) {
      return;
    }

    if (!this.data.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再发布内容',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/auth/auth'
            });
          }
        }
      });
      return;
    }

    // 再次检查用户信息是否包含id
    const currentUserInfo = app.globalData.userInfo || userInfo;
    if (!currentUserInfo || !currentUserInfo.id) {
      console.error('用户信息中缺少id字段:', currentUserInfo);
      wx.showModal({
        title: '用户信息异常',
        content: '您的登录状态异常，请重新登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/auth/auth'
            });
          }
        }
      });
      return;
    }

    let topics = this.data.topics;
    if (!topics || topics.length === 0) {
      topics = ['企业服务'];
    }

    const { content, images, video, location, isPublic, allowComment, allowForward, linkedProducts, region } = this.data;

    if (!content.trim() && !images.length && !video) {
      wx.showToast({
        title: '请输入内容或上传图片/视频',
        icon: 'none'
      });
      return;
    }

    // 禁用发布按钮，防止重复点击
    this.setData({ submitting: true });

    wx.showLoading({
      title: '发布中...',
      mask: true
    });

    console.log('点击发布时images:', images);
    // 1. 先上传所有本地图片
    const uploadImages = async () => {
      if (!images || images.length === 0) return [];
      const uploadTasks = images.map((img, idx) => {
        const ext = img.split('.').pop();
        const cloudPath = `post_images/${Date.now()}_${idx}.${ext}`;
        return wx.cloud.uploadFile({
          cloudPath,
          filePath: img,
          config: { env: 'prod-5geioww562624006' }
        }).then(res => res.fileID);
      });
      return Promise.all(uploadTasks);
    };

    // 2. 如果有视频，也上传视频，获取URL
    const uploadVideo = async () => {
      if (!video || !video.tempFilePath) return '';
      const filePath = video.tempFilePath;
      console.log('上传视频路径:', filePath);
      console.log('视频信息:', {
        size: video.size,
        duration: video.duration,
        height: video.height,
        width: video.width
      });

      // 显示上传中的提示
      wx.showLoading({
        title: '视频上传中...',
        mask: true
      });

      try {
        // 使用与图片相同的方式上传视频
        const ext = filePath.split('.').pop().toLowerCase();
        console.log('视频文件格式:', ext);

        // 检查是否为支持的视频格式
        const supportedFormats = ['mp4', 'mov', 'avi', 'wmv'];
        if (!supportedFormats.includes(ext)) {
          wx.hideLoading();
          wx.showToast({
            title: '不支持的视频格式，请使用MP4格式',
            icon: 'none',
            duration: 3000
          });
          throw new Error('不支持的视频格式');
        }

        const cloudPath = `post_videos/${Date.now()}.${ext}`;
        console.log('上传到云存储路径:', cloudPath);

        // 上传视频文件
        console.log('开始上传视频文件...');

        // 注意: wx.cloud.uploadFile 不支持 onProgressUpdate
        const res = await wx.cloud.uploadFile({
          cloudPath,
          filePath: filePath,
          config: { env: 'prod-5geioww562624006' }
        });

        wx.hideLoading();
        console.log('视频上传成功:', res);
        return res.fileID;
      } catch (err) {
        wx.hideLoading();
        console.error('视频上传失败:', err);

        // 显示更详细的错误信息
        wx.showModal({
          title: '视频上传失败',
          content: `原因: ${err.message || err.errMsg || '未知错误'}`,
          showCancel: false
        });

        throw err;
      }
    };

    // 3. 执行上传并发帖
    Promise.all([uploadImages(), uploadVideo()])
      .then(([imageUrls, videoUrl]) => {
        // 再次检查登录状态和token
        const token = wx.getStorageSync('token');
        const userInfo = wx.getStorageSync('userInfo');

        if (!token || !userInfo || !userInfo.id) {
          console.error('发帖前检查：用户未登录或缺少必要信息');
          wx.hideLoading();
          wx.showModal({
            title: '登录状态异常',
            content: '请重新登录后再发布',
            confirmText: '去登录',
            success: (res) => {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/auth/auth'
                });
              }
            }
          });
          throw new Error('用户未登录或缺少必要信息');
        }

        // 保留单个topic字段以兼容旧版后端，同时添加topics数组字段
        let topic = '';
        let topicsToSend = [];
        
        if (Array.isArray(topics) && topics.length > 0) {
          // 使用整个主题数组
          topicsToSend = [...topics];
          // 同时保留第一个主题作为兼容性字段
          topic = topics[0];
        } else if (typeof topics === 'string' && topics) {
          topic = topics;
          topicsToSend = [topics];
        } else {
          topic = '企业服务';
          topicsToSend = ['企业服务'];
        }
        // 地区处理：未选时自动设为"全国"
        let regionValue = region;
        if (!regionValue || (Array.isArray(regionValue) && regionValue[0] === '全部')) {
          regionValue = '全国';
        }
        const postData = {
          content: content.trim(),
          images: imageUrls, // 存URL
          topic: topic, // 兼容旧版后端
          topics: topicsToSend, // 新增：传递多个主题
          location: location, // 保持原始结构
          isPublic: isPublic,
          allowComment: allowComment,
          allowForward: allowForward,
          region: regionValue, // 自动补"全国"
          userId: userInfo.id // 显式添加用户ID
        };
        if (linkedProducts && linkedProducts.length > 0) {
          postData.product = linkedProducts[0]; // 保持原始结构
        }
        if (videoUrl) {
          postData.video = videoUrl; // 存URL
        }
        // 过滤掉 undefined/null/空字符串 字段
        Object.keys(postData).forEach(key => {
          if (postData[key] === undefined || postData[key] === null || postData[key] === '') {
            delete postData[key];
          }
        });
        console.log('最终发布数据:', postData);
        // 增强日志，输出每种类型的发帖参数类型和内容
        console.log('【发帖类型检查】content:', typeof content, content);
        console.log('【发帖类型检查】images:', Array.isArray(imageUrls), imageUrls);
        console.log('【发帖类型检查】video:', typeof videoUrl, videoUrl);
        console.log('【发帖类型检查】topic:', typeof topic, topic);
        console.log('【发帖类型检查】location:', typeof location, location);
        console.log('【发帖类型检查】region:', typeof regionValue, regionValue);
        console.log('【发帖类型检查】product:', typeof (linkedProducts && linkedProducts[0]), linkedProducts && linkedProducts[0]);
        return postApi.createPost(postData);
      })
      .then(res => {
        wx.hideLoading();
        if (res.success) {
          wx.showToast({
            title: '发布成功',
            icon: 'success'
          });
          this.setData({
            content: '',
            images: [],
            video: null,
            topics: [],
            location: null,
            linkedProducts: []
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.message || '发布失败');
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('发布失败:', err);
        wx.showToast({
          title: err.message || '发布失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        // 恢复发布按钮
        this.setData({ submitting: false });
      });
  },

  // 获取商品列表
  getProducts: function() {
    // 只使用API获取商品数据，不再回退本地数据
    productApi.getProducts({
      page: 1,
      pageSize: 100, // 获取更多商品
      sortType: 'sales'
    })
      .then(res => {
        if (res.success && res.data && (Array.isArray(res.data.list) ? res.data.list.length > 0 : res.data.length > 0)) {
          // 兼容两种数据结构
          let products = Array.isArray(res.data.list) ? res.data.list : res.data;
          // 处理商品数据，将 images 字符串转换为数组
          products = products.map(product => {
            if (product.images && typeof product.images === 'string') {
              try {
                product.images = JSON.parse(product.images);
              } catch (e) {
                product.images = [];
              }
            }
            if (!product._id && product.id) {
              product._id = product.id;
            }
            return product;
          });
          this.setData({
            productList: products
          });
        } else {
          this.setData({
            productList: []
          });
        }
      })
      .catch(err => {
        console.error('获取商品数据失败', err);
        this.setData({
          productList: []
        });
      });
  },

  // 搜索商品
  searchProducts: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });

    if (keyword.trim() === '') {
      this.getProducts();
      return;
    }

    // 只用API搜索商品
    productApi.getProducts({
      page: 1,
      pageSize: 100,
      keyword: keyword
    })
      .then(res => {
        if (res.success && res.data && (Array.isArray(res.data.list) ? res.data.list.length > 0 : res.data.length > 0)) {
          let products = Array.isArray(res.data.list) ? res.data.list : res.data;
          products = products.map(product => {
            if (product.images && typeof product.images === 'string') {
              try {
                product.images = JSON.parse(product.images);
              } catch (e) {
                product.images = [];
              }
            }
            if (!product._id && product.id) {
              product._id = product.id;
            }
            return product;
          });
          this.setData({
            productList: products
          });
        } else {
          this.setData({
            productList: []
          });
        }
      })
      .catch(err => {
        console.error('搜索商品失败', err);
    this.setData({
          productList: []
        });
    });
  },

  // 显示产品选择器
  showProductSelector: function() {
    this.setData({
      showProductSelector: true
    });
  },

  // 隐藏产品选择器
  hideProductSelector: function() {
    this.setData({
      showProductSelector: false,
      searchKeyword: ''
    });

    // 重新加载商品列表
    this.getProducts();
  },

  // 选择产品
  selectProduct: function(e) {
    const productId = e.currentTarget.dataset.id;
    const product = this.data.productList.find(p => p._id === productId);

    if (product) {
      // 限制最多关联一个商品，直接替换
      this.setData({
        linkedProducts: [product], // 只保留一个商品
        showProductSelector: false, // 自动关闭选择器
        searchKeyword: '' // 清空搜索关键词
      });

      wx.showToast({
        title: '已选择商品',
        icon: 'success'
      });
    }
  },

  // 移除关联产品
  removeLinkedProduct: function(e) {
    console.log('移除按钮被点击');

    // 阻止事件冒泡，防止触发商品卡片的点击事件
    e.stopPropagation();

    const index = e.currentTarget.dataset.index;
    console.log('要移除的商品索引:', index);

    wx.showModal({
      title: '提示',
      content: '确定要移除关联的商品吗？',
      confirmColor: '#FF4D4F',
      success: (res) => {
        console.log('对话框结果:', res);

        if (res.confirm) {
          console.log('移除前的linkedProducts:', this.data.linkedProducts);

          const linkedProducts = [...this.data.linkedProducts];
          linkedProducts.splice(index, 1);

          console.log('处理后的linkedProducts:', linkedProducts);

          this.setData({
            linkedProducts: linkedProducts
          }, () => {
            // 在回调中确认数据已更新
            console.log('移除商品后的linkedProducts:', this.data.linkedProducts);

            wx.showToast({
              title: '已移除商品',
              icon: 'success'
            });
          });
        }
      }
    });
  },

  // 切换卡片选项卡
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 备用的移除商品方法
  clearLinkedProducts: function() {
    console.log('清空关联商品');
    this.setData({
      linkedProducts: []
    }, () => {
      console.log('已清空关联商品');
      wx.showToast({
        title: '已移除商品',
        icon: 'success'
      });
    });
  },

  // 处理地区选择器变化
  onRegionChange: function(e) {
    const regionValue = e.detail.value;
    const regionText = this.formatRegionText(regionValue);

    // 如果选择的是全国，则不设置具体地区
    if (regionValue[0] === '全部') {
      this.setData({
        region: null,
        regionValue: regionValue,
        regionText: regionText
      });
      return;
    }

    // 构建地区对象
    let region = null;

    // 如果选择了具体省份
    if (regionValue[0] !== '全部') {
      region = {
        province: {
          name: regionValue[0],
          code: ''
        }
      };

      // 如果选择了具体城市
      if (regionValue[1] !== '全部') {
        region.city = {
          name: regionValue[1],
          code: ''
        };

        // 如果选择了具体区县
        if (regionValue[2] !== '全部') {
          region.district = {
            name: regionValue[2],
            code: ''
          };
        }
      }
    }

    this.setData({
      region: region,
      regionValue: regionValue,
      regionText: regionText
    });

    console.log('选择的地区:', region);
  },

  // 格式化区域文本
  formatRegionText: function(region) {
    if (region[0] === '全部') {
      return '全国';
    } else if (region[1] === '全部') {
      return region[0];
    } else if (region[2] === '全部') {
      return region[0] + ' ' + region[1];
    } else {
      return region[0] + ' ' + region[1] + ' ' + region[2];
    }
  },

  // 清除地区
  clearRegion: function() {
    this.setData({
      region: null,
      regionValue: ['全部', '全部', '全部'],
      regionText: '全国',
      selectedProvince: null,
      selectedCity: null
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '发布新内容',
      path: '/pages/publish/publish'
    };
  }
});

const { groupApi } = require('../../utils/api');
const { formatTime } = require('../../utils/util');
const app = getApp();

Page({
  data: {
    groupId: '',
    group: null,
    messages: [],
    inputMessage: '',
    loading: false,
    page: 1,
    pageSize: 20,
    hasMore: true,
    scrollToView: '',
    userInfo: null
  },

  onLoad: function(options) {
    const { groupId } = options;
    const userInfo = getApp().globalData.userInfo;

    console.log('群聊页面加载:', { groupId, userInfo });

    this.setData({
      groupId,
      userInfo
    });

    this.loadGroupInfo();
    this.loadMessages();
  },

  // 加载群组信息
  loadGroupInfo: function() {
    groupApi.getGroupDetail(this.data.groupId).then(res => {
      if (res.success) {
        this.setData({ group: res.data });
        // 设置导航栏标题
        wx.setNavigationBarTitle({
          title: res.data.name
        });
      }
    }).catch(err => {
      console.error('获取群组信息失败:', err);
    });
  },

  // 检查成员身份并加载消息
  checkMembershipAndLoadMessages: function() {
    // 先检查用户是否为群组成员
    groupApi.getGroupMembers(this.data.groupId).then(res => {
      if (res.success) {
        const members = res.data;
        const currentUserId = this.data.userInfo.id;
        const isMember = members.some(member => member.id === currentUserId || member.userId === currentUserId);

        console.log('群组成员检查:', {
          currentUserId,
          members: members.map(m => ({ id: m.id, userId: m.userId, nickname: m.nickname })),
          isMember
        });

        if (isMember) {
          this.loadMessages();
        } else {
          console.log('用户不是群组成员，无法加载消息');
          wx.showToast({
            title: '您还不是群组成员',
            icon: 'none'
          });
        }
      } else {
        console.error('获取群组成员失败:', res);
        // 即使获取成员失败，也尝试加载消息
        this.loadMessages();
      }
    }).catch(err => {
      console.error('检查群组成员身份失败:', err);
      // 出错时也尝试加载消息
      this.loadMessages();
    });
  },

  // 加载消息历史
  loadMessages: function() {
    if (this.data.loading || !this.data.hasMore) return;

    // 检查必要的参数
    if (!this.data.groupId) {
      console.error('群组ID为空，无法加载消息');
      wx.showToast({
        title: '群组ID无效',
        icon: 'none'
      });
      return;
    }

    // 检查用户登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      console.error('用户未登录，无法加载消息');
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }



    this.setData({ loading: true });

    groupApi.getGroupMessages(this.data.groupId, this.data.page, this.data.pageSize).then(res => {
      if (res && res.success) {
        const messages = (res.data || []).map(msg => ({
          ...msg,
          displayTime: formatTime(new Date(msg.createTime))
        }));

        this.setData({
          messages: [...messages, ...this.data.messages],
          page: this.data.page + 1,
          hasMore: messages.length === this.data.pageSize
        });
      } else {
        const errorMsg = res && res.message ? res.message : '获取消息失败';
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取群消息失败:', err);

      let errorMsg = '获取消息失败';
      if (err && err.message) {
        errorMsg = err.message;
      } else if (err && err.data && err.data.message) {
        errorMsg = err.data.message;
      }

      wx.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }).finally(() => {
      this.setData({ loading: false });
    });
  },

  // 加载更多消息
  onLoadMore: function() {
    this.loadMessages();
  },

  // 输入消息
  onInput: function(e) {
    this.setData({
      inputMessage: e.detail.value
    });
  },

  // 发送消息
  sendMessage: function() {
    const { inputMessage } = this.data;
    if (!inputMessage.trim()) return;

    this.sendTextMessage(inputMessage.trim());
  },

  // 发送文本消息
  sendTextMessage: function(content) {
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    groupApi.sendGroupMessage(this.data.groupId, content, 'text').then(res => {
      if (res.success) {
        const message = {
          ...res.data,
          nickname: this.data.userInfo.nickname,
          avatar: this.data.userInfo.avatar,
          displayTime: formatTime(new Date(res.data.createTime)),
          type: 'text'
        };

        this.setData({
          messages: [...this.data.messages, message],
          inputMessage: '',
          scrollToView: `msg-${message.id}`
        });
      } else {
        wx.showToast({
          title: res.message || '发送失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('发送消息失败:', err);
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }).finally(() => {
      wx.hideLoading();
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.uploadAndSendImage(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  // 上传并发送图片
  uploadAndSendImage: function(filePath) {
    wx.showLoading({
      title: '发送中...',
      mask: true
    });

    // 上传图片
    wx.uploadFile({
      url: app.globalData.baseUrl + '/api/upload/image',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token')
      },
      success: (uploadRes) => {
        try {
          const data = JSON.parse(uploadRes.data);
          if (data.success) {
            // 发送图片消息
            groupApi.sendGroupMessage(this.data.groupId, data.data.url, 'image').then(res => {
              if (res.success) {
                const message = {
                  ...res.data,
                  nickname: this.data.userInfo.nickname,
                  avatar: this.data.userInfo.avatar,
                  displayTime: formatTime(new Date(res.data.createTime)),
                  type: 'image'
                };

                this.setData({
                  messages: [...this.data.messages, message],
                  scrollToView: `msg-${message.id}`
                });
              } else {
                wx.showToast({
                  title: res.message || '发送失败',
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('发送图片消息失败:', err);
              wx.showToast({
                title: '发送失败',
                icon: 'none'
              });
            });
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
          }
        } catch (e) {
          console.error('解析上传结果失败:', e);
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('上传图片失败:', err);
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 预览图片
  previewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      page: 1,
      hasMore: true,
      messages: []
    });
    this.loadMessages();
    wx.stopPullDownRefresh();
  }
});
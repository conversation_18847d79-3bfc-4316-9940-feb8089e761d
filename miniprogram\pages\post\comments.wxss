.post-comments-container {
  padding: 8px;
  padding-bottom: 60px;
  background-color: #f5f5f5;
}
.post-detail-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.post-content {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 6px;
  padding: 0;
  background: transparent;
}
.comment-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 6px;
  margin-bottom: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}
.avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}
.comment-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}
.nickname {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.comment-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}
.comment-content {
  font-size: 15px;
  color: #444;
  margin-top: 2px;
}
.loading, .empty {
  text-align: center;
  color: #999;
  margin-top: 40px;
}
.input-bar-outer {
  width: 100%;
  background: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
  padding: 0;
  border: none;
}
.comment-input-bar {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 8px 10px 18px 10px;
  margin: 0;
  border: none;
  box-shadow: none;
}
.input-area {
  flex: 0 0 75%;
  max-width: 75%;
  display: flex;
  align-items: center;
}
.btn-area {
  flex: 0 0 25%;
  max-width: 25%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 8px;
}
.comment-input {
  width: 100%;
  height: 36px;
  border: none;
  border-radius: 18px;
  padding: 0 12px;
  font-size: 15px;
  background: #f5f5f5;
  color: #222;
  font-family: inherit;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.submit-btn {
  min-width: 56px;
  max-width: 100%;
  height: 36px;
  background: none;
  color: #00b26a; /* 更改为更鲜明的绿色 */
  border: none;
  border-radius: 18px;
  font-size: 17px;
  font-weight: bold;
  padding: 0 16px;
  margin-left: 0;
  margin-right: 0;
  align-self: center;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: none;
  transition: background 0.2s;
  white-space: nowrap;
  overflow: visible;
  text-overflow: initial;
}
.submit-btn:active {
  background: #179b16;
}
.submit-btn[disabled] {
  color: #ccc !important;
  background: #f5f5f5 !important;
}

.follow-btn {
  padding: 6px 16px;
  font-size: 14px;
  color: #e74c3c;
  background-color: #fef5f5;
  border: 1px solid #e74c3c;
  border-radius: 18px;
  margin-left: 10px;
  flex-shrink: 0;
  font-weight: bold;
}

.follow-btn.followed {
  color: #999;
  background-color: #f5f5f5;
  border-color: #ddd;
}

.follow-btn.disabled {
  color: #ccc;
  background-color: #f5f5f5;
  border-color: #eee;
  pointer-events: none;
}
.post-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.post-user-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.post-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.comment-user-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 4px;
}
.post-avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.post-nickname {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  display: block;
}
.post-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  display: block;
}
.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 6px 0 8px 0;
}
.post-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  background: #f2f2f2;
}
.single-image-wrapper {
  margin: 3px 0 4px 0;
  border-radius: 8px;
  overflow: hidden;
}
.single-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
}
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3px;
  margin: 3px 0 4px 0;
}
.grid-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}
.post-products {
  margin: 4px 0;
  background: #f7f7f7;
  border-radius: 6px;
  padding: 6px;
}
.product-link {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 4px;
  padding: 4px;
  margin-top: 3px;
}
.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 12px;
  background-color: #f5f5f5;
  object-fit: cover;
}
.product-info {
  flex: 1;
}
.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
.product-price {
  font-size: 15px;
  color: #ff4d4f;
  font-weight: bold;
}
.buy-btn {
  background: #ff4d4f;
  color: #fff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 12px;
  margin-left: 8px;
}
.post-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 3px;
  margin-bottom: 1px;
  gap: 3px;
}
.post-tag {
  font-size: 12px;
  color: #666666;
  background-color: #F5F5F5;
  padding: 3px 10px;
  border-radius: 10px;
  display: inline-block;
  border: 1px solid #EEEEEE;
  max-width: fit-content;
}
.post-location {
  color: #666;
  font-size: 12px;
  margin: 2px 0;
}
.post-region {
  color: #666;
  font-size: 14px;
  margin: 8px 0;
}
.interaction-bar {
  display: flex;
  height: 36px;
  border-top: 1px solid #F5F5F5;
  padding-top: 4px;
  padding-bottom: 2px;
  margin-top: 2px;
}
.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.interaction-item image {
  width: 21px;
  height: 21px;
  margin-right: 4px;
}
.interaction-item text {
  font-size: 14px;
  color: #666666;
  line-height: 1;
} 
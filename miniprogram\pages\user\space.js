const { postApi, userApi } = require('../../utils/api');
const app = getApp();

Page({
  data: {
    userId: '',
    userInfo: {},
    posts: [],
    isFollowed: false,
    globalUserInfo: {}
  },

  onLoad: function(options) {
    // 优先从options获取userId，其次从全局userInfo，再次从storage
    let userId = options.id || (app.globalData.userInfo && app.globalData.userInfo.id) || wx.getStorageSync('viewUserId') || '';
    if (!userId) {
      wx.showToast({ title: '无法获取用户ID', icon: 'none' });
      return;
    }
    wx.setStorageSync('viewUserId', userId);
    this.setData({ userId, globalUserInfo: app.globalData.userInfo || {} });
    this.loadUserInfo(userId);
    this.loadUserPosts(userId);
    if (app.globalData.isLogin) {
      this.checkFollowStatus(userId);
    } else {
      this.setData({ isFollowed: false });
    }
  },

  loadUserInfo: function(userId) {
    userApi.getUserInfo(userId).then(res => {
      if (res.success && res.data) {
        // 兼容 avatar/avatarUrl, nickname/nickName
        const info = res.data || {};
        if (!info.avatarUrl && info.avatar) info.avatarUrl = info.avatar;
        if (!info.avatar && info.avatarUrl) info.avatar = info.avatarUrl;
        if (!info.nickName && info.nickname) info.nickName = info.nickname;
        if (!info.nickname && info.nickName) info.nickname = info.nickName;
        if (!info.id && info.userId) info.id = info.userId;
        this.setData({ userInfo: info });
      } else {
        this.setData({ userInfo: { nickName: '未知用户', id: userId } });
        wx.showToast({ title: '用户信息加载失败', icon: 'none' });
      }
    }).catch(() => {
      this.setData({ userInfo: { nickName: '未知用户', id: userId } });
      wx.showToast({ title: '用户信息加载失败', icon: 'none' });
    });
  },

  loadUserPosts: function(userId) {
    postApi.getPosts({ userId }).then(res => {
      let posts = [];
      if (res.success) {
        posts = Array.isArray(res.data) ? res.data : (res.data && res.data.list ? res.data.list : []);
        // 再次用userId过滤，确保只显示该用户自己的帖子
        posts = posts.filter(post => String(post.userId) === String(userId));
        // 收集所有fileID（图片和视频）
        let allFileIDs = [];
        posts.forEach(post => {
          if (typeof post.images === 'string') {
            try { post.images = JSON.parse(post.images); } catch (e) { post.images = []; }
          }
          if (!Array.isArray(post.images)) post.images = [];
          allFileIDs = allFileIDs.concat(post.images.filter(fid => typeof fid === 'string' && fid.startsWith('cloud:')));
          if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud:')) {
            allFileIDs.push(post.video);
          }
          post.images = post.images.filter(img => typeof img === 'string' && img.trim() !== '');
          post.displayTime = this.formatTime(post.createTime);

          // 处理多主题数组 - 增强健壮性与安全性
          try {
            // 首先确保topic字段存在且有效
            if (!post.topic || post.topic === '') {
              post.topic = '企业服务';
            }
            
            // 处理topics字段 - 更严格的检查
            if (post.topics) {
              if (typeof post.topics === 'string') {
                // 提前检查是否是有效的JSON格式
                const trimmedTopics = post.topics.trim();
                if (trimmedTopics === '' || trimmedTopics === '[]' || trimmedTopics === '{}' || 
                    trimmedTopics === 'null' || trimmedTopics === 'undefined') {
                  // 明确无效的值，直接使用默认主题
                  post.topicsArray = [post.topic];
                  // 使用默认主题
                } else {
                  // 检查是否是有效的JSON格式（以 [ 开头或 " 开头）
                  const firstChar = trimmedTopics.charAt(0);
                  if (firstChar === '[' || firstChar === '"') {
                    try {
                      // 尝试解析JSON字符串
                      const parsed = JSON.parse(trimmedTopics);
                      if (Array.isArray(parsed) && parsed.length > 0) {
                        // 有效数组
                        post.topicsArray = parsed.map(t => String(t)).filter(t => t.trim() !== '');
                        if (post.topicsArray.length === 0) {
                          post.topicsArray = [post.topic];
                        }
                      } else if (typeof parsed === 'string' && parsed.trim() !== '') {
                        // 单个字符串
                        post.topicsArray = [parsed];
                      } else if (parsed === null || parsed === undefined) {
                        // null或undefined
                        post.topicsArray = [post.topic];
                      } else {
                        // 其他情况回退到单个主题
                        post.topicsArray = [post.topic];
                      }
                    } catch (e) {
                      // 解析topics字段失败
                      // 解析失败，直接使用单个主题
                      post.topicsArray = [post.topic];
                    }
                  } else {
                    // 不是有效的JSON格式，直接作为单个主题使用
                    post.topicsArray = [trimmedTopics];
                    // 非JSON格式topics处理
                  }
                }
              } else if (Array.isArray(post.topics)) {
                // 已经是数组，过滤空值和非字符串值
                post.topicsArray = post.topics
                  .map(t => t !== null && t !== undefined ? String(t) : null)
                  .filter(t => t && t.trim() !== '');
                if (post.topicsArray.length === 0) {
                  post.topicsArray = [post.topic];
                }
              } else if (post.topics === null || post.topics === undefined) {
                // null或undefined
                post.topicsArray = [post.topic];
              } else {
                // 其他类型，尝试转换为字符串
                try {
                  const topicStr = String(post.topics).trim();
                  post.topicsArray = topicStr ? [topicStr] : [post.topic];
                } catch {
                  post.topicsArray = [post.topic];
                }
              }
            } else {
              // 没有topics字段，使用单个主题
              post.topicsArray = [post.topic];
            }
            
            // 最后确保至少有一个有效的主题
            if (!post.topicsArray || !Array.isArray(post.topicsArray) || post.topicsArray.length === 0) {
              post.topicsArray = ['企业服务'];
            }
          } catch (err) {
            // 捕获所有异常，确保不会影响整体渲染
            // 处理主题时发生异常
            post.topicsArray = [post.topic || '企业服务'];
          }
        });
        if (allFileIDs.length > 0) {
          wx.cloud.getTempFileURL({
            fileList: allFileIDs,
            config: { env: 'prod-5geioww562624006' },
            success: res2 => {
              const urlMap = {};
              res2.fileList.forEach(item => {
                urlMap[item.fileID] = item.tempFileURL;
              });
              posts.forEach(post => {
                if (Array.isArray(post.images)) {
                  post.images = post.images.map(fid => (urlMap[fid] && urlMap[fid].startsWith('https')) ? urlMap[fid] : '').filter(img => typeof img === 'string' && img.startsWith('https'));
                }
                if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud:')) {
                  post.video = (urlMap[post.video] && urlMap[post.video].startsWith('https')) ? urlMap[post.video] : '';
                }
                // 只保留https外链
                if (Array.isArray(post.images)) {
                  post.images = post.images.filter(img => typeof img === 'string' && img.startsWith('https'));
                }
                if (post.video && typeof post.video === 'string' && !post.video.startsWith('https')) {
                  post.video = '';
                }
              });
              // 增强调试输出
              // 最终渲染用户空间posts图片/视频URL
              this.setData({ posts });
              if (!posts.length) {
                wx.showToast({ title: '暂无动态', icon: 'none' });
              }
            },
            fail: err => {
              posts.forEach(post => {
                if (Array.isArray(post.images)) post.images = [];
                if (post.video) post.video = '';
              });
              this.setData({ posts });
              if (!posts.length) {
                wx.showToast({ title: '暂无动态', icon: 'none' });
              }
            }
          });
        } else {
          this.setData({ posts });
          if (!posts.length) {
            wx.showToast({ title: '暂无动态', icon: 'none' });
          }
        }
        return;
      }
      this.setData({ posts: [] });
      wx.showToast({ title: '动态加载失败', icon: 'none' });
    }).catch(() => {
      this.setData({ posts: [] });
      wx.showToast({ title: '动态加载失败', icon: 'none' });
    });
  },

  onContactTap: function() {
    const userId_contact = this.data.userId;
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: `/pages/auth/auth?redirect=${encodeURIComponent('/pages/user/space?id=' + userId_contact)}`
            });
          }
        }
      });
      return;
    }
    const { phone, wechat } = this.data.userInfo;
    wx.showModal({
      title: '联系方式',
      content: `手机号：${phone || '暂无'}\n微信号：${wechat || '暂无'}`,
      showCancel: false
    });
  },
  onFollowTap: function() {
    // 点击关注按钮
    const userId_follow = this.data.userId;
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: `/pages/auth/auth?redirect=${encodeURIComponent('/pages/user/space?id=' + userId_follow)}`
            });
          }
        }
      });
      return;
    }
    // 直接根据当前isFollowed状态切换
    const { isFollowed } = this.data;
    const api = isFollowed ? userApi.unfollowUser : userApi.followUser;
    api(userId_follow).then(res2 => {
      if (res2.success) {
        this.setData({ isFollowed: !isFollowed });
        wx.showToast({
          title: isFollowed ? '已取消关注' : '已关注',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: (res2.message || res2.errMsg || '操作失败'),
          icon: 'none',
          duration: 3500
        });
        // 关注操作失败
      }
    }).catch(err => {
      wx.showToast({
        title: (err && (err.message || err.errMsg)) ? (err.message || err.errMsg) : '操作失败',
        icon: 'none',
        duration: 3500
      });
      // 关注操作异常
      // 新增：异常时弹窗提示
      wx.showModal({
        title: '关注异常',
        content: (err && (err.message || err.errMsg)) ? (err.message || err.errMsg) : '操作失败',
        showCancel: false
      });
    });
  },
  onMessageTap: function() {
    const userId_msg = this.data.userId;
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({
              url: `/pages/auth/auth?redirect=${encodeURIComponent('/pages/user/space?id=' + userId_msg)}`
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/message/chat?targetId=${this.data.userId}`
    });
  },
  onPostTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/post/detail?id=${id}` });
  },
  formatTime: function(ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    return date.getFullYear() + '-' + (date.getMonth()+1).toString().padStart(2,'0') + '-' + date.getDate().toString().padStart(2,'0') + ' ' + date.getHours().toString().padStart(2,'0') + ':' + date.getMinutes().toString().padStart(2,'0');
  },
  checkFollowStatus: function(followingId) {
    // 只在已登录时才请求关注状态
    if (!app.globalData.isLogin) {
      this.setData({ isFollowed: false });
      return;
    }
    userApi.checkFollowStatus(followingId).then(res => {
      if (res.success) {
        this.setData({ isFollowed: res.data.isFollowing });
      }
    });
  },
  // 点赞
  onLikeTap: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.showToast({ title: '点赞功能待实现', icon: 'none' });
  },
  // 评论
  onCommentTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({ title: '评论功能待实现', icon: 'none' });
  },
  // 转发
  onShareTap: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.showToast({ title: '转发功能待实现', icon: 'none' });
  },
  goToLogin: function() {
    const userId = this.data.userId;
    wx.navigateTo({
      url: `/pages/auth/auth?redirect=${encodeURIComponent('/pages/user/space?id=' + userId)}`
    });
  },
}); 
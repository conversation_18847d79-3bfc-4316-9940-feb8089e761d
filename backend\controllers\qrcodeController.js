const qrcodeService = require('../services/qrcodeService');

exports.getQrcode = async (req, res) => {
  try {
    const { scene } = req.query;
    if (!scene) {
      return res.status(400).json({ success: false, message: '缺少scene参数' });
    }
    const qrcodeUrl = await qrcodeService.generateQrcode(scene);
    res.json({ success: true, qrcodeUrl });
  } catch (error) {
    console.error('生成二维码失败:', error);
    res.status(500).json({ success: false, message: '生成二维码失败', error: error.message });
  }
}; 
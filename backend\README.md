# 猎优企小程序后端

## 数据库适配说明

本项目支持两种数据库：

1. **SQLite**：本地开发和测试使用
2. **MySQL**：微信云托管环境使用

## 环境变量配置

### SQLite 配置

```
DB_PATH=/path/to/your/database.db
```

### MySQL 配置

```
USE_MYSQL=true
DB_HOST=your-mysql-host
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-password
DB_NAME=lieyouqi
```

## 数据库初始化

### 初始化 SQLite 数据库

```bash
npm run init-db
npm run migrate-data
```

### 初始化 MySQL 数据库

```bash
npm run init-mysql-db
```

### 从 SQLite 迁移到 MySQL

如果您已经有 SQLite 数据，可以迁移到 MySQL：

```bash
npm run migrate-to-mysql
```

## 微信云托管部署

### 详细部署步骤

1. **准备工作**
   - 确保代码已提交到Git仓库
   - 确保Dockerfile和container.config.json文件存在

2. **创建MySQL数据库**
   - 在微信云托管控制台中进入"数据库"选项卡
   - 创建MySQL实例
   - 设置数据库名称为 `leiyouqi`
   - 记下数据库密码

3. **部署应用**
   - 在微信云托管控制台创建新服务
   - 选择"代码库部署"
   - 关联你的GitHub仓库
   - 选择部署分支
   - 在环境变量中设置 `MYSQL_PASSWORD` 为你的MySQL数据库密码

4. **初始化数据库**
   - 首次部署后，进入"云托管"控制台
   - 找到你的服务，点击"终端"按钮
   - 在终端中执行：
     ```
     cd /app
     npm run init-mysql-db
     ```

### 常见问题排查

1. **部署失败**
   - 检查Dockerfile是否包含所有必要的依赖（Python, make, g++, gcc）
   - 确保container.config.json配置正确
   - 查看构建日志，找出具体错误

2. **数据库连接失败**
   - 确认环境变量配置正确
   - 检查MySQL实例是否正常运行
   - 验证数据库用户名和密码

3. **应用启动失败**
   - 检查日志输出
   - 确认端口配置正确（应为80）
   - 验证数据库初始化是否完成

## 本地开发

```bash
npm run dev
```

## 生产环境启动

```bash
npm start
```

## Docker 构建

```bash
docker build -t leiyouqi-backend .
docker run -p 80:80 leiyouqi-backend
```

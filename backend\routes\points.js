/**
 * 积分路由
 */
const express = require('express');
const router = express.Router();
const pointsController = require('../controllers/pointsController');
const authMiddleware = require('../middleware/auth');

// 获取用户积分
router.get('/user', authMiddleware.verifyToken, pointsController.getUserPoints);

// 更新用户积分
router.post('/user/update', authMiddleware.verifyToken, pointsController.updateUserPoints);

// 获取用户积分记录
router.get('/user/records', authMiddleware.verifyToken, pointsController.getUserPointsRecords);

// 获取积分配置
router.get('/config', pointsController.getPointsConfig);

// 更新积分配置（需要管理员权限）
router.post('/config/update', authMiddleware.verifyAdmin, pointsController.updatePointsConfig);

module.exports = router;

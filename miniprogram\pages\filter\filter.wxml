<view class="filter-mask" bindtap="cancelFilter" catchtouchmove="preventTouchMove"></view>
<view class="filter-drawer" catchtouchmove="preventTouchMove">
  <view class="filter-header">
    <view class="header-title">筛选</view>
    <view class="header-reset" bindtap="resetFilter">重置</view>
  </view>

  <!-- 区域筛选 -->
  <view class="filter-section">
    <view class="section-title">区域筛选</view>
    <view class="region-selector">
      <picker mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="全部">
        <view class="region-picker">
          <text class="region-text">{{regionText}}</text>
          <view class="region-arrow"></view>
        </view>
      </picker>
    </view>
  </view>

  <!-- 主题筛选 -->
  <view class="filter-section">
    <view class="section-title">主题筛选</view>
    <view class="topic-container">
      <block wx:for="{{topics}}" wx:for-item="topic" wx:key="id">
        <view
          class="topic-tag {{topic.selected ? 'active' : ''}}"
          bindtap="toggleTopic"
          data-id="{{topic.id}}"
        >
          {{topic.name}}
          <view class="check-icon" wx:if="{{topic.selected}}">✓</view>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="filter-footer">
    <view class="footer-btn cancel" bindtap="cancelFilter">取消</view>
    <view class="footer-btn confirm" bindtap="confirmFilter">确定</view>
  </view>
</view>

/**
 * 登录状态管理工具
 * 用于管理登录状态和确保数据一致性
 */

// 登录状态存储键
const LOGIN_STATE_KEY = 'login_state';
const TOKEN_KEY = 'token';
const USER_INFO_KEY = 'userInfo';

/**
 * 保存登录状态
 * @param {Object} userInfo 用户信息
 * @param {String} token 用户令牌
 * @param {Boolean} isLogin 是否登录
 */
function saveLoginState(userInfo, token, isLogin = true) {
  if (!userInfo || !token) {
    // 保存登录状态失败: 无效的用户信息或令牌
    return false;
  }

  // 确保用户信息中有id字段
  if (!userInfo.id && userInfo._id) {
    // 用户信息中使用_id替代id，正在规范化
    userInfo.id = userInfo._id;
  }

  if (!userInfo.id) {
    // 保存登录状态失败: 用户信息中缺少id字段
    return false;
  }

  // 创建登录状态对象
  const loginState = {
    userId: userInfo.id,
    token: token,
    isLogin: isLogin,
    timestamp: Date.now()
  };

  try {
    // 保存登录状态
    wx.setStorageSync(LOGIN_STATE_KEY, loginState);

    // 保存用户信息和令牌
    wx.setStorageSync(USER_INFO_KEY, userInfo);
    wx.setStorageSync(TOKEN_KEY, token);

    // 更新全局数据
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo;
      app.globalData.isLogin = true;
      app.globalData.needRefreshProfile = true;
    }

    // 登录状态已保存
    return true;
  } catch (error) {
    // 保存登录状态失败
    return false;
  }
}

/**
 * 获取登录状态
 * @returns {Object|null} 登录状态对象或null
 */
function getLoginState() {
  try {
    const loginState = wx.getStorageSync(LOGIN_STATE_KEY);
    return loginState || null;
  } catch (error) {
    // 获取登录状态失败
    return null;
  }
}

/**
 * 清除登录状态
 */
function clearLoginState() {
  try {
    wx.removeStorageSync(LOGIN_STATE_KEY);
    wx.removeStorageSync(TOKEN_KEY);
    wx.removeStorageSync(USER_INFO_KEY);
    // 登录状态已清除
    return true;
  } catch (error) {
    // 清除登录状态失败
    return false;
  }
}

/**
 * 验证登录状态
 * @returns {Promise} 包含验证结果的Promise
 */
function validateLoginState() {
  return new Promise((resolve) => {
    const loginState = getLoginState();
    // 验证登录状态

    if (!loginState) {
      // 未找到登录状态
      resolve({
        isValid: false,
        message: '未登录'
      });
      return;
    }

    const token = wx.getStorageSync(TOKEN_KEY);
    const userInfo = wx.getStorageSync(USER_INFO_KEY);
    // 验证登录状态

    if (!token || !userInfo) {
      // 登录状态不完整
      clearLoginState();
      resolve({
        isValid: false,
        message: '登录状态不完整'
      });
      return;
    }

    // 确保用户信息中有id字段
    if (!userInfo.id && userInfo._id) {
      // 用户信息中使用_id替代id，正在规范化
      userInfo.id = userInfo._id;
      // 更新存储
      wx.setStorageSync(USER_INFO_KEY, userInfo);
    }

    // 验证用户ID一致性
    if (loginState.userId !== userInfo.id) {
      // 用户ID不一致
      // 登录状态ID与用户信息ID不一致
      clearLoginState();
      resolve({
        isValid: false,
        message: '用户ID不一致'
      });
      return;
    }

    // 验证令牌一致性
    if (loginState.token !== token) {
      // 令牌不一致
      clearLoginState();
      resolve({
        isValid: false,
        message: '令牌不一致'
      });
      return;
    }

    // 在服务器验证前，先返回本地状态为有效，避免用户体验中断
    // 这样用户可以立即看到已登录状态，而不必等待网络请求完成
    const localValidResult = {
      isValid: true,
      message: '使用本地登录状态',
      userInfo: userInfo,
      usingLocalState: true
    };

    // 立即解析为有效，让UI可以立即显示登录状态
    resolve(localValidResult);

    // 然后在后台继续验证服务器状态
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        // 后台验证服务器登录状态结果

        if (res.success) {
          // 确保服务器返回的用户信息中有id字段
          if (!res.data.id && res.data._id) {
            // 服务器返回的用户信息中使用_id替代id
            res.data.id = res.data._id;
          }

          // 验证服务器返回的用户ID
          if (res.data.id !== loginState.userId) {
            // 服务器返回的用户ID与本地不一致
            // 本地ID与服务器ID不一致

            // 更新本地用户信息
            saveLoginState(res.data, token);
            // 已更新本地用户信息与服务器同步
          } else {
            // 更新本地用户信息，确保最新
            saveLoginState(res.data, token);
            // 已更新本地用户信息
          }

          // 更新全局状态
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.userInfo = res.data;
            app.globalData.isLogin = true;
            app.globalData.needRefreshProfile = true;
            console.log('已更新全局状态');
          }
        } else {
          console.warn('服务器验证失败:', res.message);

          // 如果是未登录错误，但不立即清除登录状态
          // 而是在下次应用启动时再处理，避免用户当前会话中突然登出
          if (res.message === '未登录' || res.message === '认证令牌无效') {
            console.warn('服务器报告未登录或令牌无效，但保留本地状态以避免中断用户体验');
            // 不清除登录状态，让用户继续使用
          }
        }
      })
      .catch(err => {
        console.error('后台验证登录状态出错:', err);
        // 网络错误时不做任何处理，保留本地登录状态
        console.log('网络错误，继续使用本地登录状态');
      });
  });
}

/**
 * 登录
 * @param {Object} loginParams 登录参数
 * @param {Function} loginMethod 登录方法
 * @returns {Promise} 包含登录结果的Promise
 */
function login(loginParams, loginMethod) {
  return new Promise((resolve, reject) => {
    // 清除之前的登录状态
    clearLoginState();

    console.log('开始登录流程，参数:', loginParams);

    // 调用登录方法
    loginMethod(loginParams)
      .then(res => {
        console.log('登录响应:', res);

        if (res.success) {
          // 确保用户信息中有id字段
          const userInfo = res.data.userInfo || res.data.user;
          const token = res.data.token;

          if (!userInfo) {
            console.error('登录响应中缺少用户信息');
            resolve({
              success: false,
              message: '登录响应格式错误'
            });
            return;
          }

          if (!token) {
            console.error('登录响应中缺少令牌');
            resolve({
              success: false,
              message: '登录响应格式错误'
            });
            return;
          }

          // 确保用户信息中有id字段
          if (!userInfo.id && userInfo._id) {
            // 用户信息中使用_id替代id，正在规范化
            userInfo.id = userInfo._id;
          }

          // 保存登录状态
          const saveResult = saveLoginState(userInfo, token);
          console.log('保存登录状态结果:', saveResult);

          resolve({
            success: true,
            message: '登录成功',
            data: {
              token: token,
              userInfo: userInfo
            }
          });
        } else {
          resolve({
            success: false,
            message: res.message || '登录失败'
          });
        }
      })
      .catch(err => {
        console.error('登录出错:', err);
        reject(err);
      });
  });
}

/**
 * 登出
 */
function logout() {
  return new Promise((resolve) => {
    // 清除登录状态
    clearLoginState();

    resolve({
      success: true,
      message: '已登出'
    });
  });
}

module.exports = {
  saveLoginState,
  getLoginState,
  clearLoginState,
  validateLoginState,
  login,
  logout
};

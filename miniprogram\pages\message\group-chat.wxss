.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.message-list {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  align-items: flex-start;
}

.message-item.self {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 20rpx;
  flex-shrink: 0;
}

.message-content {
  max-width: 60%;
  display: flex;
  flex-direction: column;
}

.message-item.self .message-content {
  align-items: flex-end;
}

.message-item.other .message-content {
  align-items: flex-start;
}

/* 群聊发送者昵称样式 */
.sender-name {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.bubble {
  background: #fff;
  padding: 20rpx;
  word-break: break-all;
  font-size: 28rpx;
  line-height: 1.5;
  position: relative;
  border-radius: 10rpx;
}

.message-item.other .bubble {
  border-radius: 10rpx;
}

.message-item.self .bubble {
  background: #95ec69;
  border-radius: 10rpx;
}

.image-bubble {
  padding: 0;
  background: none;
  box-shadow: none;
}

.image-bubble image {
  max-width: 25vw;
  width: 25vw;
  height: auto;
  border-radius: 10rpx;
  display: block;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
  width: 100%;
}

.input-area {
  background: #fff;
  padding: 20rpx;
  padding-bottom: 40rpx;
  border-top: 1rpx solid #eee;
}

.input-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 10rpx;
  padding: 10rpx;
}

.message-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.action-btn image {
  width: 40rpx;
  height: 40rpx;
}

.send-btn {
  background: #07c160;
  border-radius: 10rpx;
  width: 100rpx;
}

.send-btn text {
  color: #fff;
  font-size: 28rpx;
}

/* 左侧图片按钮样式 */
.image-btn {
  margin-left: 10rpx;
  margin-right: 5rpx;
}

.image-btn image {
  width: 44rpx;
  height: 44rpx;
}
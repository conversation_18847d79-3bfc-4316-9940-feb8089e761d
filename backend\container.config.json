{"containerPort": 3000, "minNum": 0, "maxNum": 5, "cpu": 1, "mem": 2, "policyType": "cpu", "policyThreshold": 60, "envParams": {"NODE_ENV": "production", "USE_MYSQL": "true", "DB_HOST": "mysql", "DB_PORT": "3306", "DB_USER": "root", "DB_PASSWORD": "{{MYSQL_PASSWORD}}", "DB_NAME": "<PERSON><PERSON><PERSON><PERSON>"}, "customLogs": "stdout", "initialDelaySeconds": 30, "readinessProbe": {"httpGet": {"path": "/health", "port": 3000}, "initialDelaySeconds": 60, "periodSeconds": 15}, "livenessProbe": {"httpGet": {"path": "/health", "port": 3000}, "initialDelaySeconds": 60, "periodSeconds": 15}}
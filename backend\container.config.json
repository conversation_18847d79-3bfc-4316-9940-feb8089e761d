{"containerPort": 3001, "minNum": 0, "maxNum": 5, "cpu": 1, "mem": 2, "policyType": "cpu", "policyThreshold": 60, "lifecycleHook": {"prestop": {"handler": {"exec": {"command": ["sh", "-c", "sleep 10"]}}}}, "envParams": {"NODE_ENV": "production", "PORT": "3001", "USE_MYSQL": "true", "DB_HOST": "mysql", "DB_PORT": "3306", "DB_USER": "root", "DB_PASSWORD": "{{MYSQL_PASSWORD}}", "DB_NAME": "<PERSON><PERSON><PERSON><PERSON>"}, "customLogs": "stdout", "initialDelaySeconds": 90, "readinessProbe": {"httpGet": {"path": "/health", "port": 3001}, "initialDelaySeconds": 90, "periodSeconds": 15, "timeoutSeconds": 10, "failureThreshold": 5}, "livenessProbe": {"httpGet": {"path": "/health", "port": 3001}, "initialDelaySeconds": 120, "periodSeconds": 30, "timeoutSeconds": 10, "failureThreshold": 3}}
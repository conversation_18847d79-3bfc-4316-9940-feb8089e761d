#!/bin/sh

set -e
echo "开始初始化环境..."

# 创建必要的目录
echo "创建目录..."
mkdir -p /app/uploads /app/public/qrcode /app/__tmp__

# 设置目录权限
echo "设置目录权限..."
chmod 777 /app/uploads /app/public/qrcode /app/__tmp__

# 设置默认数据库连接参数
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}

echo "等待数据库连接 ${DB_HOST}:${DB_PORT}..."
# 检查并等待数据库就绪，设置超时时间为 60 秒
timeout=60
counter=0
until nc -z -w 5 $DB_HOST $DB_PORT; do
    counter=$((counter + 5))
    if [ $counter -ge $timeout ]; then
        echo "错误：等待数据库超时（${timeout}秒）"
        exit 1
    fi
    echo "等待数据库就绪...（${counter}/${timeout}秒）"
    sleep 5
done

echo "数据库已就绪"
echo "初始化完成"
exit 0

#!/bin/sh

echo "开始初始化环境..."

# 创建必要的目录
echo "创建目录..."
mkdir -p /app/uploads /app/public/qrcode /app/__tmp__

# 设置目录权限
echo "设置目录权限..."
chmod 777 /app/uploads /app/public/qrcode /app/__tmp__

echo "目录初始化完成"

# 简单的数据库连接检查，不阻塞启动
DB_HOST=${DB_HOST:-mysql}
DB_PORT=${DB_PORT:-3306}

echo "检查数据库连接 ${DB_HOST}:${DB_PORT}..."

# 只尝试一次连接检查，不等待
if nc -z -w 3 $DB_HOST $DB_PORT 2>/dev/null; then
    echo "数据库连接正常"
else
    echo "数据库暂时无法连接，应用启动后会自动重试"
fi

echo "初始化完成"
exit 0

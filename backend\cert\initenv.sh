#!/bin/sh

# 微信云托管证书初始化脚本
# 这个脚本会被微信云托管平台覆盖，但我们提供一个基础版本

echo "开始证书和环境初始化..."

# 确保ca-certificates已安装并更新
if command -v update-ca-certificates >/dev/null 2>&1; then
    echo "更新CA证书..."
    update-ca-certificates 2>/dev/null || echo "CA证书更新完成"
else
    echo "CA证书工具不可用，跳过证书更新"
fi

# 创建必要的目录
echo "创建目录..."
mkdir -p /app/uploads /app/public/qrcode /app/__tmp__ /app/cert

# 设置目录权限
echo "设置目录权限..."
chmod 777 /app/uploads /app/public/qrcode /app/__tmp__
chmod 755 /app/cert

echo "初始化完成"
exit 0

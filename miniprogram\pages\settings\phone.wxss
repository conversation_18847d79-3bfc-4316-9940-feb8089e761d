/* pages/settings/phone.wxss */
.phone-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.phone-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.phone-content {
  padding: 30rpx;
}

.current-phone {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.current-phone-label {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.current-phone-value {
  font-size: 32rpx;
  color: #333333;
}

.input-area {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
}

.input-group {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.input-group:last-child {
  border-bottom: none;
}

.input-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.input {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  color: #333333;
}

.verify-btn {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  color: #999999;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.verify-btn.active {
  color: #ffffff;
  background-color: #ff4d4f;
}

.tips {
  font-size: 26rpx;
  color: #999999;
  margin: 20rpx 0 40rpx;
  padding: 0 10rpx;
}

.next-button, .save-button {
  background-color: #ff4d4f;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}

.next-button.disabled, .save-button.disabled {
  background-color: #cccccc;
}

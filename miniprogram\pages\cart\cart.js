// pages/cart/cart.js
const { cartApi } = require('../../utils/api');

Page({
  data: {
    cartItems: [],
    isLogin: false,
    loading: true,
    isEdit: false,
    allSelected: false,
    totalPrice: 0,
    totalCount: 0,
    selectedCount: 0
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    if (this.data.isLogin) {
      this.getCartItems();
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    console.log('检查登录状态');
    const app = getApp();

    // 检查全局登录状态
    if (app.globalData.isLogin && app.globalData.userInfo) {
      this.setData({
        isLogin: true,
        loading: false
      });

      // 获取购物车数据
      this.getCartItems();
    } else {
      // 尝试从存储中获取用户信息
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          app.globalData.userInfo = res.data;
          app.globalData.isLogin = true;
          this.setData({
            isLogin: true,
            loading: false
          });

          // 获取购物车数据
          this.getCartItems();
        },
        fail: () => {
          // 未登录状态，只设置loading为false
          this.setData({
            isLogin: false,
            loading: false
          });
        }
      });
    }
  },

  // 获取购物车数据
  getCartItems: function() {
    console.log('开始获取购物车数据');
    // 使用 API 获取购物车数据
    cartApi.getCartItems()
      .then(res => {
        if (res.success) {
          const cartItems = res.data;
          // 设置每个商品的选中状态
          cartItems.forEach(item => {
            item.selected = false;
          });
          this.setData({
            cartItems: cartItems,
            loading: false,
            allSelected: false
          });
          this.calculateTotal();
          console.log('购物车数据已加载，商品数量:', cartItems.length);
        } else {
          this.setData({
            loading: false,
            cartItems: []
          });
          wx.showToast({
            title: '获取购物车失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取购物车数据失败', err);
        this.setData({
          loading: false,
          cartItems: []
        });
        wx.showToast({
          title: '获取购物车数据失败',
          icon: 'none'
        });
      });
  },

  // 切换编辑模式
  toggleEditMode: function() {
    this.setData({
      isEdit: !this.data.isEdit
    });
  },

  // 选择/取消选择单个商品
  toggleSelectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;

    cartItems[index].selected = !cartItems[index].selected;

    // 检查是否全选
    const allSelected = cartItems.every(item => item.selected);

    this.setData({
      cartItems: cartItems,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 全选/取消全选
  toggleSelectAll: function() {
    const allSelected = !this.data.allSelected;
    const cartItems = this.data.cartItems;

    cartItems.forEach(item => {
      item.selected = allSelected;
    });

    this.setData({
      cartItems: cartItems,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 计算总价和数量
  calculateTotal: function() {
    const cartItems = this.data.cartItems;
    let totalPrice = 0;
    let totalCount = 0;
    let selectedCount = 0;

    cartItems.forEach(item => {
      totalCount += item.quantity;

      if (item.selected) {
        totalPrice += item.price * item.quantity;
        selectedCount += item.quantity;
      }
    });

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      totalCount: totalCount,
      selectedCount: selectedCount
    });

    // 注意：购物车数量变化事件已在各个修改方法中触发
  },

  // 减少商品数量
  decreaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    if (item.quantity <= 1) {
      return;
    }
    // 使用 API 更新购物车商品数量
    cartApi.updateCartItem(item.id || item._id, item.quantity - 1)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity -= 1;
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          wx.showToast({ title: res.message || '操作失败', icon: 'none' });
        }
      })
      .catch(err => {
        console.error('减少商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
  },

  // 增加商品数量
  increaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    // 使用 API 更新购物车商品数量
    cartApi.updateCartItem(item.id || item._id, item.quantity + 1)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity += 1;
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          wx.showToast({ title: res.message || '操作失败', icon: 'none' });
        }
      })
      .catch(err => {
        console.error('增加商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
  },

  // 输入商品数量
  inputQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const quantity = parseInt(value);
    if (isNaN(quantity) || quantity < 1) {
      return 1;
    }
    const cartItems = this.data.cartItems;
    const item = cartItems[index];
    // 使用 API 更新购物车商品数量
    cartApi.updateCartItem(item.id || item._id, quantity)
      .then(res => {
        if (res.success) {
          cartItems[index].quantity = quantity;
          this.setData({ cartItems });
          this.calculateTotal();
        } else {
          wx.showToast({ title: res.message || '操作失败', icon: 'none' });
        }
      })
      .catch(err => {
        console.error('更新商品数量失败', err);
        wx.showToast({ title: '操作失败', icon: 'none' });
      });
    return quantity;
  },

  // 删除商品
  deleteItem: function(e) {
    const index = e.currentTarget.dataset.index;
    const cartItems = this.data.cartItems;
    const item = cartItems[index];

    wx.showModal({
      title: '提示',
      content: '确定要删除该商品吗？',
      success: (res) => {
        if (res.confirm) {
          cartApi.removeCartItem(item.id || item._id)
            .then(res => {
              if (res.success) {
                wx.showToast({ title: '删除成功', icon: 'success' });
                this.getCartItems();
              } else {
                wx.showToast({ title: res.message || '删除失败', icon: 'none' });
              }
            })
            .catch(err => {
              console.error('删除商品失败', err);
              wx.showToast({ title: '删除失败', icon: 'none' });
            });
        }
      }
    });
  },

  // 批量删除商品
  deleteSelected: function() {
    const cartItems = this.data.cartItems;
    const selectedItems = cartItems.filter(item => item.selected);
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }
    wx.showModal({
      title: '提示',
      content: `确定要删除选中的${selectedItems.length}件商品吗？`,
      success: (res) => {
        if (res.confirm) {
          const ids = selectedItems.map(item => item.id || item._id);
          cartApi.batchRemoveCartItems(ids)
            .then(res => {
              if (res.success) {
                wx.showToast({ title: '删除成功', icon: 'success' });
                this.getCartItems();
              } else {
                wx.showToast({ title: res.message || '删除失败', icon: 'none' });
              }
            })
            .catch(err => {
              console.error('批量删除商品失败', err);
              wx.showToast({ title: '删除失败', icon: 'none' });
            });
        }
      }
    });
  },

  // 点击商品
  onItemTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    const app = getApp();

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.continueCheckout();
      }
    })) {
      return;
    }

    this.continueCheckout();
  },

  // 继续结算流程
  continueCheckout: function() {
    const cartItems = this.data.cartItems;
    const selectedItems = cartItems.filter(item => item.selected);
    const selectedIds = selectedItems.map(item => item._id);

    wx.navigateTo({
      url: `/pages/order/create?cartItemIds=${selectedIds.join(',')}`
    });
  },

  // 跳转到登录页
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 跳转到商城
  goToShop: function() {
    // 如果是从商城页面跳转过来的，直接返回
    const pages = getCurrentPages();
    if (pages.length > 1 && pages[pages.length - 2].route === 'pages/shop/shop') {
      wx.navigateBack();
    } else {
      // 否则跳转到商城页面
      wx.switchTab({
        url: '/pages/shop/shop'
      });
    }
  },

  // 显示选择提示
  showSelectTip: function() {
    wx.showToast({
      title: '请先选择商品',
      icon: 'none',
      duration: 1500
    });
  },

  // 保存购物车数据并触发更新事件
  saveCartAndNotify: function(cartItems) {
    // 保存到本地存储
    wx.setStorageSync('cartItems', cartItems);

    // 触发购物车数量变化事件
    wx.setStorageSync('cartItemsUpdated', new Date().getTime());
  }
});

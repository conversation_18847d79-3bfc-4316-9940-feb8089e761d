// pages/home/<USER>
const { postApi } = require('../../utils/api');

Page({
  data: {
    banners: [],
    categories: [],
    posts: [],
    currentTab: 0,
    tabs: ['推荐', '关注', '热门', '附近'],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    // 筛选条件
    filterParams: null,
    // 搜索关键词
    searchKeyword: '',
    // 导航栏高度
    statusBarHeight: 0,
    navBarHeight: 44,
    // 搜索框宽度
    searchWidth: 0,
    // 胶囊按钮信息
    menuButtonInfo: null,
    // 窗口宽度
    windowWidth: 0,
    globalUserInfo: {},
    // 关注卡片特殊提示
    showNoFollowsTip: false,
    noFollowsMessage: '',
    // 筛选抽屉显示状态
    showFilterDrawer: false,
    // 主题数据 - 与发布页保持一致
    topics: [
      { id: 1, name: '企业服务' },
      { id: 2, name: '工商注册' },
      { id: 3, name: '税务合规' },
      { id: 4, name: '企业转让' },
      { id: 5, name: '地址托管' },
      { id: 6, name: '疑难业务' },
      { id: 7, name: '企业信用' },
      { id: 8, name: '异常处理' },
      { id: 9, name: '资质代办' },
      { id: 10, name: '知识产权' },
      { id: 11, name: '高新技术' },
      { id: 12, name: '老板圈层' }
    ]
  },

  onLoad: function (options) {
    // 首页加载

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算胶囊按钮的位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算搜索框的宽度为屏幕宽度的60%
    const searchWidth = systemInfo.windowWidth * 0.6;

    // 设置状态栏高度的 CSS 变量
    wx.setStorageSync('statusBarHeight', systemInfo.statusBarHeight);

    const app = getApp();
    const globalData = app.globalData || {};
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      searchWidth: searchWidth,
      menuButtonInfo: menuButtonInfo,
      windowWidth: systemInfo.windowWidth,
      globalUserInfo: globalData.userInfo || {}
    });

    // 获取轮播图数据
    this.getBanners();

    // 获取分类数据
    this.getCategories();

    // 延迟获取帖子数据，确保其他初始化完成
    setTimeout(() => {
      // 延迟获取帖子数据
      this.getPosts();
    }, 500);
  },

  // 页面显示时触发
  onShow: function() {
    // 首页显示
    console.log('首页 onShow 被调用');

    // 获取全局用户信息
    const app = getApp();
    const globalData = app.globalData || {};
    
    // 更新全局用户信息
    if (globalData.userInfo) {
      console.log('更新全局用户信息:', globalData.userInfo);
      this.setData({
        globalUserInfo: globalData.userInfo
      });
    }

    // 如果页面已经加载过数据，但是数据为空，则重新加载
    if (this.data.posts.length === 0 && !this.data.loading) {
      console.log('数据为空，重新加载数据');
      // 数据为空，重新加载
      this.setData({
        page: 1,
        loading: true
      });
      this.getPosts();
    } else if (globalData.isLogin && this.data.posts.length > 0) {
      // 如果用户已登录且有帖子数据，刷新关注状态
      console.log('刷新关注状态');
      const { userApi } = require('../../utils/api');
      const posts = [...this.data.posts];
      let hasUpdate = false;
      
      // 使用Promise.all并行检查所有帖子的关注状态
      const checkPromises = [];
      
      posts.forEach((post, index) => {
        if (!post.userInfo) return;
        
        // 获取目标用户ID，支持多种ID字段
        const targetId = post.userInfo.id || post.userInfo._id || post.userInfo.userId;
        const currentUserId = globalData.userInfo && (globalData.userInfo.id || globalData.userInfo._id);
        
        // 跳过无效的目标ID或自己的帖子
        if (!targetId || !currentUserId || targetId === currentUserId) {
          return;
        }
        
        const promise = userApi.checkFollowStatus(targetId)
          .then(res => {
            if (res.success && res.data && res.data.isFollowing !== undefined) {
              if (post.isFollowed !== res.data.isFollowing) {
                console.log(`用户 ${targetId} 关注状态更新: ${post.isFollowed} -> ${res.data.isFollowing}`);
                posts[index].isFollowed = res.data.isFollowing;
                hasUpdate = true;
              }
            }
          })
          .catch(err => {
            console.error('检查关注状态失败:', err);
          });
          
        checkPromises.push(promise);
      });
      
      // 如果有更新，则更新UI
      if (checkPromises.length > 0) {
        console.log(`正在检查 ${checkPromises.length} 个用户的关注状态`);
        Promise.all(checkPromises).then(() => {
          if (hasUpdate) {
            console.log('有关注状态更新，刷新UI');
            this.setData({ posts });
          } else {
            console.log('没有关注状态更新');
          }
        }).catch(err => {
          console.error('检查关注状态时出错:', err);
        });
      } else {
        console.log('没有需要检查关注状态的帖子');
      }
    }
  },

  // 获取轮播图数据
  getBanners: function() {
    // 显示加载中
    wx.showLoading({
      title: '加载中',
      mask: true
    });

    // 使用API获取轮播图数据
    postApi.getBanners()
      .then(res => {
        // 获取轮播图数据成功

        if (res.success) {
          this.setData({
            banners: res.data
          });
        } else {
          // 获取轮播图数据失败

          // 使用默认数据
          this.setDefaultBanners();
        }

        wx.hideLoading();
      })
      .catch(err => {
        // 获取轮播图数据出错

        // 使用默认数据
        this.setDefaultBanners();

        wx.hideLoading();
      });
  },

  // 设置默认轮播图数据
  setDefaultBanners: function() {
    // 默认轮播图数据
    const defaultBanners = [
      {
        id: 1,
        imageUrl: '/images/lunbo/001.jpeg',
        type: 'page',
        url: '/pages/shop/shop',
        title: '企业所得税汇算清缴专题'
      },
      {
        id: 2,
        imageUrl: '/images/lunbo/002.jpg',
        type: 'page',
        url: '/pages/shop/shop',
        title: '知识产权保护与商标注册'
      },
      {
        id: 3,
        imageUrl: '/images/lunbo/003.png',
        type: 'page',
        url: '/pages/shop/shop',
        title: '企业并购与资产重组'
      },
      {
        id: 4,
        imageUrl: '/images/lunbo/004.jpg',
        type: 'page',
        url: '/pages/shop/shop',
        title: '公司注册与工商变更'
      }
    ];

    this.setData({
      banners: defaultBanners
    });
  },

  // 获取分类数据
  getCategories: function() {
    // 开发测试阶段，直接使用本地数据
    const categories = [
      {
        id: 1,
        name: '我的发布',
        icon: '/images/icons2/我的发布.png',
        url: '/pages/user/publish'
      },
      {
        id: 2,
        name: '积分中心',
        icon: '/images/icons2/积分.png',
        url: '/pages/points/points'
      },
      {
        id: 3,
        name: '会员中心',
        icon: '/images/icons2/会员.png',
        url: '/pages/vip/center'
      },
      {
        id: 4,
        name: '在线客服',
        icon: '/images/icons2/客服.png',
        url: '/pages/service/index'
      },
      {
        id: 5,
        name: '立即分享',
        icon: '/images/icons2/分享.png',
        url: '/pages/share/share'
      }
    ];

    this.setData({
      categories: categories
    });
  },

  // 获取帖子数据
  getPosts: function(append = false) {
    const { currentTab, page, pageSize, filterParams } = this.data;

    // 显示加载中
    if (!append) {
      this.setData({
        loading: true
      });
    }

    // 添加调试日志
    console.log('【调试】当前页码:', page);
    console.log('【调试】每页数量:', pageSize);
    console.log('【调试】当前标签:', currentTab);
    console.log('【调试】筛选参数:', filterParams);

    // 构建请求参数
    const params = {
      page: page,
      pageSize: pageSize,
      tab: currentTab
    };

    // 根据不同选项卡添加特定参数
    const app = getApp();
    const globalData = app.globalData || {};

    // 添加当前标签参数，便于后端进行特定处理
    params.tab = currentTab.toString();

    switch (currentTab) {
      case 1: // 关注
        if (globalData.isLogin && globalData.userInfo) {
          // 输出用户信息以便于调试
          console.log('【调试-关注卡片】当前登录用户信息:', JSON.stringify(globalData.userInfo));
          
          // 确保有有效的用户ID
          let userId = globalData.userInfo.id || globalData.userInfo._id;
          
          if (!userId && globalData.userInfo.userId) {
            userId = globalData.userInfo.userId;
          }
          
          console.log('【调试-关注卡片】最终使用的用户ID:', userId);
          
          if (userId) {
            // 使用关注查询参数，通过follows表关联查询
            params.followedOnly = true;
            params.userId = userId;
            console.log('【调试-关注卡片】添加关注查询参数:', params);
          } else {
            console.log('【调试-关注卡片】未找到有效的用户ID');
            params.followedOnly = true;
            params.userId = 'none'; // 使用一个不存在的用户ID，确保不会返回数据
          }
        } else {
          // 如果未登录，将在switchTab中处理，这里添加一个默认参数避免获取所有帖子
          console.log('【调试-关注卡片】用户未登录或用户信息不完整');
          params.followedOnly = true;
          params.userId = 'none'; // 使用一个不存在的用户ID，确保不会返回数据
        }
        break;
      case 2: // 热门
        // 热门卡片使用点赞数排序，已在后端根据tab参数处理
        params.sortBy = 'likeCount';
        params.sortOrder = 'desc';
        break;
      case 3: // 附近
        // 获取用户设置的地区
        let userRegion = null;

        // 从存储中获取用户信息
        console.log('开始获取用户地区信息...');
        const userInfo = wx.getStorageSync('userInfo');
        console.log('从本地存储获取的用户信息:', JSON.stringify(userInfo));
        
        // 从用户信息中获取地区 - 直接检查省市字段
        if (userInfo) {
          // 首先检查region字段
          if (userInfo.region) {
            userRegion = userInfo.region;
            console.log('从 userInfo.region 获取地区:', userRegion);
          }
          // 如果没有region字段，则检查province和city字段
          else if (userInfo.province) {
            let regionParts = [];
            
            if (userInfo.province) regionParts.push(userInfo.province);
            if (userInfo.city) regionParts.push(userInfo.city);
            if (userInfo.district) regionParts.push(userInfo.district);
            
            // 如果至少有省份信息，则组合成地区字符串
            if (regionParts.length > 0) {
              userRegion = regionParts.join(' ');
              console.log('从 province/city 字段组合地区:', userRegion);
            }
          }
          
          // 确保地区信息是字符串
          if (userRegion && typeof userRegion !== 'string') {
            userRegion = String(userRegion);
            console.log('将地区信息转换为字符串:', userRegion);
          }
        }
        
        // 如果仍然没有找到地区信息，则检查最近发帖地区
        if (!userRegion) {
          console.log('用户信息中没有地区，检查最近发帖地区');
          const lastPostRegion = wx.getStorageSync('lastPostRegion');
          
          if (lastPostRegion) {
            userRegion = lastPostRegion;
            console.log('从最近发帖中获取到地区:', userRegion);
          } else {
            console.log('没有找到用户地区信息');
          }
        }

        // 再次确认用户地区信息，添加更详细的日志
        console.log('用户地区信息再次确认 - userRegion:', userRegion);
        console.log('用户地区信息再次确认 - userInfo:', JSON.stringify(userInfo));
        
        // 打印完整的userInfo对象以检查其结构
        if (userInfo) {
            console.log('用户信息完整对象属性列表:');
            for (const key in userInfo) {
                console.log(`  ${key}: ${typeof userInfo[key] === 'object' ? JSON.stringify(userInfo[key]) : userInfo[key]}`);
            }
        }
        
        // 检查全局数据中的用户信息
        const app = getApp();
        if (app && app.globalData && app.globalData.userInfo) {
            console.log('全局数据中的用户地区信息:', app.globalData.userInfo.region);
        }
        
        // 根据用户是否设置地区来决定 noRegionSet 参数
        if (!userRegion) {
            // 如果没有找到用户地区信息，设置 noRegionSet 为 true
            params.noRegionSet = 'true';
            console.log('附近卡片：用户未设置地区，将显示提示信息');
        } else {
            // 如果找到了用户地区信息，设置 noRegionSet 为 false
            params.noRegionSet = 'false';
            console.log('附近卡片：用户已设置地区:', userRegion);
        }

        // 使用地区筛选参数
        if (userRegion) {
          params.region = userRegion;
          // 强制转换为字符串，确保传递的是字符串类型
          if (typeof params.region !== 'string') {
            params.region = String(params.region);
          }
          console.log('设置地区参数:', params.region, '类型:', typeof params.region);
        }
        // 确保传递的是字符串类型
        params.useRegionFilter = 'true';
        console.log('最终请求参数:', JSON.stringify(params));
        break;
    }

    // 筛选规则处理
    console.log('处理筛选参数');
    
    if (filterParams) {
      // 检查是否有主题筛选
      let hasTopicFilter = false;
      let topicsString = '';
      
      if (filterParams.topics) {
        if (typeof filterParams.topics === 'string' && filterParams.topics.length > 0) {
          hasTopicFilter = true;
          topicsString = filterParams.topics;
          console.log('传递主题名称字符串:', topicsString);
        } else if (Array.isArray(filterParams.topics) && filterParams.topics.length > 0) {
          hasTopicFilter = true;
          topicsString = filterParams.topics.join(',');
          console.log('传递主题名称数组:', topicsString);
        }
      } else if (filterParams.selectedTopicNames && filterParams.selectedTopicNames.length > 0) {
        // 兼容使用selectedTopicNames的情况
        hasTopicFilter = true;
        if (Array.isArray(filterParams.selectedTopicNames)) {
          topicsString = filterParams.selectedTopicNames.join(',');
        } else {
          topicsString = String(filterParams.selectedTopicNames);
        }
        console.log('使用selectedTopicNames作为主题筛选:', topicsString);
      } else if (filterParams.topicNames && filterParams.topicNames.length > 0) {
        // 使用topicNames数组
        hasTopicFilter = true;
        if (Array.isArray(filterParams.topicNames)) {
          topicsString = filterParams.topicNames.join(',');
        } else {
          topicsString = String(filterParams.topicNames);
        }
        console.log('使用topicNames作为主题筛选:', topicsString);
      } else if (filterParams.topicIds) {
        // 使用topicIds做兼容处理
        hasTopicFilter = true;
        let topicIdsStr = '';
        if (Array.isArray(filterParams.topicIds)) {
          topicIdsStr = filterParams.topicIds.join(',');
        } else {
          topicIdsStr = String(filterParams.topicIds);
        }
        // 将IDs转换为对应的主题名称
        const topicMap = this.data.topics.reduce((map, topic) => {
          map[topic.id] = topic.name;
          return map;
        }, {});
        const idArr = topicIdsStr.split(',').map(id => parseInt(id.trim()));
        const nameArr = idArr.map(id => topicMap[id] || id);
        topicsString = nameArr.join(',');
        console.log('使用topicIds转换为主题名称:', topicsString);
      }
      
      // 检查是否有地区筛选
      let hasRegionFilter = false;
      let regionString = '';
      
      if (filterParams.region && Array.isArray(filterParams.region)) {
        // 检查是否选择了非"全部"的地区
        const regionParts = filterParams.region.filter(item => item !== '全部');
        if (regionParts.length > 0) {
          hasRegionFilter = true;
          regionString = regionParts.join(',');
        }
      }
      
      console.log('主题筛选:', hasTopicFilter ? topicsString : '无');
      console.log('地区筛选:', hasRegionFilter ? regionString : '无');
      
      // 1. 当选择了地区，没有选择主题时，仅作地区筛选
      if (hasRegionFilter && !hasTopicFilter) {
        console.log('规则1: 仅作地区筛选，不筛选主题');
        params.region = regionString;
        params.useRegionFilter = 'true';
        params.onlyFilterByRegion = 'true';
        // 清除主题筛选相关参数
        delete params.topics;
        delete params.topicIds;
      }
      
      // 2. 当选择了地区，也选了主题时，同时筛选地区和主题
      if (hasRegionFilter && hasTopicFilter) {
        console.log('规则2: 同时筛选地区和主题');
        params.region = regionString;
        params.topics = topicsString;
        params.useRegionFilter = 'true';
        params.topicFilterType = 'OR';
        params.topicAndRegionRelation = 'AND';
      }
      
      // 3. 当不选择地区，仅选择主题时，不筛选地区
      if (!hasRegionFilter && hasTopicFilter) {
        console.log('规则3: 不筛选地区，只筛选主题');
        params.topics = topicsString;
        params.topicFilterType = 'OR';
        // 清除地区筛选相关参数
        delete params.region;
        delete params.useRegionFilter;
      }
      
      // 4. 无论地区筛选，主题始终是“或”关系
      if (hasTopicFilter) {
        console.log('规则4: 主题筛选始终是“或”关系');
        params.topicFilterType = 'OR';
      }
    }

    // 再次检查最终请求参数，确保筛选正确应用
    console.log('最终请求参数:', JSON.stringify(params));
    
    // 特别检查地区筛选参数
    if (params.region) {
      console.log('地区筛选参数类型和值:', typeof params.region, params.region);
    }
    
    // 使用API获取帖子数据
    postApi.getPosts(params)
      .then(res => {
        console.log('获取帖子数据响应:', res);
        if (res.success) {
          let posts = Array.isArray(res.data) ? res.data : (res.data && res.data.list ? res.data.list : []);
          const hasMore = posts.length === pageSize;
          
          // 添加调试信息 - 查看返回数据数量
          console.log(`返回帖子数量: ${posts.length}，是否有更多: ${hasMore}`);
          
          // 检查是否有筛选参数但没有结果
          if (posts.length === 0 && (params.region || params.topics)) {
            console.log('警告: 应用筛选条件后没有找到匹配的帖子');  
          }

          // 处理帖子中的图片和视频URL
          let allFileIDs = [];
          posts.forEach(post => {
            // 处理图片数组
            if (Array.isArray(post.images)) {
              // 分开处理云开发和云托管的图片URL
              const cloudFileIDs = post.images.filter(img => typeof img === 'string' && img.startsWith('cloud://'));
              allFileIDs = allFileIDs.concat(cloudFileIDs);

              // 对于非cloud://开头的URL，保留原样，但要过滤无效的URL
              post.images = post.images.filter(img => {
                // 保留云开发的图片（后面会转换）
                if (typeof img === 'string' && img.startsWith('cloud://')) {
                  return true;
                }
                // 保留有效的HTTP URL（云托管对象存储的URL）
                if (typeof img === 'string' && (img.startsWith('http://') || img.startsWith('https://'))) {
                  return true;
                }
                return false;
              });
            } else {
              post.images = [];
            }

            // 处理视频URL
            if (post.video && typeof post.video === 'string') {
              if (post.video.startsWith('cloud://')) {
                allFileIDs.push(post.video);
              } else if (!post.video.startsWith('http://') && !post.video.startsWith('https://')) {
                post.video = ''; // 无效的视频URL
              }
            } else {
              post.video = '';
            }

            // 商品图片fileID收集
            if (post.product && typeof post.product === 'string') {
              try { post.product = JSON.parse(post.product); } catch { post.product = null; }
            }
            if (post.product && typeof post.product === 'object') {
              // 保存原始商品图片URL
              let imgUrl = post.product.imageUrl || '';
              if (!imgUrl && post.product.img) {
                imgUrl = post.product.img;
                post.product.imageUrl = imgUrl; // 确保设置imageUrl字段
              }
              // 检查是否有images数组
              if (!imgUrl && post.product.images && Array.isArray(post.product.images) && post.product.images.length > 0) {
                imgUrl = post.product.images[0];
                post.product.imageUrl = imgUrl; // 使用第一张图片作为商品图片
              }
              // 如果是云开发文件ID，添加到转换列表
              if (typeof imgUrl === 'string' && imgUrl.startsWith('cloud://')) {
                allFileIDs.push(imgUrl);
              }
              // 如果是HTTP URL，直接保留
              if (typeof imgUrl === 'string' && (imgUrl.startsWith('http://') || imgUrl.startsWith('https://'))) {
                post.product.imageUrl = imgUrl;
              }
            }
          });

          // 如果有云开发的文件ID，则转换为可访问的URL
          if (allFileIDs.length > 0 && typeof wx.cloud !== 'undefined' && wx.cloud) {
            wx.cloud.getTempFileURL({
              fileList: allFileIDs,
              config: { env: 'prod-5geioww562624006' },
              success: res2 => {
                const urlMap = {};
                res2.fileList.forEach(item => {
                  urlMap[item.fileID] = item.tempFileURL;
                });
                // 替换images和video为可访问URL
                posts.forEach(post => {
                  if (Array.isArray(post.images)) {
                    // 处理云开发的图片URL
                    post.images = post.images.map(fid => {
                      // 如果是云开发的文件ID，使用临时URL
                      if (typeof fid === 'string' && fid.startsWith('cloud://')) {
                        return (urlMap[fid]) ? urlMap[fid] : '';
                      }
                      // 保留其他有效URL，包括本地图片路径
                      return fid;
                    }).filter(img => {
                      // 过滤掉空字符串
                      if (!img) return false;
                      // 确保是字符串
                      if (typeof img !== 'string') return false;
                      // 过滤掉空字符串
                      if (img.trim() === '') return false;
                      return true;
                    });
                  }

                  // 处理视频URL
                  if (post.video && typeof post.video === 'string') {
                    if (post.video.startsWith('cloud://')) {
                      post.video = urlMap[post.video] || '';
                    }
                    // 保留其他格式的视频URL
                  }

                  // 商品图片fileID转URL
                  if (post.product && typeof post.product === 'object') {
                    // 处理不同类型的图片URL
                    if (typeof post.product.imageUrl === 'string') {
                      if (post.product.imageUrl.startsWith('cloud://')) {
                        // 云开发文件ID转URL
                        post.product.imageUrl = urlMap[post.product.imageUrl] || '';
                      } else if (post.product.imageUrl.startsWith('http://') || post.product.imageUrl.startsWith('https://')) {
                        // 已经是有效的HTTP URL，直接使用
                        post.product.imageUrl = post.product.imageUrl;
                      }
                    }
                    // 只有当商品真的没有图片时才使用默认图片
                    if (!post.product.imageUrl || post.product.imageUrl.trim() === '') {
                      post.product.imageUrl = '/images/icons2/默认商品.png';
                    }
                  }
                  console.log('【调试】渲染前单条post图片/视频：', post.id, post.images, post.video);
                });
                this._afterPostsLoaded(posts, append, hasMore);
              },
              fail: err => {
                console.error('【调试】getTempFileURL调用失败:', err);
                // 保留本地图片路径，只清除云开发的图片
                posts.forEach(post => {
                  if (Array.isArray(post.images)) {
                    post.images = post.images.filter(img => !img.startsWith('cloud://'));
                    // 不再为没有图片的帖子添加默认头像
                    // 允许post.images为空数组
                  }
                  if (post.video && post.video.startsWith('cloud://')) {
                    post.video = '';
                  }
                });
                // 商品图片兜底
                posts.forEach(post => {
                  if (post.product && typeof post.product === 'object') {
                    // 保存原始图片URL以便调试
                    const originalImgUrl = post.product.imageUrl || post.product.img || '';
                    console.log('失败处理中原始商品图片URL:', originalImgUrl);
                    
                    // 处理不同类型的图片URL
                    if (typeof originalImgUrl === 'string') {
                      if (originalImgUrl.startsWith('cloud://')) {
                        // 云开发文件ID无法转换，清空
                        post.product.imageUrl = '';
                      } else if (originalImgUrl.startsWith('http://') || originalImgUrl.startsWith('https://')) {
                        // 已经是有效的HTTP URL，直接使用
                        post.product.imageUrl = originalImgUrl;
                        console.log('失败处理中保留HTTP商品图片URL:', post.product.imageUrl);
                      }
                    }
                    
                    // 只有当商品真的没有图片时才使用默认图片
                    if (!post.product.imageUrl || post.product.imageUrl.trim() === '') {
                      post.product.imageUrl = '/images/icons2/默认商品.png';
                      console.log('失败处理中使用默认商品图片');
                    }
                  }
                });
                this._afterPostsLoaded(posts, append, hasMore);
              }
            });
          } else {
            // 【调试】没有fileID，直接后续处理
            console.warn('【调试】没有fileID，跳过getTempFileURL，posts:', posts);
            // 商品图片兜底
            posts.forEach(post => {
              if (post.product && typeof post.product === 'object') {
                // 保存原始图片URL以便调试
                const originalImgUrl = post.product.imageUrl || post.product.img || '';
                console.log('无fileID处理中原始商品图片URL:', originalImgUrl);
                
                // 处理不同类型的图片URL
                if (typeof originalImgUrl === 'string') {
                  if (originalImgUrl.startsWith('cloud://')) {
                    // 云开发文件ID无法转换，清空
                    post.product.imageUrl = '';
                  } else if (originalImgUrl.startsWith('http://') || originalImgUrl.startsWith('https://')) {
                    // 已经是有效的HTTP URL，直接使用
                    post.product.imageUrl = originalImgUrl;
                    console.log('无fileID处理中保留HTTP商品图片URL:', post.product.imageUrl);
                  }
                }
                
                // 只有当商品真的没有图片时才使用默认图片
                if (!post.product.imageUrl || post.product.imageUrl.trim() === '') {
                  post.product.imageUrl = '/images/icons2/默认商品.png';
                  console.log('无fileID处理中使用默认商品图片');
                }
              }
            });
            this._afterPostsLoaded(posts, append, hasMore);
          }
        } else {
          console.warn('获取帖子数据失败', res);
          wx.showToast({
            title: res.message || '获取帖子数据失败',
            icon: 'none'
          });
          this.setData({
            loading: false,
            refreshing: false
          });
        }
      })
      .catch(err => {
        console.error('获取帖子数据出错', err);
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
        this.setData({
          loading: false,
          refreshing: false
        });
      });
  },

  // 统一后续处理和渲染逻辑
  _afterPostsLoaded: function(posts, append, hasMore) {
    // 检查是否是关注卡片的特殊情况
    if (this.data.currentTab === 1) {
      console.log('【调试-关注卡片】处理关注卡片的特殊情况，帖子数量:', posts.length);
      
      // 情况1：没有关注任何人或关注的人没有发帖
      if (posts.length === 1 && posts[0]._noFollows) {
        console.log('【调试-关注卡片】检测到特殊标记，消息:', posts[0]._message);
        // 显示特殊提示信息
        this.setData({
          posts: [],
          loading: false,
          refreshing: false,
          hasMore: false,
          noFollowsMessage: posts[0]._message || '您还没有关注任何人',
          showNoFollowsTip: true
        });
        console.log('【调试-关注卡片】设置特殊提示:', posts[0]._message);
        return;
      } 
      // 情况2：关注卡片没有数据，但没有特殊标记（可能是其他原因导致的空数据）
      else if (posts.length === 0) {
        console.log('【调试-关注卡片】没有帖子数据，显示默认提示');
        // 显示默认提示（使用WXML中的else分支）
        this.setData({
          posts: [],
          loading: false,
          refreshing: false,
          hasMore: false,
          showNoFollowsTip: false,
          noFollowsMessage: ''
        });
        return;
      }
      // 情况3：关注卡片有数据，隐藏提示
      else {
        console.log('【调试-关注卡片】有帖子数据，隐藏提示');
        this.setData({
          showNoFollowsTip: false,
          noFollowsMessage: ''
        });
      }
    }
    
    // 检查是否是附近卡片的特殊情况：用户未设置地区
    if (this.data.currentTab === 3) {
      // 情况：用户未设置地区
      if (posts.length === 1 && posts[0]._noRegionSet) {
        console.log('【调试-附近卡片】检测到用户未设置地区标记，消息:', posts[0]._message);
        // 显示特殊提示信息
        this.setData({
          posts: [],
          loading: false,
          refreshing: false,
          hasMore: false,
          noFollowsMessage: posts[0]._message || '请设置您的所在地区',
          showNoFollowsTip: true
        });
        console.log('【调试-附近卡片】设置特殊提示:', posts[0]._message);
        return;
      }
    }
    
    // 检查是否有地区回退标记（附近卡片特殊处理）
    let hasRegionFallback = false;
    let userRegionInfo = null;
    
    // 检查是否有帖子带有地区回退标记
    if (posts.length > 0 && posts[0]._isNationalFallback) {
      hasRegionFallback = true;
      userRegionInfo = posts[0]._userRegionInfo;
      console.log('检测到地区回退标记，用户地区信息:', userRegionInfo);
      
      // 分类帖子来源，统计数量
      const nationalPosts = posts.filter(post => post._source === 'national');
      const otherRegionPosts = posts.filter(post => post._source === 'other');
      
      console.log('全国帖子数量:', nationalPosts.length);
      console.log('其他地区帖子数量:', otherRegionPosts.length);
    }
    
    // 渲染前去重：同一内容+同一用户+同一时间只显示一条
    const uniqueMap = new Map();
    posts.forEach(post => {
      const key = `${post.content}_${post.userInfo && post.userInfo.id}_${post.createTime}`;
      if (!uniqueMap.has(key)) {
        uniqueMap.set(key, post);
      }
    });
    let newPosts = Array.from(uniqueMap.values());

    // 合并去重逻辑
    if (append) {
      const oldPosts = this.data.posts || [];
      const allPosts = oldPosts.concat(newPosts);
      const postMap = new Map();
      allPosts.forEach(p => {
        if (!postMap.has(p.id) || Number(p.createTime) > Number((postMap.get(p.id)||{}).createTime)) {
          postMap.set(p.id, p);
        }
      });
      newPosts = Array.from(postMap.values());
    }
    newPosts.sort((a, b) => Number(b.createTime) - Number(a.createTime));

    // 补全userInfo和displayTime
    newPosts.forEach(post => {
      // userInfo兼容 nickName/nickname、avatarUrl/avatar
      if (!post.userInfo) post.userInfo = {};
      if (!post.userInfo.avatarUrl && post.userInfo.avatar) post.userInfo.avatarUrl = post.userInfo.avatar;
      if (!post.userInfo.avatar && post.userInfo.avatarUrl) post.userInfo.avatar = post.userInfo.avatarUrl;
      if (!post.userInfo.nickName && post.userInfo.nickname) post.userInfo.nickName = post.userInfo.nickname;
      if (!post.userInfo.nickname && post.userInfo.nickName) post.userInfo.nickname = post.userInfo.nickName;
      if (!post.userInfo.avatarUrl) post.userInfo.avatarUrl = '/images/icons2/默认头像.png';
      if (!post.userInfo.nickName && !post.userInfo.nickname) post.userInfo.nickName = '匿名用户';
      // 发帖时间
      post.displayTime = this.formatTime(post.createTime);
      // 地区标签
      if (post.region) {
        post.regionText = this.formatRegion(post.region);
      } else {
        post.regionText = '全国';
      }
      // 处理多主题数组 - 增强健壮性与安全性
      try {
        // 首先确保topic字段存在且有效
        if (!post.topic || post.topic === '') {
          post.topic = '企业服务';
        }
        
        // 处理topics字段 - 更严格的检查
        if (post.topics) {
          if (typeof post.topics === 'string') {
            // 提前检查是否是有效的JSON格式
            const trimmedTopics = post.topics.trim();
            if (trimmedTopics === '' || trimmedTopics === '[]' || trimmedTopics === '{}' || 
                trimmedTopics === 'null' || trimmedTopics === 'undefined') {
              // 明确无效的值，直接使用默认主题
              post.topicsArray = [post.topic];
              console.log('使用默认主题，原topics为:', post.topics);
            } else {
              // 检查是否是有效的JSON格式（以 [ 开头或 " 开头）
              const firstChar = trimmedTopics.charAt(0);
              if (firstChar === '[' || firstChar === '"') {
                try {
                  // 尝试解析JSON字符串
                  const parsed = JSON.parse(trimmedTopics);
                  if (Array.isArray(parsed) && parsed.length > 0) {
                    // 有效数组
                    post.topicsArray = parsed.map(t => String(t)).filter(t => t.trim() !== '');
                    if (post.topicsArray.length === 0) {
                      post.topicsArray = [post.topic];
                    }
                  } else if (typeof parsed === 'string' && parsed.trim() !== '') {
                    // 单个字符串
                    post.topicsArray = [parsed];
                  } else if (parsed === null || parsed === undefined) {
                    // null或undefined
                    post.topicsArray = [post.topic];
                  } else {
                    // 其他情况回退到单个主题
                    post.topicsArray = [post.topic];
                  }
                } catch (e) {
                  console.error('解析topics字段失败:', e, '原始值:', post.topics);
                  // 解析失败，直接使用单个主题
                  post.topicsArray = [post.topic];
                }
              } else {
                // 不是有效的JSON格式，直接作为单个主题使用
                post.topicsArray = [trimmedTopics];
                console.log('非JSON格式topics:', trimmedTopics);
              }
            }
          } else if (Array.isArray(post.topics)) {
            // 已经是数组，过滤空值和非字符串值
            post.topicsArray = post.topics
              .map(t => t !== null && t !== undefined ? String(t) : null)
              .filter(t => t && t.trim() !== '');
            if (post.topicsArray.length === 0) {
              post.topicsArray = [post.topic];
            }
          } else if (post.topics === null || post.topics === undefined) {
            // null或undefined
            post.topicsArray = [post.topic];
          } else {
            // 其他类型，尝试转换为字符串
            try {
              const topicStr = String(post.topics).trim();
              post.topicsArray = topicStr ? [topicStr] : [post.topic];
            } catch {
              post.topicsArray = [post.topic];
            }
          }
        } else {
          // 没有topics字段，使用单个主题
          post.topicsArray = [post.topic];
        }
        
        // 最后确保至少有一个有效的主题
        if (!post.topicsArray || !Array.isArray(post.topicsArray) || post.topicsArray.length === 0) {
          post.topicsArray = ['企业服务'];
        }
      } catch (err) {
        // 捕获所有异常，确保不会影响整体渲染
        console.error('处理主题时发生异常:', err);
        post.topicsArray = [post.topic || '企业服务'];
      }
      
      // 商品字段兼容
      if (post.product && typeof post.product === 'string') {
        try {
          post.product = JSON.parse(post.product);
        } catch (e) {
          post.product = null;
        }
      }
      if (post.product && typeof post.product === 'object') {
        console.log('处理商品数据 (_afterPostsLoaded):', JSON.stringify(post.product));
        
        // 检查所有可能的图片字段
        let productImage = '';
        
        // 1. 优先检查imageUrl字段
        if (post.product.imageUrl && typeof post.product.imageUrl === 'string' && post.product.imageUrl.trim() !== '') {
          productImage = post.product.imageUrl;
          console.log('使用imageUrl字段作为商品图片:', productImage);
        }
        // 2. 如果没有imageUrl，检查img字段
        else if (post.product.img && typeof post.product.img === 'string' && post.product.img.trim() !== '') {
          productImage = post.product.img;
          console.log('使用img字段作为商品图片:', productImage);
        }
        // 3. 如果没有img，检查images数组
        else if (post.product.images && Array.isArray(post.product.images) && post.product.images.length > 0) {
          // 使用第一张图片
          const firstImage = post.product.images[0];
          if (typeof firstImage === 'string' && firstImage.trim() !== '') {
            productImage = firstImage;
            console.log('使用images数组第一张图片作为商品图片:', productImage);
          }
        }
        // 4. 如果没有images，检查image字段
        else if (post.product.image && typeof post.product.image === 'string' && post.product.image.trim() !== '') {
          productImage = post.product.image;
          console.log('使用image字段作为商品图片:', productImage);
        }
        // 5. 如果没有image，检查avatar字段
        else if (post.product.avatar && typeof post.product.avatar === 'string' && post.product.avatar.trim() !== '') {
          productImage = post.product.avatar;
          console.log('使用avatar字段作为商品图片:', productImage);
        }
        // 6. 如果没有avatar，检查cover字段
        else if (post.product.cover && typeof post.product.cover === 'string' && post.product.cover.trim() !== '') {
          productImage = post.product.cover;
          console.log('使用cover字段作为商品图片:', productImage);
        }
        // 7. 如果没有cover，检查thumbnail字段
        else if (post.product.thumbnail && typeof post.product.thumbnail === 'string' && post.product.thumbnail.trim() !== '') {
          productImage = post.product.thumbnail;
          console.log('使用thumbnail字段作为商品图片:', productImage);
        }
        
        // 处理找到的图片URL
        if (productImage) {
          // 如果是云开发文件ID，已经在前面处理过，这里不需要再处理
          // 如果是HTTP URL，直接使用
          if (productImage.startsWith('http://') || productImage.startsWith('https://')) {
            post.product.imageUrl = productImage;
            console.log('使用HTTP URL作为商品图片:', post.product.imageUrl);
          }
          // 如枟是相对路径，直接使用
          else if (productImage.startsWith('/')) {
            post.product.imageUrl = productImage;
            console.log('使用相对路径作为商品图片:', post.product.imageUrl);
          }
          // 如果是云开发文件ID，已经在前面转换过，这里不需要再处理
        }
        
        // 只有当商品真的没有图片时才使用默认图片
        if (!post.product.imageUrl || post.product.imageUrl.trim() === '') {
          post.product.imageUrl = '/images/icons2/默认商品.png';
          console.log('在_afterPostsLoaded中使用默认商品图片');
        }
        
        // 打印最终使用的商品图片URL
        console.log('最终使用的商品图片URL:', post.product.imageUrl);
        
        // 其他字段兼容
        post.product.name = post.product.name || post.product.title || '';
        post.product.price = post.product.price || post.product.amount || '';
      }
    });

    // 添加最终数据检查日志
    console.log('最终帖子数据:', newPosts.map(p => ({
      id: p.id,
      images: p.images,
      content: p.content ? p.content.substring(0, 20) + '...' : ''
    })));

    // 格式化地区回退提示
    let regionFallbackTip = '';
    if (hasRegionFallback && userRegionInfo) {
      // 根据用户地区信息构建提示语
      let regionText = '';
      if (userRegionInfo.district && userRegionInfo.district !== '全部') {
        regionText = `${userRegionInfo.province} ${userRegionInfo.city} ${userRegionInfo.district}`;
      } else if (userRegionInfo.city && userRegionInfo.city !== '全部') {
        regionText = `${userRegionInfo.province} ${userRegionInfo.city}`;
      } else if (userRegionInfo.province && userRegionInfo.province !== '全部') {
        regionText = userRegionInfo.province;
      } else {
        regionText = '当前地区';
      }
      
      // 分类帖子来源，统计数量
      const nationalPosts = posts.filter(post => post._source === 'national');
      const otherRegionPosts = posts.filter(post => post._source === 'other');
      
      // 根据不同来源的帖子数量构建不同的提示语
      if (nationalPosts.length > 0 && otherRegionPosts.length > 0) {
        regionFallbackTip = `您当前所在的地区"${regionText}"暂无帖子，为您显示全国及其他地区的帖子`;
      } else if (nationalPosts.length > 0) {
        regionFallbackTip = `您当前所在的地区"${regionText}"暂无帖子，为您显示全国的帖子`;
      } else if (otherRegionPosts.length > 0) {
        regionFallbackTip = `您当前所在的地区"${regionText}"暂无帖子，为您显示其他地区的帖子`;
      } else {
        regionFallbackTip = `您当前所在的地区"${regionText}"暂无帖子，为您显示其他地区的内容`;
      }
      
      console.log('地区回退提示:', regionFallbackTip);
      console.log('全国帖子数量:', nationalPosts.length);
      console.log('其他地区帖子数量:', otherRegionPosts.length);
    }
    
    this.setData({
      posts: newPosts,
      loading: false,
      refreshing: false,
      hasMore: hasMore,
      // 添加地区回退提示
      regionFallbackTip: regionFallbackTip,
      hasRegionFallback: hasRegionFallback
    });
  },

  // 商品卡片点击跳转商品详情
  onProductTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const post = this.data.posts[index];
    if (post && post.product && (post.product._id || post.product.id)) {
      wx.navigateTo({
        url: `/pages/product/detail?id=${post.product._id || post.product.id}`
      });
    } else {
      wx.showToast({ title: '商品信息缺失', icon: 'none' });
    }
  },

  // 切换标签
  switchTab: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentTab) {
      return;
    }

    // 如果点击的是"关注"选项卡，需要检查登录状态
    if (index === 1) { // 关注选项卡
      const app = getApp();
      const isLogin = app.globalData && app.globalData.isLogin;

      if (!isLogin) {
        wx.showModal({
          title: '提示',
          content: '请先登录后查看关注内容',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/auth/auth'
              });
            }
          }
        });
        return;
      }
    }

    // 重置筛选条件，但保留地区和主题的选择
    const currentFilterParams = this.data.filterParams || {};
    const newFilterParams = {
      ...currentFilterParams,
      // 保留地区和主题的选择
      region: currentFilterParams.region,
      selectedTopicIds: currentFilterParams.selectedTopicIds
    };

    this.setData({
      currentTab: index,
      loading: true,
      page: 1,
      posts: [],
      filterParams: newFilterParams
    });

    this.getPosts();
  },
  
  // 从关注卡片切换到推荐卡片
  switchToRecommend: function() {
    console.log('从关注卡片切换到推荐卡片');
    
    // 重置筛选条件，但保留地区和主题的选择
    const currentFilterParams = this.data.filterParams || {};
    const newFilterParams = {
      ...currentFilterParams,
      region: currentFilterParams.region,
      selectedTopicIds: currentFilterParams.selectedTopicIds
    };

    this.setData({
      currentTab: 0, // 切换到推荐卡片
      loading: true,
      page: 1,
      posts: [],
      filterParams: newFilterParams
    });

    // 显示切换提示
    wx.showToast({
      title: '已切换到推荐',
      icon: 'none',
      duration: 1500
    });

    // 重新获取帖子数据
    this.getPosts();
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      refreshing: true,
      page: 1
    });

    this.getPosts();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1,
      loading: true
    });

    this.getPosts(true);
  },

  // 点击轮播图
  onBannerTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];

    if (banner.type === 'url') {
      // 打开网页
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(banner.url)}`
      });
    } else if (banner.type === 'page') {
      // 打开小程序页面
      wx.navigateTo({
        url: banner.url
      });
    }
  },

  // 点击分类
  onCategoryTap: function(e) {
    const id = Number(e.currentTarget.dataset.id);
    const url = e.currentTarget.dataset.url;
    const app = getApp();
    // 判断是否点击的是积分中心或会员中心，兼容id为数字或字符串
    if ((id === 2 || (url && url.indexOf('/points/points') !== -1)) || (id === 3 || (url && url.indexOf('/vip/center') !== -1))) {
      if (!app.globalData.isLogin) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可进入该功能',
          showCancel: true,
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({ url: '/pages/auth/auth' });
            }
            // 取消时不做任何操作
          }
        });
        return;
      }
    }
    // 在线客服，直接跳转到客服留言页面
    if (id === 4 || (url && url.indexOf('/service/index') !== -1)) {
      wx.navigateTo({ url: '/pages/service/index' });
      return;
    }
    // 立即分享，需登录校验
    if (id === 5 || (url && url.indexOf('/share/share') !== -1)) {
      if (!app.globalData.isLogin) {
        wx.showModal({
          title: '请先登录',
          content: '登录后可进入该功能',
          showCancel: true,
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({ url: '/pages/auth/auth' });
            }
          }
        });
        return;
      }
      wx.navigateTo({ url: '/pages/share/share' });
      return;
    }
    if (url) {
      wx.navigateTo({
        url: url
      });
    } else {
      wx.navigateTo({
        url: `/pages/category/category?id=${id}`
      });
    }
  },

  // 点击帖子
  onPostTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}`
    });
  },

  // 点击用户头像
  onUserTap: function(e) {
    let id = e.currentTarget.dataset.id;
    // 兼容userInfo.id和userInfo.userId
    if (!id && e.currentTarget.dataset.userid) {
      id = e.currentTarget.dataset.userid;
    }
    wx.navigateTo({
      url: `/pages/user/space?id=${id}`
    });
  },

  // 点赞
  onLikeTap: function(e) {
    const app = getApp();
    const index = e.currentTarget.dataset.index;
    const post = this.data.posts[index];

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performLike(index, post);
      }
    })) {
      return;
    }
  },

  // 执行点赞操作
  performLike: function(index, post) {
    // 显示加载中
    wx.showLoading({
      title: '处理中',
      mask: true
    });

    // 使用API进行点赞操作
    postApi.likePost(post.id)
      .then(res => {
        console.log('点赞操作成功', res);
        wx.hideLoading();

        if (res.success) {
          // 更新点赞状态和数量
          const posts = this.data.posts;
          posts[index].isLiked = res.data.isLiked;
          posts[index].likeCount = res.data.likeCount;

          this.setData({
            posts: posts
          });

          // 显示成功提示
          wx.showToast({
            title: res.data.isLiked ? '点赞成功' : '取消点赞成功',
            icon: 'success'
          });
        } else {
          console.warn('点赞操作失败', res);

          // 显示错误提示
          wx.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('点赞操作出错', err);
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 评论
  onCommentTap: function(e) {
    const app = getApp();
    const id = e.currentTarget.dataset.id;

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        wx.navigateTo({
          url: `/pages/post/comments?postId=${id}`
        });
      }
    })) {
      return;
    }
  },

  // 分享
  onShareTap: function(e) {
    const app = getApp();
    const index = e.currentTarget.dataset.index;
    const post = this.data.posts[index];

    // 使用全局登录检查方法
    if (!app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.showShareOptions(index, post);
      }
    })) {
      return;
    }
  },

  // 显示分享选项
  showShareOptions: function(index, post) {
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈'],
      success: (res) => {
        if (res.tapIndex === 0 || res.tapIndex === 1) {
          // 更新转发数
          const posts = this.data.posts;
          posts[index].shareCount = (posts[index].shareCount || 0) + 1;

          this.setData({
            posts: posts
          });

          if (res.tapIndex === 0) {
            // 分享给朋友，由onShareAppMessage处理
            wx.showShareMenu({
              withShareTicket: true
            });
          } else if (res.tapIndex === 1) {
            // 分享到朋友圈
            wx.showToast({
              title: '已分享到朋友圈',
              icon: 'success'
            });
          }
        }
      }
    });
  },

  // 分享给朋友
  onShareAppMessage: function(res) {
    if (res.from === 'button') {
      const index = res.target.dataset.index;
      const post = this.data.posts[index];

      return {
        title: post.content.substring(0, 30),
        path: `/pages/post/detail?id=${post.id}`,
        imageUrl: post.images && post.images.length > 0 ? post.images[0] : ''
      };
    }

    return {
      title: '发现更多精彩内容',
      path: '/pages/home/<USER>'
    };
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认（键盘回车）
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    if (keyword) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
      });
    }
  },

  // 消息
  onMessageTap: function() {
    wx.switchTab({
      url: '/pages/message/message'
    });
  },

  // 点击筛选按钮
  onFilterTap: function() {
    // 使用自定义抽屉组件
    this.setData({
      showFilterDrawer: true
    });
  },
  
  // 关闭筛选抽屉
  onFilterClose: function() {
    this.setData({
      showFilterDrawer: false
    });
  },
  
  // 筛选确认
  onFilterConfirm: function(e) {
    // 接收组件传来的筛选参数
    const { filterParams } = e.detail;
    
    console.log('筛选确认收到参数:', JSON.stringify(filterParams));
    
    // 关闭抽屉
    this.setData({
      showFilterDrawer: false
    });
    
    // 应用筛选条件
    if (filterParams) {
      this.applyFilter(filterParams);
    } else {
      console.error('筛选参数为空');
      wx.showToast({
        title: '筛选应用失败，请重试',
        icon: 'none'
      });
    }
  },

  // 应用筛选条件
  applyFilter: function(filterParams) {
    try {
      // 确保筛选参数有效
      if (!filterParams) {
        console.log('筛选参数为空，不应用筛选');
        return;
      }

      console.log('应用筛选条件:', JSON.stringify(filterParams));
      
      // 防止filterParams不是对象
      if (typeof filterParams !== 'object') {
        console.error('筛选参数不是对象:', filterParams);
        return;
      }

      // 处理主题筛选
      if (filterParams.topicNames && Array.isArray(filterParams.topicNames) && filterParams.topicNames.length > 0) {
        // 使用topicNames作为主题筛选条件
        filterParams.topics = filterParams.topicNames;
        console.log('使用topicNames作为主题筛选:', filterParams.topics);
      } else if (filterParams.topics && Array.isArray(filterParams.topics) && filterParams.topics.length > 0) {
        // 检查topics数组中的元素是ID还是名称
        const firstItem = filterParams.topics[0];
        if (typeof firstItem === 'number' || (typeof firstItem === 'string' && /^\d+$/.test(firstItem))) {
          // 如果是数字或数字字符串，则认为是ID，需要转换为名称
          const topicMap = this.data.topics.reduce((map, topic) => {
            map[topic.id] = topic.name;
            return map;
          }, {});
          filterParams.topics = filterParams.topics
            .map(id => topicMap[id] || id)
            .filter(name => name); // 过滤掉未找到对应名称的ID
          console.log('将主题ID转换为名称:', filterParams.topics);
        } else {
          // 已经是名称，直接使用
          console.log('使用现有的topics参数(名称):', filterParams.topics);
        }
      } else if (filterParams.selectedTopicIds && Array.isArray(filterParams.selectedTopicIds) && filterParams.selectedTopicIds.length > 0) {
        // 兼容旧版，将选中的主题ID转换为名称
        const topicMap = this.data.topics.reduce((map, topic) => {
          map[topic.id] = topic.name;
          return map;
        }, {});
        filterParams.topics = filterParams.selectedTopicIds
          .map(id => topicMap[id])
          .filter(name => name); // 过滤掉未找到对应名称的ID
        console.log('将选中的主题ID转换为名称:', filterParams.topics);
      } else {
        // 确保总是有一个topics属性，即使是空数组
        filterParams.topics = [];
        console.log('未设置主题筛选');
      }

      // 处理地区筛选
      if (filterParams.region && Array.isArray(filterParams.region)) {
        // 检查是否选择了地区（不是全部）
        const hasRegionSelected = filterParams.region.some(item => item !== '全部');
        if (hasRegionSelected) {
          console.log('选中的地区:', filterParams.region.join(','));
          filterParams.useRegionFilter = true;
        } else {
          filterParams.useRegionFilter = false;
        }
      } else {
        // 确保总是有region属性
        filterParams.region = ['全部', '全部', '全部'];
        filterParams.useRegionFilter = false;
      }

      // 更新数据
      this.setData({
        filterParams: filterParams,
        page: 1,
        loading: true,
        posts: []
      });

      // 显示筛选提示
      let filterTips = [];
      
      // 地区筛选提示
      if (filterParams.regionText && typeof filterParams.regionText === 'string' && 
          filterParams.regionText !== '请选择地区' && filterParams.regionText !== '全国') {
        filterTips.push(`地区: ${filterParams.regionText}`);
      } else if (filterParams.region && Array.isArray(filterParams.region) && 
                filterParams.region.length === 3 && filterParams.region[0] !== '全部') {
        // 如果没有regionText但有region信息
        const regionText = filterParams.region.filter(r => r !== '全部').join(' ');
        if (regionText) {
          filterTips.push(`地区: ${regionText}`);
        }
      }
      
      // 主题筛选提示
      if (filterParams.topics && Array.isArray(filterParams.topics) && filterParams.topics.length > 0) {
        try {
          // 确保主题数据存在
          if (this.data.topics && Array.isArray(this.data.topics)) {
            // 将主题ID转为数字保证比较正确
            const topicIds = filterParams.topics.map(id => typeof id === 'string' ? parseInt(id, 10) : id);
            
            const topicNames = this.data.topics
              .filter(topic => topicIds.includes(topic.id))
              .map(topic => topic.name);
              
            if (topicNames.length > 0) {
              filterTips.push(`主题: ${topicNames.join(', ')}`);
            } else {
              console.log('未找到匹配的主题名称', topicIds);
            }
          } else {
            console.log('主题数据不存在');
          }
        } catch (error) {
          console.error('渲染主题筛选提示出错:', error);
        }
      }
      
      // 显示筛选提示弹窗
      if (filterTips.length > 0) {
        wx.showToast({
          title: `已筛选 ${filterTips.join(' | ')}`,
          icon: 'none',
          duration: 2000
        });
      }

      // 重新获取帖子数据
      this.getPosts();
    } catch (error) {
      console.error('应用筛选条件出错:', error);
      wx.showToast({
        title: '筛选应用失败，请重试',
        icon: 'none'
      });
    }
  },

  // 工具函数：格式化时间戳为友好字符串
  formatTime: function(ts) {
    if (!ts) return '';
    const now = Date.now();
    const diff = now - Number(ts);
    if (diff < 60 * 1000) return '刚刚';
    if (diff < 60 * 60 * 1000) return Math.floor(diff / 60000) + '分钟前';
    const date = new Date(Number(ts));
    return date.getFullYear() + '-' + (date.getMonth()+1).toString().padStart(2,'0') + '-' + date.getDate().toString().padStart(2,'0') + ' ' + date.getHours().toString().padStart(2,'0') + ':' + date.getMinutes().toString().padStart(2,'0');
  },

  // 新增：格式化地区
  formatRegion: function(region) {
    if (!region) return '';
    if (typeof region === 'string') {
      try {
        region = JSON.parse(region);
      } catch (e) {
        return region;
      }
    }
    if (region.province && region.province.name) {
      let text = region.province.name;
      if (region.city && region.city.name) {
        text += ' ' + region.city.name;
      }
      if (region.district && region.district.name) {
        text += ' ' + region.district.name;
      }
      return text;
    }
    return '';
  },

  scrollToTop: function() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  onFollowTap: function(e) {
    const app = getApp();
    const index = e.currentTarget.dataset.index;
    const posts = this.data.posts;
    const post = posts[index];
    
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: true,
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/home/<USER>' });
          }
        }
      });
      return;
    }
    
    // 不能关注自己
    if (post.userInfo && (post.userInfo.id === app.globalData.userInfo.id || post.userInfo.userId === app.globalData.userInfo.id)) {
      wx.showToast({ title: '不能关注自己', icon: 'none' });
      return;
    }
    
    // 更新UI状态，立即反馈
    const newFollowStatus = !post.isFollowed;
    posts[index].isFollowed = newFollowStatus;
    this.setData({ posts });
    
    // 发起关注/取消关注请求
    const { userApi } = require('../../utils/api');
    const targetId = post.userInfo.id || post.userInfo.userId;
    const api = newFollowStatus ? userApi.followUser : userApi.unfollowUser;
    
    api(targetId).then(res => {
      if (!res.success) {
        // 操作失败，恢复之前的状态
        posts[index].isFollowed = !newFollowStatus;
        this.setData({ posts });
        wx.showToast({ 
          title: res.message || (newFollowStatus ? '关注失败' : '取消关注失败'), 
          icon: 'none' 
        });
      }
    }).catch(err => {
      // 请求失败，恢复之前的状态
      posts[index].isFollowed = !newFollowStatus;
      this.setData({ posts });
      wx.showToast({ 
        title: '网络错误，请稍后再试', 
        icon: 'none' 
      });
    });
  },

  // 图片加载失败兜底
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const imgIndex = e.currentTarget.dataset.imgindex;
    const posts = this.data.posts;
    if (posts[index]) {
      if (imgIndex !== undefined && posts[index].images && posts[index].images[imgIndex]) {
        posts[index].images[imgIndex] = '/images/icons2/默认头像.png';
      } else if (posts[index].images && posts[index].images.length > 0) {
        posts[index].images[0] = '/images/icons2/默认头像.png';
      } else if (posts[index].userInfo) {
        posts[index].userInfo.avatarUrl = '/images/icons2/默认头像.png';
      }
      this.setData({ posts });
    }
  },

  // 单图模式图片加载后动态设置高度
  onSingleImageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    const { width, height } = e.detail;
    // 获取容器宽度（即屏幕宽度-左右padding）
    const query = wx.createSelectorQuery();
    query.select('.post-list').boundingClientRect(rect => {
      if (!rect) return;
      const containerWidth = rect.width;
      // 计算图片按宽度等比缩放后的高度
      let displayHeight = height * containerWidth / width;
      const maxHeight = containerWidth * 0.65;
      if (displayHeight > maxHeight) displayHeight = maxHeight;
      // 更新对应post的imageDisplayHeight
      const posts = this.data.posts;
      if (posts[index]) {
        posts[index].imageDisplayHeight = displayHeight;
        this.setData({ posts });
      }
    }).exec();
  },

  // 多图模式图片加载后，动态计算每张图片的宽度和高度
  onMultiImageLoad: function(e) {
    const postIndex = e.currentTarget.dataset.index;
    const imgIndex = e.currentTarget.dataset.imgindex;
    const { width, height } = e.detail;
    const posts = this.data.posts;
    if (!posts[postIndex]) return;
    // 初始化存储
    if (!posts[postIndex].multiImageMeta) posts[postIndex].multiImageMeta = [];
    posts[postIndex].multiImageMeta[imgIndex] = { width, height };
    // 检查是否所有图片都已加载
    if (posts[postIndex].multiImageMeta.length === posts[postIndex].images.length && posts[postIndex].multiImageMeta.every(meta => meta)) {
      // 计算总原始宽度
      const totalWidth = posts[postIndex].multiImageMeta.reduce((sum, meta) => sum + meta.width, 0);
      // 获取容器宽度，扣除图片间隙（每张图片右侧4px，最后一张不需要）和左右padding 32px
      const query = wx.createSelectorQuery();
      query.select('.post-list').boundingClientRect(rect => {
        if (!rect) return;
        const containerWidth = rect.width - 4 * (posts[postIndex].images.length - 1); // 扣除间隙
        // 计算每张图片的显示宽度和高度
        posts[postIndex].multiImageWidths = posts[postIndex].multiImageMeta.map(meta => containerWidth * meta.width / totalWidth);
        posts[postIndex].multiImageHeights = posts[postIndex].multiImageMeta.map((meta, i) => {
          let displayHeight = meta.height * posts[postIndex].multiImageWidths[i] / meta.width;
          const maxHeight = posts[postIndex].multiImageWidths[i] * 0.65;
          if (displayHeight > maxHeight) displayHeight = maxHeight;
          return displayHeight;
        });
        this.setData({ posts });
      }).exec();
    } else {
      this.setData({ posts });
    }
  }
});

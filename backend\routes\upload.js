const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();
const COS = require('cos-nodejs-sdk-v5');

// 腾讯云COS配置（请将真实信息填入环境变量或config文件）
const cosConfig = {
  SecretId: process.env.COS_SECRET_ID || '你的SecretId',
  SecretKey: process.env.COS_SECRET_KEY || '你的SecretKey',
  Bucket: process.env.COS_BUCKET || '你的Bucket',
  Region: process.env.COS_REGION || 'ap-shanghai'
};
const cos = new COS({
  SecretId: cosConfig.SecretId,
  SecretKey: cosConfig.SecretKey
});

// 本地临时上传目录
const uploadDir = path.resolve(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});
const upload = multer({ storage });

// 上传文件接口
router.post('/', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '未收到文件' });
    }
    const cosKey = `uploads/${req.file.filename}`;
    const localFilePath = req.file.path;
    // 上传到COS
    await new Promise((resolve, reject) => {
      cos.putObject({
        Bucket: cosConfig.Bucket,
        Region: cosConfig.Region,
        Key: cosKey,
        Body: fs.createReadStream(localFilePath),
        ContentType: req.file.mimetype
      }, (err, data) => {
        if (err) return reject(err);
        resolve(data);
      });
    });
    // 获取带签名外链
    const url = await new Promise((resolve, reject) => {
      cos.getObjectUrl({
        Bucket: cosConfig.Bucket,
        Region: cosConfig.Region,
        Key: cosKey,
        Sign: true,
        Expires: 3600
      }, (err, data) => {
        if (err) return reject(err);
        resolve(data.Url);
      });
    });
    // 删除本地临时文件
    fs.unlink(localFilePath, () => {});
    res.json({ success: true, url, type: req.file.mimetype.startsWith('image/') ? 'image' : 'video' });
  } catch (err) {
    res.status(500).json({ error: '上传失败', details: err.message });
  }
});

// 错误处理中间件
router.use((err, req, res, next) => {
  console.error('Upload route error:', err);
  res.status(500).json({ error: '服务器内部错误' });
});

module.exports = router; 
/**
 * 认证中间件
 */
const jwt = require('jsonwebtoken');
const config = require('../config');

function checkAuth(req, res, next) {
  try {
    console.log('认证中间件 - 请求路径:', req.path);
    console.log('认证中间件 - 请求方法:', req.method);
    console.log('认证中间件 - 请求头:', JSON.stringify(req.headers));

    // 检查不同格式的认证头
    const authHeader = req.headers.authorization || req.headers.Authorization;
    console.log('认证中间件 - 认证头:', authHeader);

    // 从认证头中提取token
    let token;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    }

    console.log('认证中间件 - 令牌:', token ? token.substring(0, 10) + '...' : '未提供');

    if (!token) {
      console.log('认证中间件 - 错误: 未提供认证令牌');
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    try {
      // 获取JWT密钥
      const jwtSecret = config.jwtSecret || 'lieyouqi-secret-key';
      console.log('认证中间件 - 使用密钥:', jwtSecret.substring(0, 5) + '...');

      // 验证token
      const decoded = jwt.verify(token, jwtSecret);
      console.log('认证中间件 - 解码成功:', JSON.stringify(decoded));

      // 确保user信息完整
      req.userData = decoded;
      req.user = decoded; // 添加req.user对象，与前端保持一致

      // 如果decoded中没有userId，尝试从其他字段获取
      if (!req.userData.userId && req.userData.id) {
        console.log('认证中间件 - 从id字段获取userId');
        req.userData.userId = req.userData.id;
        req.user.userId = req.userData.id;
      }

      // 确保用户ID存在于req.user.id
      if (!req.user.id && req.user.userId) {
        console.log('认证中间件 - 从userData.userId字段获取id');
        req.user.id = req.user.userId;
      }

      // 添加调试信息
      console.log('认证中间件 - 用户数据:', JSON.stringify(req.userData));
      console.log('认证中间件 - 用户ID:', req.userData.userId);

      // 确保有userId
      if (!req.userData.userId) {
        console.error('认证中间件 - 错误: 令牌中没有userId');
        return res.status(401).json({
          success: false,
          message: '认证令牌中缺少用户ID'
        });
      }

      next();
    } catch (jwtError) {
      console.log('认证中间件 - JWT验证错误:', jwtError.message);
      return res.status(401).json({
        success: false,
        message: '认证令牌无效',
        error: jwtError.message
      });
    }
  } catch (error) {
    console.error('认证中间件 - 未捕获错误:', error);
    return res.status(401).json({
      success: false,
      message: '认证失败',
      error: error.message
    });
  }
}

// 可选认证中间件（不强制要求登录）
function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization || req.headers.Authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有认证头，继续执行但不设置用户信息
      return next();
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return next();
    }

    try {
      const jwtSecret = config.jwtSecret || 'lieyouqi-secret-key';
      const decoded = jwt.verify(token, jwtSecret);

      req.userData = decoded;
      req.user = decoded;
      req.userId = decoded.userId || decoded.id;

      if (!req.userData.userId && req.userData.id) {
        req.userData.userId = req.userData.id;
        req.user.userId = req.userData.id;
      }

      if (!req.user.id && req.user.userId) {
        req.user.id = req.user.userId;
      }

      next();
    } catch (jwtError) {
      // JWT验证失败，继续执行但不设置用户信息
      next();
    }
  } catch (error) {
    // 发生错误，继续执行但不设置用户信息
    next();
  }
}

// 必需认证中间件（强制要求登录）
function requiredAuth(req, res, next) {
  return checkAuth(req, res, next);
}

// 管理员认证中间件
function verifyAdmin(req, res, next) {
  // 先进行基本认证
  checkAuth(req, res, (err) => {
    if (err) return next(err);

    // 检查是否是管理员
    if (!req.userData || !req.userData.isAdmin) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    next();
  });
}

module.exports = {
  checkAuth,
  verifyToken: checkAuth,  // 别名
  optional: optionalAuth,
  required: requiredAuth,
  verifyAdmin
};

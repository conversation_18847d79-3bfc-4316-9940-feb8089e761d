[2025-05-29 23:10:45] Started by user coding
[2025-05-29 23:10:45] Running in Durability level: MAX_SURVIVABILITY
[2025-05-29 23:10:49] [Pipeline] Start of Pipeline
[2025-05-29 23:10:49] [Pipeline] node
[2025-05-29 23:10:49] Running on <PERSON> in /root/workspace
[2025-05-29 23:10:49] [Pipeline] {
[2025-05-29 23:10:49] [Pipeline] stage
[2025-05-29 23:10:49] [Pipeline] { (检出软件包)
[2025-05-29 23:10:49] Stage "检出软件包" skipped due to when conditional
[2025-05-29 23:10:49] [Pipeline] }
[2025-05-29 23:10:49] [Pipeline] // stage
[2025-05-29 23:10:49] [Pipeline] stage
[2025-05-29 23:10:49] [Pipeline] { (检出 ZIP 包)
[2025-05-29 23:10:49] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-29 23:10:49] [Pipeline] }
[2025-05-29 23:10:50] [Pipeline] // stage
[2025-05-29 23:10:50] [Pipeline] stage
[2025-05-29 23:10:50] [Pipeline] { (检出代码仓库)
[2025-05-29 23:10:50] [Pipeline] sh
[2025-05-29 23:10:50] + git clone ****** .
[2025-05-29 23:10:50] Cloning into '.'...
[2025-05-29 23:10:53] [Pipeline] sh
[2025-05-29 23:10:53] + git checkout main
[2025-05-29 23:10:53] Already on 'main'
[2025-05-29 23:10:53] Your branch is up to date with 'origin/main'.
[2025-05-29 23:10:53] [Pipeline] }
[2025-05-29 23:10:53] [Pipeline] // stage
[2025-05-29 23:10:53] [Pipeline] stage
[2025-05-29 23:10:53] [Pipeline] { (写入 dockerfile)
[2025-05-29 23:10:54] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-29 23:10:54] [Pipeline] }
[2025-05-29 23:10:54] [Pipeline] // stage
[2025-05-29 23:10:54] [Pipeline] stage
[2025-05-29 23:10:54] [Pipeline] { (构建 Docker 镜像)
[2025-05-29 23:10:54] [Pipeline] sh
[2025-05-29 23:10:54] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-145-20250529231043
[2025-05-29 23:10:54] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-29 23:10:54] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-29 23:10:54] Configure a credential helper to remove this warning. See
[2025-05-29 23:10:54] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-29 23:10:54] 
[2025-05-29 23:10:54] Login Succeeded
[2025-05-29 23:10:54] [Pipeline] sh
[2025-05-29 23:10:54] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-145-20250529231043 backend
[2025-05-29 23:10:55] #0 building with "default" instance using docker driver
[2025-05-29 23:10:55] 
[2025-05-29 23:10:55] #1 [internal] load build definition from Dockerfile
[2025-05-29 23:10:55] #1 transferring dockerfile: 1.33kB done
[2025-05-29 23:10:55] #1 DONE 0.0s
[2025-05-29 23:10:55] 
[2025-05-29 23:10:55] #2 [internal] load .dockerignore
[2025-05-29 23:10:55] #2 transferring context: 2B done
[2025-05-29 23:10:55] #2 DONE 0.0s
[2025-05-29 23:10:55] 
[2025-05-29 23:10:55] #3 [internal] load metadata for docker.io/library/node:20-alpine3.18
[2025-05-29 23:10:56] #3 DONE 1.2s
[2025-05-29 23:10:56] 
[2025-05-29 23:10:56] #4 [ 1/10] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 23:10:56] #4 resolve docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 0.0s done
[2025-05-29 23:10:56] #4 sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 1.43kB / 1.43kB done
[2025-05-29 23:10:56] #4 sha256:991e09934c996820b55f872e292ca2eec916ae1f65f44bb22404d43d93e52e78 1.16kB / 1.16kB done
[2025-05-29 23:10:56] #4 sha256:37173254e18884787979e033e408a4bae4f2c8b2bddb6f5bc3a727a58fd114e4 7.21kB / 7.21kB done
[2025-05-29 23:10:56] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0B / 3.40MB 0.2s
[2025-05-29 23:10:56] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0B / 42.09MB 0.2s
[2025-05-29 23:10:56] #4 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0B / 1.38MB 0.2s
[2025-05-29 23:10:56] #4 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 0B / 451B 0.2s
[2025-05-29 23:10:57] #4 ...
[2025-05-29 23:10:57] 
[2025-05-29 23:10:57] #5 [internal] load build context
[2025-05-29 23:10:57] #5 transferring context: 35.49MB 0.3s done
[2025-05-29 23:10:57] #5 DONE 0.3s
[2025-05-29 23:10:57] 
[2025-05-29 23:10:57] #4 [ 1/10] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 23:10:57] #4 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 451B / 451B 0.2s done
[2025-05-29 23:10:57] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 2.10MB / 3.40MB 0.5s
[2025-05-29 23:10:57] #4 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 1.38MB / 1.38MB 0.4s done
[2025-05-29 23:10:57] #4 extracting sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8
[2025-05-29 23:10:57] #4 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 3.40MB / 3.40MB 0.5s done
[2025-05-29 23:10:57] #4 extracting sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0.1s done
[2025-05-29 23:10:57] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 3.15MB / 42.09MB 0.8s
[2025-05-29 23:10:58] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 7.34MB / 42.09MB 1.5s
[2025-05-29 23:10:58] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 10.49MB / 42.09MB 2.0s
[2025-05-29 23:10:59] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 13.63MB / 42.09MB 2.5s
[2025-05-29 23:10:59] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 17.83MB / 42.09MB 3.0s
[2025-05-29 23:11:00] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 20.97MB / 42.09MB 3.4s
[2025-05-29 23:11:00] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 24.12MB / 42.09MB 3.8s
[2025-05-29 23:11:00] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 27.26MB / 42.09MB 4.2s
[2025-05-29 23:11:01] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 30.41MB / 42.09MB 4.5s
[2025-05-29 23:11:01] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 34.60MB / 42.09MB 5.0s
[2025-05-29 23:11:02] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 37.75MB / 42.09MB 5.3s
[2025-05-29 23:11:02] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 40.89MB / 42.09MB 5.6s
[2025-05-29 23:11:02] #4 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 42.09MB / 42.09MB 5.7s done
[2025-05-29 23:11:02] #4 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709
[2025-05-29 23:11:03] #4 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 1.4s done
[2025-05-29 23:11:04] #4 extracting sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057
[2025-05-29 23:11:04] #4 extracting sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0.0s done
[2025-05-29 23:11:04] #4 extracting sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d done
[2025-05-29 23:11:04] #4 DONE 7.4s
[2025-05-29 23:11:04] 
[2025-05-29 23:11:04] #6 [ 2/10] RUN apk add --no-cache python3 make g++ gcc netcat-openbsd dos2unix ca-certificates
[2025-05-29 23:11:04] #6 0.206 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/main/x86_64/APKINDEX.tar.gz
[2025-05-29 23:11:06] #6 2.024 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/community/x86_64/APKINDEX.tar.gz
[2025-05-29 23:11:07] #6 2.894 (1/35) Upgrading musl (1.2.4-r2 -> 1.2.4-r3)
[2025-05-29 23:11:07] #6 3.083 (2/35) Installing ca-certificates (20241121-r1)
[2025-05-29 23:11:07] #6 3.271 (3/35) Installing dos2unix (7.4.4-r1)
[2025-05-29 23:11:07] #6 3.444 (4/35) Installing libstdc++-dev (12.2.1_git20220924-r10)
[2025-05-29 23:11:08] #6 4.390 (5/35) Installing zstd-libs (1.5.5-r4)
[2025-05-29 23:11:08] #6 4.571 (6/35) Installing binutils (2.40-r8)
[2025-05-29 23:11:08] #6 4.818 (7/35) Installing libgomp (12.2.1_git20220924-r10)
[2025-05-29 23:11:09] #6 4.992 (8/35) Installing libatomic (12.2.1_git20220924-r10)
[2025-05-29 23:11:09] #6 5.163 (9/35) Installing gmp (6.2.1-r3)
[2025-05-29 23:11:09] #6 5.340 (10/35) Installing isl26 (0.26-r1)
[2025-05-29 23:11:09] #6 5.533 (11/35) Installing mpfr4 (4.2.0_p12-r0)
[2025-05-29 23:11:09] #6 5.712 (12/35) Installing mpc1 (1.3.1-r1)
[2025-05-29 23:11:09] #6 5.884 (13/35) Installing gcc (12.2.1_git20220924-r10)
[2025-05-29 23:11:13] #6 8.830 (14/35) Installing musl-dev (1.2.4-r3)
[2025-05-29 23:11:13] #6 9.180 (15/35) Installing libc-dev (0.7.2-r5)
[2025-05-29 23:11:13] #6 9.351 (16/35) Installing g++ (12.2.1_git20220924-r10)
[2025-05-29 23:11:14] #6 10.48 (17/35) Installing make (4.4.1-r1)
[2025-05-29 23:11:14] #6 10.65 (18/35) Installing libmd (1.0.4-r2)
[2025-05-29 23:11:15] #6 10.83 (19/35) Installing libbsd (0.11.7-r1)
[2025-05-29 23:11:15] #6 11.00 (20/35) Installing netcat-openbsd (1.219-r1)
[2025-05-29 23:11:15] #6 11.17 (21/35) Installing libexpat (2.7.0-r0)
[2025-05-29 23:11:15] #6 11.34 (22/35) Installing libbz2 (1.0.8-r5)
[2025-05-29 23:11:15] #6 11.52 (23/35) Installing libffi (3.4.4-r2)
[2025-05-29 23:11:15] #6 11.69 (24/35) Installing gdbm (1.23-r1)
[2025-05-29 23:11:16] #6 11.86 (25/35) Installing xz-libs (5.4.3-r1)
[2025-05-29 23:11:16] #6 12.04 (26/35) Installing mpdecimal (2.5.1-r2)
[2025-05-29 23:11:16] #6 12.21 (27/35) Installing ncurses-terminfo-base (6.4_p20230506-r0)
[2025-05-29 23:11:16] #6 12.39 (28/35) Installing libncursesw (6.4_p20230506-r0)
[2025-05-29 23:11:16] #6 12.57 (29/35) Installing libpanelw (6.4_p20230506-r0)
[2025-05-29 23:11:16] #6 12.74 (30/35) Installing readline (8.2.1-r1)
[2025-05-29 23:11:17] #6 12.92 (31/35) Installing sqlite-libs (3.41.2-r3)
[2025-05-29 23:11:17] #6 13.11 (32/35) Installing python3 (3.11.12-r1)
[2025-05-29 23:11:18] #6 13.83 (33/35) Installing python3-pycache-pyc0 (3.11.12-r1)
[2025-05-29 23:11:18] #6 14.27 (34/35) Installing pyc (0.1-r0)
[2025-05-29 23:11:18] #6 14.44 (35/35) Installing python3-pyc (3.11.12-r1)
[2025-05-29 23:11:18] #6 14.61 Executing busybox-1.36.1-r5.trigger
[2025-05-29 23:11:18] #6 14.62 Executing ca-certificates-20241121-r1.trigger
[2025-05-29 23:11:18] #6 14.67 OK: 276 MiB in 51 packages
[2025-05-29 23:11:22] #6 DONE 17.5s
[2025-05-29 23:11:22] 
[2025-05-29 23:11:22] #7 [ 3/10] WORKDIR /app
[2025-05-29 23:11:22] #7 DONE 0.1s
[2025-05-29 23:11:22] 
[2025-05-29 23:11:22] #8 [ 4/10] RUN mkdir -p /app/uploads /app/public/qrcode /app/__tmp__
[2025-05-29 23:11:22] #8 DONE 0.4s
[2025-05-29 23:11:22] 
[2025-05-29 23:11:22] #9 [ 5/10] COPY package*.json ./
[2025-05-29 23:11:22] #9 DONE 0.0s
[2025-05-29 23:11:22] 
[2025-05-29 23:11:22] #10 [ 6/10] RUN npm config set registry https://registry.npmmirror.com
[2025-05-29 23:11:22] #10 DONE 0.9s
[2025-05-29 23:11:22] 
[2025-05-29 23:11:22] #11 [ 7/10] RUN npm install --production
[2025-05-29 23:11:23] #11 0.558 npm WARN config production Use `--omit=dev` instead.
[2025-05-29 23:11:27] #11 3.493 npm WARN deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-29 23:11:27] #11 3.736 npm WARN deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-29 23:11:27] #11 3.778 npm WARN deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-29 23:11:27] #11 4.431 
[2025-05-29 23:11:27] #11 4.431 added 221 packages in 4s
[2025-05-29 23:11:27] #11 4.431 
[2025-05-29 23:11:27] #11 4.431 31 packages are looking for funding
[2025-05-29 23:11:27] #11 4.431   run `npm fund` for details
[2025-05-29 23:11:27] #11 DONE 4.7s
[2025-05-29 23:11:27] 
[2025-05-29 23:11:27] #12 [ 8/10] COPY . .
[2025-05-29 23:11:27] #12 DONE 0.1s
[2025-05-29 23:11:28] 
[2025-05-29 23:11:28] #13 [ 9/10] RUN chmod +x /app/cert/initenv.sh &&     chmod +x /app/cert/app-init.sh &&     chmod 777 /app/uploads /app/public/qrcode /app/__tmp__ &&     dos2unix /app/cert/initenv.sh &&     dos2unix /app/cert/app-init.sh &&     sed -i 's/\r$//' /app/cert/initenv.sh &&     sed -i 's/\r$//' /app/cert/app-init.sh
[2025-05-29 23:11:28] #13 0.322 dos2unix: converting file /app/cert/initenv.sh to Unix format...
[2025-05-29 23:11:28] #13 0.323 dos2unix: converting file /app/cert/app-init.sh to Unix format...
[2025-05-29 23:11:28] #13 DONE 0.3s
[2025-05-29 23:11:28] 
[2025-05-29 23:11:28] #14 [10/10] RUN rm -f wait-for-it.sh Dockerfile.cloud generate-cert.js &&     rm -rf test docs temp __tmp__ &&     rm -f config/db-backup.js config/db-init.js config/db-seed.js config/db-validate.js
[2025-05-29 23:11:28] #14 DONE 0.3s
[2025-05-29 23:11:28] 
[2025-05-29 23:11:28] #15 exporting to image
[2025-05-29 23:11:28] #15 exporting layers
[2025-05-29 23:11:30] #15 exporting layers 1.7s done
[2025-05-29 23:11:30] #15 writing image sha256:26ff9cf0a871f50685c2c35855dc625614dd99fcc6e36a8f636c9edd58f70031 done
[2025-05-29 23:11:30] #15 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-145-20250529231043 done
[2025-05-29 23:11:30] #15 DONE 1.7s
[2025-05-29 23:11:30] [Pipeline] sh
[2025-05-29 23:11:30] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-29 23:11:30] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-145-20250529231043
[2025-05-29 23:11:30] + awk {print $NF}
[2025-05-29 23:11:30] + echo 镜像的大小是：461MB
[2025-05-29 23:11:30] 镜像的大小是：461MB
[2025-05-29 23:11:30] [Pipeline] echo
[2025-05-29 23:11:30] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-29 23:11:30] [Pipeline] }
[2025-05-29 23:11:30] [Pipeline] // stage
[2025-05-29 23:11:30] [Pipeline] stage
[2025-05-29 23:11:30] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-29 23:11:30] [Pipeline] sh
[2025-05-29 23:11:31] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-145-20250529231043
[2025-05-29 23:11:31] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-29 23:11:31] f643f9848ab1: Preparing
[2025-05-29 23:11:31] 9362fc2c4f2c: Preparing
[2025-05-29 23:11:31] 8ffa641b43af: Preparing
[2025-05-29 23:11:31] e98102a038ed: Preparing
[2025-05-29 23:11:31] 5ac08dbdb868: Preparing
[2025-05-29 23:11:31] 5194617fa1d6: Preparing
[2025-05-29 23:11:31] 6e697b798729: Preparing
[2025-05-29 23:11:31] 4640893d40fa: Preparing
[2025-05-29 23:11:31] 2f98835e6fe0: Preparing
[2025-05-29 23:11:31] 1709f4dd23fa: Preparing
[2025-05-29 23:11:31] 92f7edb51836: Preparing
[2025-05-29 23:11:31] af0fd4ac5053: Preparing
[2025-05-29 23:11:31] aedc3bda2944: Preparing
[2025-05-29 23:11:31] 92f7edb51836: Waiting
[2025-05-29 23:11:31] af0fd4ac5053: Waiting
[2025-05-29 23:11:31] aedc3bda2944: Waiting
[2025-05-29 23:11:31] 1709f4dd23fa: Layer already exists
[2025-05-29 23:11:31] 92f7edb51836: Layer already exists
[2025-05-29 23:11:31] af0fd4ac5053: Layer already exists
[2025-05-29 23:11:31] aedc3bda2944: Layer already exists
[2025-05-29 23:11:32] 6e697b798729: Pushed
[2025-05-29 23:11:32] f643f9848ab1: Pushed
[2025-05-29 23:11:32] 5ac08dbdb868: Pushed
[2025-05-29 23:11:32] 9362fc2c4f2c: Pushed
[2025-05-29 23:11:32] 4640893d40fa: Pushed
[2025-05-29 23:11:32] 5194617fa1d6: Pushed
[2025-05-29 23:11:37] e98102a038ed: Pushed
[2025-05-29 23:11:52] 8ffa641b43af: Pushed
[2025-05-29 23:12:18] 2f98835e6fe0: Pushed
[2025-05-29 23:12:18] lieyouqi-145-20250529231043: digest: sha256:0c829bd4fe7d71addfb8969d7c4131d5269dcab3373f1bf1ad73ac628223ecb6 size: 3037
[2025-05-29 23:12:18] [Pipeline] }
[2025-05-29 23:12:18] [Pipeline] // stage
[2025-05-29 23:12:18] [Pipeline] }
[2025-05-29 23:12:18] [Pipeline] // node
[2025-05-29 23:12:18] [Pipeline] End of Pipeline
[2025-05-29 23:12:18] Finished: SUCCESS
***
-----------构建lieyouqi-145-----------
2025-05-29 23:10:44 create_build_image : creating
2025-05-29 23:12:24 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-145-----------
2025-05-29 23:12:25 create_eks_virtual_service : creating
2025-05-29 23:12:25 check_eks_virtual_service : process, DescribeVersion_user_error_Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ , message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ ", [service]:[Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "588510c7e9cb6f5f2ab062a97d7a54cb7e34fbe51efe8787e92638ed68895cdf": OCI runtime exec failed: exec failed: cannot exec in a stopped container: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "03d2c193c6dbfbeaf63cff09d5ac01e773a4cb91a1768394072b055b7e8c603b": OCI runtime exec failed: exec failed: unable to start container process: error executing setns process: exit status 1: unknown, message: "",Back-off restarting failed container,Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "6fe71e3e0ed95471f152c4df46352065ef24df3bdbf5f0aa6bc61a5cd27adfff": OCI runtime exec failed: exec failed: cannot exec in a stopped container: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
+ '[' 0 -eq 0 ]
+ echo 'User is root, patching env and certs.'
+ '[' '!' -f /app/cert/certificate.crt ]
+ '[' '!' -f /etc/os-release ]
+ . /etc/os-release
+ NAME='Alpine Linux'
+ ID=alpine
+ VERSION_ID=3.18.6
+ PRETTY_NAME='Alpine Linux v3.18'
+ HOME_URL=https://alpinelinux.org/
+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues
+ echo '[I]: os release is alpine'
+ update-ca-certificates -h
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n+ '[' 0 -eq 0 ]\n+ echo 'User is root, patching env and certs.'\n+ '[' '!' -f /app/cert/certificate.crt ]\n+ '[' '!' -f /etc/os-release ]\n+ . /etc/os-release\n+ NAME='Alpine Linux'\n+ ID=alpine\n+ VERSION_ID=3.18.6\n+ PRETTY_NAME='Alpine Linux v3.18'\n+ HOME_URL=https://alpinelinux.org/\n+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues\n+ echo '[I]: os release is alpine'\n+ update-ca-certificates -h\n",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: rpc error: code = Unknown desc = failed to exec in container: failed to start exec "d62a17b63f6b637969223e538a898c11b466fdb55267af87b7599f7b8fbaf37b": OCI runtime exec failed: exec failed: cannot exec in a stopped container: unknown, message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-145" in Pod "lieyouqi-145-5f6d5859fc-nqjv7_yhrfubyv(74bec9a7-d464-490d-a5b9-6f6ae382a88f)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ , message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ ",]
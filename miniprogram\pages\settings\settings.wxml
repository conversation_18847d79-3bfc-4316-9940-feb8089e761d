<!--pages/settings/settings.wxml-->
<view class="settings-container">
  <!-- <view class="settings-header">
    <view class="header-title">账号设置</view>
  </view> -->

  <view class="settings-content" wx:if="{{!loading}}">
    <!-- 用户ID信息 -->
    <view class="user-id-section">
      <view class="user-id-content">
        <image class="user-avatar" src="{{userInfo.avatar || '/images/icons2/男头像.png'}}"></image>
        <view class="user-info">
          <view class="user-nickname">{{userInfo.nickname || '未设置昵称'}}</view>
          <view class="user-id-text">ID: {{userInfo.id || '未获取到ID'}}</view>
        </view>
      </view>
    </view>

    <!-- 基本信息设置 -->
    <view class="settings-section">
      <view class="section-title">基本信息</view>

      <view class="settings-item" bindtap="editNickname">
        <view class="item-label">昵称</view>
        <view class="item-value">
          <text>{{userInfo.nickname || '未设置'}}</text>
          <view class="arrow-right">》</view>
        </view>
      </view>

      <view class="settings-item" bindtap="editAvatar">
        <view class="item-label">头像</view>
        <view class="item-value">
          <image class="avatar-small" src="{{userInfo.avatar || '/images/icons2/男头像.png'}}"></image>
          <view class="arrow-right">》</view>
        </view>
      </view>

      <view class="settings-item" bindtap="editRegion">
        <view class="item-label">地区</view>
        <view class="item-value">
          <text>{{userInfo.province && userInfo.city ? userInfo.province + ' ' + userInfo.city : '未设置'}}</text>
          <view class="arrow-right">》</view>
        </view>
      </view>
    </view>

    <!-- 账号安全设置 -->
    <view class="settings-section">
      <view class="section-title">账号安全</view>

      <view class="settings-item" bindtap="editPhone">
        <view class="item-label">手机号</view>
        <view class="item-value">
          <text>{{userInfo.phone ? userInfo.phone : '未绑定'}}</text>
          <view class="arrow-right">》</view>
        </view>
      </view>

      <view class="settings-item" bindtap="editPassword">
        <view class="item-label">密码</view>
        <view class="item-value">
          <text>已设置</text>
          <view class="arrow-right">》</view>
        </view>
      </view>

      <view class="settings-item" bindtap="bindWechat">
        <view class="item-label">关联微信</view>
        <view class="item-value">
          <text>{{userInfo.openid ? '已关联' : '未关联'}}</text>
          <view class="arrow-right">》</view>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-button" bindtap="logout">退出登录</view>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>
</view>

/**
 * 购物车路由
 */
const express = require('express');
const { body, param } = require('express-validator');
const cartController = require('../controllers/cartController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取购物车
router.get('/', checkAuth, cartController.getCart);

// 添加到购物车
router.post('/', [
  checkAuth,
  body('productId').isString().withMessage('商品ID必须是字符串'),
  body('quantity').optional().isInt({ min: 1 }).withMessage('数量必须是大于0的整数'),
  validate
], cartController.addToCart);

// 更新购物车
router.put('/:id', [
  checkAuth,
  param('id').isString().withMessage('购物车项ID必须是字符串'),
  body('quantity').isInt({ min: 1 }).withMessage('数量必须是大于0的整数'),
  validate
], cartController.updateCart);

// 从购物车中删除
router.delete('/:id', [
  checkAuth,
  param('id').isString().withMessage('购物车项ID必须是字符串'),
  validate
], cartController.removeFromCart);

// 清空购物车
router.delete('/', checkAuth, cartController.clearCart);

// 获取购物车数量
router.get('/count', checkAuth, cartController.getCartCount);

module.exports = router;

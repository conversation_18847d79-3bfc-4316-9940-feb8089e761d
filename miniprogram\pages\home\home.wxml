<!--pages/home/<USER>
<view class="home-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view class="status-bar" style="height:{{statusBarHeight}}px"></view>
    <view class="nav-bar" style="height: {{menuButtonInfo.height + 12}}px;">
      <view class="search-bar">
        <view class="search-input-container" style="width: {{searchWidth}}px; height: {{menuButtonInfo.height}}px;">
          <input class="search-input" placeholder="搜索感兴趣的内容" placeholder-class="search-placeholder" bindinput="onSearchInput" value="{{searchKeyword}}" confirm-type="search" bindconfirm="onSearchConfirm" />
          <view class="search-btn" bindtap="onSearchConfirm">
            <image src="/images/icons2/搜索.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 占位，防止内容被导航栏遮挡 -->
  <view class="nav-placeholder" style="height:{{statusBarHeight + (menuButtonInfo ? menuButtonInfo.height + 12 : 44)}}px"></view>

  <!-- 轮播广告 -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
    <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
      <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image"></image>
      <view class="banner-title">{{item.title}}</view>
    </swiper-item>
  </swiper>

  <!-- 功能导航 -->
  <view class="nav-grid">
    <view class="nav-row">
      <view class="nav-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}" data-url="{{item.url}}">
        <image class="nav-icon" src="{{item.icon}}"></image>
        <text class="nav-text">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 内容分类 -->
  <view class="tab-container" style="top: {{statusBarHeight + 44}}px;">
    <view class="tab-bar">
      <view class="tab-item {{currentTab === index ? 'active' : ''}}"
            wx:for="{{tabs}}"
            wx:key="*this"
            bindtap="switchTab"
            data-index="{{index}}">
        <text>{{item}}</text>
        <view class="tab-line" wx:if="{{currentTab === index}}"></view>
      </view>
      <view class="filter-btn" bindtap="onFilterTap">
        <image src="/images/icons2/筛选.png" class="filter-icon"></image>
        <text>筛选</text>
        <view class="expand-icon"></view>
      </view>
    </view>
  </view>

  <!-- 地区回退提示 -->
  <view class="region-fallback-tip" wx:if="{{hasRegionFallback && regionFallbackTip}}">
    <image class="region-fallback-icon" src="/images/icons2/地区.png"></image>
    <view class="region-fallback-content">
      <text class="region-fallback-text">{{regionFallbackTip}}</text>
    </view>
  </view>
  
  <!-- 信息流列表 -->
  <view class="post-list">
    <view class="post-item card" wx:for="{{posts}}" wx:key="id" bindtap="onPostTap" data-id="{{item.id}}">
      <!-- 用户信息栏 -->
      <view class="post-user">
        <image class="avatar" src="{{item.userInfo && item.userInfo.avatarUrl ? item.userInfo.avatarUrl : '/images/icons2/默认头像.png'}}" catchtap="onUserTap" data-id="{{item.userInfo && item.userInfo.id}}"></image>
        <view class="user-info">
          <view class="username" catchtap="onUserTap" data-id="{{item.userInfo && item.userInfo.id}}">{{item.userInfo && (item.userInfo.nickName || item.userInfo.nickname) ? (item.userInfo.nickName || item.userInfo.nickname) : '匿名用户'}}</view>
          <view class="post-time">{{item.displayTime}}</view>
        </view>
        <view class="follow-btn {{(item.userInfo && (item.userInfo.id === globalUserInfo.id || item.userInfo.userId === globalUserInfo.id)) ? 'disabled' : (item.isFollowed ? 'followed' : '')}}"
          catchtap="onFollowTap"
          data-index="{{index}}"
          wx:if="{{item.userInfo}}"
        >
          {{
            (item.userInfo.id === globalUserInfo.id || item.userInfo.userId === globalUserInfo.id) ? '关注' : (item.isFollowed ? '已关注' : '关注')
          }}
        </view>
      </view>

      <!-- 内容区 -->
      <view class="post-content">
        <text class="content-text">{{item.content}}</text>

        <!-- 视频模式 -->
        <video wx:if="{{item.video}}" src="{{item.video}}" controls="true" class="post-video"></video>

        <!-- 图片模式：只在有图片时才渲染 -->
        <block wx:if="{{item.images && item.images.length > 0}}">
          <!-- 单图模式 -->
          <view class="single-image-wrapper" wx:if="{{item.images.length === 1}}" style="height:{{item.imageDisplayHeight || 'auto'}}px;">
            <image class="single-image"
                   src="{{item.images[0]}}"
                   mode="aspectFill"
                   style="width:100%;height:100%;"
                   bindload="onSingleImageLoad"
                   data-index="{{index}}"
                   binderror="onImageError"/>
          </view>
          <!-- 多图模式 -->
          <view class="image-grid" wx:if="{{item.images.length > 1}}" style="display:flex;flex-direction:row;align-items:center;width:100%;gap:4px;">
            <image wx:for="{{item.images}}" wx:for-item="image" wx:for-index="imgIndex" wx:key="imgIndex"
                   src="{{image}}"
                   class="grid-image"
                   style="flex:1 1 0;width:auto;height:{{item.multiImageHeights && item.multiImageHeights[imgIndex] ? item.multiImageHeights[imgIndex] : 'auto'}}px;max-width:calc((100% - {{(item.images.length-1)*4}}px)/{{item.images.length}});"
                   mode="aspectFill"
                   bindload="onMultiImageLoad"
                   data-index="{{index}}"
                   data-imgindex="{{imgIndex}}"
                   binderror="onImageError">
            </image>
          </view>
        </block>
      </view>

      <!-- 商品关联区 -->
      <view class="product-link" wx:if="{{item.product}}" bindtap="onProductTap" data-index="{{index}}">
        <image class="product-image" src="{{item.product.imageUrl}}"></image>
        <view class="product-info">
          <view class="product-name">{{item.product.name}}</view>
          <view class="product-price">¥{{item.product.price}}</view>
        </view>
        <view class="buy-btn">购买</view>
      </view>

      <!-- 发布区域和主题类别 -->
      <view class="post-meta" wx:if="{{item.region || item.topic || item.topicsArray}}">
        <view class="post-region" wx:if="{{item.region}}">{{item.regionText}}</view>
        <!-- 多主题标签显示 -->
        <block wx:if="{{item.topicsArray && item.topicsArray.length > 0}}">
          <view class="post-topic" wx:for="{{item.topicsArray}}" wx:for-item="topicItem" wx:key="*this">{{topicItem}}</view>
        </block>
        <!-- 兼容旧版单主题显示 -->
        <block wx:elif="{{item.topic}}">
          <view class="post-topic">{{item.topic}}</view>
        </block>
      </view>

      <!-- 互动栏 -->
      <view class="interaction-bar">
        <view class="interaction-item" catchtap="onLikeTap" data-index="{{index}}">
          <image src="{{item.isLiked ? '/images/icons2/已点赞.png' : '/images/icons2/未点赞.png'}}"></image>
          <text>({{item.likeCount || 0}})</text>
        </view>
        <view class="interaction-item" catchtap="onCommentTap" data-id="{{item.id}}">
          <image src="/images/icons2/评论.png"></image>
          <text>({{item.commentCount || 0}})</text>
        </view>
        <view class="interaction-item" catchtap="onShareTap" data-index="{{index}}">
          <image src="/images/icons2/分享.png"></image>
          <text>转发 ({{item.shareCount || 0}})</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!loading && !hasMore && posts.length > 0}}">
    <text>没有更多内容了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && posts.length === 0}}">
    <!-- 关注卡片的特殊空状态 -->
    <block wx:if="{{currentTab === 1}}">
      <image class="empty-icon" src="/images/icons2/暂无数据.png"></image>
      <!-- 没有关注任何人的特殊提示 -->
      <text class="empty-text" wx:if="{{showNoFollowsTip}}">{{noFollowsMessage || '您还没有关注任何人'}}</text>
      <!-- 已关注但没有帖子的提示 -->
      <text class="empty-text" wx:else>您关注的用户暂无发贴</text>
      <view class="go-explore-btn" bindtap="switchToRecommend">去看看</view>
    </block>
    <!-- 附近卡片的特殊空状态 -->
    <block wx:elif="{{currentTab === 3}}">
      <image class="empty-icon" src="/images/icons2/暂无数据.png"></image>
      <!-- 未设置地区的特殊提示 -->
      <text class="empty-text" wx:if="{{showNoFollowsTip}}">{{noFollowsMessage || '请设置您的所在地区'}}</text>
      <!-- 当前地区没有帖子的提示 -->
      <text class="empty-text" wx:else>您当前所在地区暂无帖子</text>
      <view class="retry-btn" bindtap="getPosts">重新加载</view>
    </block>
    <!-- 其他卡片的空状态 -->
    <block wx:else>
      <image class="empty-icon" src="/images/icons2/暂无数据.png"></image>
      <text class="empty-text">暂无内容，请稍后再试</text>
      <view class="retry-btn" bindtap="getPosts">重新加载</view>
    </block>
  </view>

  <!-- 悬浮回到顶部按钮 -->
  <view class="back-to-top-btn" bindtap="scrollToTop">
    <image src="/images/icons2/返回顶部.png" class="back-to-top-icon" />
  </view>
  
  <!-- 筛选抽屉组件 -->
  <filter-drawer 
    visible="{{showFilterDrawer}}" 
    initialFilter="{{filterParams}}" 
    bind:close="onFilterClose" 
    bind:confirm="onFilterConfirm">
  </filter-drawer>
</view>

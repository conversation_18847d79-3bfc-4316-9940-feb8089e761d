// pages/settings/nickname.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    nickname: '',
    loading: false
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    // 先从全局获取
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        nickname: app.globalData.userInfo.nickname || ''
      });
      return;
    }

    // 如果全局没有，从服务器获取
    const userInfo = wx.getStorageSync('userInfo');
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        if (res.success) {
          // 更新全局用户信息
          app.globalData.userInfo = res.data;
          app.globalData.isLogin = true;

          this.setData({
            nickname: res.data.nickname || ''
          });
        } else {
          // 如果API调用失败，尝试从本地存储获取
          this.getLocalUserInfo();
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        // 如果API调用失败，尝试从本地存储获取
        this.getLocalUserInfo();
      });
  },

  // 从本地存储获取用户信息
  getLocalUserInfo: function() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        const app = getApp();
        app.globalData.userInfo = res.data;
        app.globalData.isLogin = true;

        this.setData({
          nickname: res.data.nickname || ''
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 输入昵称
  inputNickname: function(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  // 保存昵称
  saveNickname: function() {
    const { nickname } = this.data;

    if (!nickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    // 检查token是否存在
    const token = wx.getStorageSync('token');
    console.log('保存昵称时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');

    if (!token) {
      wx.showToast({
        title: '未登录，请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/auth/auth'
        });
      }, 1500);
      return;
    }

    // 直接保存
    this.doSaveNickname(nickname);
  },

  // 执行保存昵称操作
  doSaveNickname: function(nickname) {
    console.log('开始更新昵称:', nickname);
    userApi.updateUserInfo({ nickname })
      .then(res => {
        console.log('更新昵称响应:', res);
        this.setData({ loading: false });

        if (res.success) {
          // 获取最新的用户信息
          userApi.getUserInfo(wx.getStorageSync('userInfo').id)
            .then(infoRes => {
              console.log('修改昵称后获取最新用户信息结果:', infoRes);
              if (infoRes.success) {
                console.log('修改昵称后获取最新用户信息成功:', infoRes.data);
                const app = getApp();

                // 更新全局用户信息
                app.globalData.userInfo = infoRes.data;

                // 更新本地存储
                wx.setStorage({
                  key: 'userInfo',
                  data: infoRes.data
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '昵称修改成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              } else {
                // 如果获取最新信息失败，至少更新本地的昵称
                const app = getApp();

                // 直接更新昵称
                if (app.globalData.userInfo) {
                  app.globalData.userInfo.nickname = nickname;
                } else {
                  app.globalData.userInfo = { nickname };
                }

                // 更新本地存储
                wx.getStorage({
                  key: 'userInfo',
                  success: (result) => {
                    const userInfo = result.data;
                    userInfo.nickname = nickname;
                    wx.setStorage({
                      key: 'userInfo',
                      data: userInfo
                    });
                  },
                  fail: () => {
                    // 如果本地没有存储，创建一个新的
                    wx.setStorage({
                      key: 'userInfo',
                      data: { nickname }
                    });
                  }
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '昵称修改成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              }
            })
            .catch(err => {
              console.error('修改昵称后获取用户信息失败:', err);

              // 如果获取最新信息失败，至少更新本地的昵称
              const app = getApp();

              // 直接更新昵称
              if (app.globalData.userInfo) {
                app.globalData.userInfo.nickname = nickname;
              } else {
                app.globalData.userInfo = { nickname };
              }

              // 更新本地存储
              wx.getStorage({
                key: 'userInfo',
                success: (result) => {
                  const userInfo = result.data;
                  userInfo.nickname = nickname;
                  wx.setStorage({
                    key: 'userInfo',
                    data: userInfo
                  });
                },
                fail: () => {
                  // 如果本地没有存储，创建一个新的
                  wx.setStorage({
                    key: 'userInfo',
                    data: { nickname }
                  });
                }
              });

              // 标记需要刷新个人中心页面
              app.globalData.needRefreshProfile = true;

              wx.showToast({
                title: '昵称修改成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            });
        } else {
          wx.showToast({
            title: res.message || '修改失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('更新昵称请求失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
})

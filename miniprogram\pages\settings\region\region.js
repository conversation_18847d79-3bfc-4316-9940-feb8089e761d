const { userApi } = require('../../../utils/api');

Page({
  data: {
    loading: false,
    region: ['北京市', '北京市', '东城区']
  },

  onLoad() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.region) {
      // 将地区字符串分割成数组
      const regionArray = userInfo.region.split(' ');
      if (regionArray.length === 3) {
        this.setData({
          region: regionArray
        });
      }
    }
  },

  // 地区选择改变
  onRegionChange(e) {
    this.setData({
      region: e.detail.value
    });
  },

  // 保存地区设置
  async saveRegion() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const regionString = this.data.region.join(' ');
      const res = await userApi.updateUserInfo({ region: regionString });
      
      if (res.success) {
        // 更新本地存储的用户信息
        const userInfo = wx.getStorageSync('userInfo');
        userInfo.region = regionString;
        wx.setStorageSync('userInfo', userInfo);
        
        // 更新全局用户信息
        const app = getApp();
        app.globalData.userInfo = userInfo;
        
        wx.showToast({
          title: '地区设置成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: '地区设置失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存地区失败:', error);
      wx.showToast({
        title: '地区设置失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  }
}); 
/* pages/search/search.wxss */
.search-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #F5F5F5;
}

/* 搜索栏 */
.search-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input-container {
  flex: 1;
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 18px;
  display: flex;
  align-items: center;
  padding: 0 4px 0 12px; /* 右侧内边距减小，为搜索按钮留出空间 */
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  padding-left: 12px;
  padding-right: 120px; /* 为搜索按钮留出足够空间 */
}

.search-placeholder {
  font-size: 14px;
  color: #999999;
}

.search-btn {
  width: 80px; /* 搜索框内宽的80%左右 */
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 6px;
  background-color: #07C160;
  border-radius: 15px;
}

.search-btn image {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.search-btn text {
  color: #FFFFFF;
  font-size: 14px;
}

.clear-btn {
  font-size: 14px;
  color: #333333;
  margin-left: 12px;
  padding: 0 8px;
}

/* 搜索历史 */
.search-history {
  background-color: #FFFFFF;
  padding: 16px;
  margin-top: 10px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-title {
  font-size: 15px;
  color: #333333;
  font-weight: bold;
}

.clear-history {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  font-size: 18px;
  line-height: 1;
}

.history-list {
  display: flex;
  flex-direction: column;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #F5F5F5;
}

.history-icon {
  font-size: 16px;
  margin-right: 8px;
  line-height: 1;
}

.history-text {
  font-size: 14px;
  color: #666666;
}

/* 搜索结果 */
.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.result-count {
  padding: 10px 16px;
  font-size: 14px;
  color: #999999;
  background-color: #FFFFFF;
  border-bottom: 1px solid #F5F5F5;
}

/* 商品列表样式 */
.product-list {
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  background-color: #FFFFFF;
}

.product-item {
  width: calc(50% - 10px);
  margin: 5px;
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.product-item .product-image {
  width: 100%;
  height: 160px;
  background-color: #F5F5F5;
}

.product-item .product-info {
  padding: 8px;
}

.product-item .product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 40px;
}

.product-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.product-item .product-price {
  font-size: 16px;
  color: #FF4D4F;
  font-weight: bold;
  margin-right: 6px;
}

.product-item .product-original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.product-item .product-shop {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
}

.product-item .product-sales {
  font-size: 12px;
  color: #999999;
}

/* 信息流列表样式 - 复用首页样式 */
.post-list {
  padding: 0 16px;
}

.post-item {
  margin-bottom: 12px;
  padding: 16px;
  background-color: #FFFFFF;
  border-radius: 8px;
}

.post-user {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 2px;
}

.post-time {
  font-size: 12px;
  color: #999999;
}

.follow-btn {
  padding: 4px 12px;
  font-size: 12px;
  color: #FF4D4F;
  border: 1px solid #FF4D4F;
  border-radius: 15px;
}

.post-content {
  margin-bottom: 12px;
}

.content-text {
  font-size: 15px;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.single-image {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
}

.grid-image {
  width: calc((100% - 8px) / 3);
  height: calc((100% - 8px) / 3);
  margin-right: 4px;
  margin-bottom: 4px;
  border-radius: 4px;
}

.grid-image:nth-child(3n) {
  margin-right: 0;
}

.product-link {
  display: flex;
  align-items: center;
  background-color: #F8F8F8;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 8px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.product-price {
  font-size: 14px;
  color: #FF4D4F;
  font-weight: bold;
}

.buy-btn {
  padding: 4px 12px;
  font-size: 12px;
  color: #FFFFFF;
  background-color: #FF4D4F;
  border-radius: 15px;
}

.interaction-bar {
  display: flex;
  height: 48px;
  border-top: 1px solid #F5F5F5;
  padding-top: 10px;
  margin-top: 4px;
}

.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interaction-item image {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

.interaction-item text {
  font-size: 13px;
  color: #666666;
  line-height: 1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  font-size: 60px;
  margin-bottom: 16px;
  line-height: 1;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

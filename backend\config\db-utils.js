const mysql = require('mysql2/promise');
const config = require('./config');

async function testConnection() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });
    await connection.end();
    return true;
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return false;
  }
}

module.exports = {
  testConnection
}; 
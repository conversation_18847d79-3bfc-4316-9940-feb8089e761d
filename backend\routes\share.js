/**
 * 分享统计路由
 */
const express = require('express');
const { body, query, param } = require('express-validator');
const shareController = require('../controllers/shareController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 记录分享行为
router.post('/record', [
  body('userId').isString().withMessage('用户ID必须是字符串'),
  body('shareType').isIn(['wechat', 'save_image', 'qrcode']).withMessage('分享类型不正确'),
  body('nickname').optional().isString().withMessage('昵称必须是字符串'),
  validate
], shareController.recordShare);

// 获取分享统计
router.get('/statistics', [
  query('userId').optional().isString().withMessage('用户ID必须是字符串'),
  query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
  query('endDate').optional().isISO8601().withMessage('结束日期格式不正确'),
  validate
], shareController.getShareStatistics);

// 获取用户分享总数
router.get('/count/:userId', [
  param('userId').isString().withMessage('用户ID必须是字符串'),
  validate
], shareController.getUserShareCount);

module.exports = router;

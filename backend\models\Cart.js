/**
 * 购物车模型
 */
const db = require('../config/db');

class Cart {
  static async findAll(userId) {
    return await db.query(`
      SELECT c.*, p.name, p.price, p.originalPrice, p.images, p.shopName
      FROM cart c
      LEFT JOIN products p ON c.productId = p.id
      WHERE c.userId = ?
      ORDER BY c.createTime DESC
    `, [userId]);
  }

  static async findById(id) {
    const result = await db.query('SELECT * FROM cart WHERE id = ?', [id]);
    return result.length > 0 ? result[0] : null;
  }

  static async findByUserAndProduct(userId, productId) {
    const result = await db.query('SELECT * FROM cart WHERE userId = ? AND productId = ?', [userId, productId]);
    return result.length > 0 ? result[0] : null;
  }

  static async create(cartData) {
    cartData.createTime = cartData.createTime || Date.now();
    cartData.updateTime = cartData.updateTime || Date.now();

    // 构建插入SQL
    const sql = `
      INSERT INTO cart (id, userId, productId, name, price, image, quantity, selected, createTime, updateTime)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      cartData.id,
      cartData.userId,
      cartData.productId,
      cartData.name,
      cartData.price,
      cartData.image,
      cartData.quantity || 1,
      cartData.selected !== undefined ? cartData.selected : true,
      cartData.createTime,
      cartData.updateTime
    ];
    await db.query(sql, params);
    return cartData;
  }

  static async update(id, cartData) {
    cartData.updateTime = Date.now();
    // 构建更新SQL
    const fields = [];
    const params = [];
    if (cartData.quantity !== undefined) { fields.push('quantity = ?'); params.push(cartData.quantity); }
    if (cartData.selected !== undefined) { fields.push('selected = ?'); params.push(cartData.selected); }
    fields.push('updateTime = ?'); params.push(cartData.updateTime);
    const sql = `UPDATE cart SET ${fields.join(', ')} WHERE id = ?`;
    params.push(id);
    await db.query(sql, params);
    return await this.findById(id);
  }

  static async remove(id) {
    const sql = 'DELETE FROM cart WHERE id = ?';
    await db.query(sql, [id]);
    return { id };
  }

  static async removeByUser(userId) {
    const sql = 'DELETE FROM cart WHERE userId = ?';
    await db.query(sql, [userId]);
    return { userId };
  }

  static async count(userId) {
    const result = await db.query('SELECT COUNT(*) as total FROM cart WHERE userId = ?', [userId]);
    return result[0].total;
  }
}

module.exports = Cart;

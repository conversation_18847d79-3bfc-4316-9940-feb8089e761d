/* pages/settings/nickname.wxss */
.nickname-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.nickname-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.nickname-content {
  padding: 30rpx;
}

.input-area {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  position: relative;
  margin-bottom: 20rpx;
}

.nickname-input {
  height: 80rpx;
  font-size: 30rpx;
  color: #333333;
  width: 100%;
}

.input-counter {
  position: absolute;
  right: 30rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: #999999;
}

.tips {
  font-size: 26rpx;
  color: #999999;
  margin: 20rpx 0 40rpx;
  padding: 0 10rpx;
}

.save-button {
  background-color: #ff4d4f;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}

.save-button.disabled {
  background-color: #cccccc;
}

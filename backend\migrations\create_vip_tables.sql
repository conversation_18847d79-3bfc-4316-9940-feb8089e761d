-- VIP会员系统数据库表
-- 创建日期: 2025-05-29

-- 会员等级表
CREATE TABLE IF NOT EXISTS vip_levels (
  id INT AUTO_INCREMENT PRIMARY KEY,
  level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
  level_name VARCHAR(50) NOT NULL COMMENT '会员等级名称',
  description TEXT COMMENT '会员等级描述',
  icon VARCHAR(255) COMMENT '等级图标URL',
  price DECIMAL(10, 2) NOT NULL DEFAULT 0 COMMENT '会员价格',
  duration INT NOT NULL DEFAULT 365 COMMENT '会员有效期(天)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY (level_code)
);

-- 会员权益表
CREATE TABLE IF NOT EXISTS vip_benefits (
  id INT AUTO_INCREMENT PRIMARY KEY,
  level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
  benefit_name VARCHAR(100) NOT NULL COMMENT '权益名称',
  benefit_desc TEXT COMMENT '权益描述',
  sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX (level_code)
);

-- 用户会员表
CREATE TABLE IF NOT EXISTS user_vip (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
  start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  expire_time DATETIME NOT NULL COMMENT '过期时间',
  is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否有效',
  payment_id VARCHAR(50) COMMENT '支付ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY (user_id),
  INDEX (level_code)
);

-- 初始化会员等级数据
INSERT INTO vip_levels (level_code, level_name, description, price, duration) 
VALUES 
('free', '普通用户', '基础账户，可以浏览和使用基本功能', 0, 3650),
('annual', '年度会员', '标准会员，享受平台大部分功能和服务', 198, 365),
('super', '超级会员', '高级会员，享受更多权益和服务', 398, 365),
('v3', 'V3会员', '尊贵会员，享受全部平台权益和个性化服务', 998, 365);

-- 初始化会员权益数据
INSERT INTO vip_benefits (level_code, benefit_name, benefit_desc, sort_order)
VALUES
-- 年度会员权益
('annual', '专属会员标识与徽章', '在个人主页和评论中展示专属会员标识', 1),
('annual', '7×12小时专属客服', '优先获得客服响应和解决问题', 2),
('annual', '免费参与会员专属活动', '参加平台定期举办的线上专属活动', 3),
('annual', '专属文档模板使用权', '使用会员专属的法律和商业文档模板', 4),
('annual', '每月免费咨询1次', '每月可获得1次免费专业咨询服务', 5),

-- 超级会员权益
('super', '全部年度会员权益', '包含年度会员的所有权益', 1),
('super', '专享85折服务优惠', '平台所有付费服务可享受85折优惠', 2),
('super', '高级会员专属礼包', '每季度获得一次会员专属礼包', 3),
('super', '优先参与线下活动', '优先参与平台组织的线下活动', 4),
('super', '每月免费咨询3次', '每月可获得3次免费专业咨询服务', 5),
('super', '专属高级模板库', '使用更高级、更全面的文档模板库', 6),

-- V3会员权益
('v3', '超级会员全部权益', '包含超级会员的所有权益', 1),
('v3', '一对一专属服务顾问', '指定专属顾问提供一对一服务', 2),
('v3', 'V3专属高级定制服务', '根据个人需求提供定制化服务', 3),
('v3', '无限制法律咨询', '不限次数的法律咨询服务', 4),
('v3', '线下活动免费参与', '免费参与所有线下活动', 5),
('v3', '项目优先对接', '业务需求优先对接专业服务团队', 6);

-- 为已有用户初始化会员数据（默认为普通用户）
INSERT INTO user_vip (user_id, level_code, start_time, expire_time)
SELECT id, 'free', NOW(), DATE_ADD(NOW(), INTERVAL 10 YEAR) FROM users
ON DUPLICATE KEY UPDATE level_code = 'free';

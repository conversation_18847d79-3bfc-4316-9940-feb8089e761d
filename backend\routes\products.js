/**
 * 商品路由
 */
const express = require('express');
const { query, param } = require('express-validator');
const productController = require('../controllers/productController');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取商品列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
  query('categoryId').optional().isString().withMessage('分类ID必须是字符串'),
  query('subCategoryId').optional().isString().withMessage('子分类ID必须是字符串'),
  query('keyword').optional().isString().withMessage('关键词必须是字符串'),
  query('sortType').optional().isIn(['default', 'price_asc', 'price-asc', 'price_desc', 'price-desc', 'sales']).withMessage('排序类型不正确'),
  validate
], productController.getProducts);

// 获取商品详情
router.get('/:id', [
  param('id').isString().withMessage('商品ID必须是字符串'),
  validate
], productController.getProductById);

// 获取分类列表
router.get('/categories', productController.getCategories);

// 获取商城分类列表
router.get('/shop/categories', productController.getShopCategories);

// 获取商城子分类列表
router.get('/shop/subcategories', productController.getShopSubCategories);

// 获取轮播图列表
router.get('/banners', productController.getBanners);

module.exports = router;

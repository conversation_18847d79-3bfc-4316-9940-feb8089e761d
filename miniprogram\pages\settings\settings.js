// pages/settings/settings.js
const loginStateManager = require('../../utils/login-state-manager');
const userIdentity = require('../../utils/user-identity');
// 已移除调试工具类引用

Page({
  data: {
    userInfo: null,
    isLogin: false,
    loading: true
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    // 检查登录状态是否发生变化
    const app = getApp();

    // 如果全局状态中有登录状态变化的标记，或者登录状态与当前页面不一致，则重新检查登录状态
    if (app.globalData.needRefreshProfile || app.globalData.isLogin !== this.data.isLogin) {
      console.log('检测到登录状态变化，重新检查登录状态');
      this.checkLoginStatus();
    } else if (this.data.isLogin && app.globalData.userInfo) {
      // 检查全局用户信息是否与当前页面不一致
      if (app.globalData.userInfo.nickname !== this.data.userInfo.nickname ||
          app.globalData.userInfo.avatar !== this.data.userInfo.avatar ||
          app.globalData.userInfo.phone !== this.data.userInfo.phone ||
          app.globalData.userInfo.province !== this.data.userInfo.province ||
          app.globalData.userInfo.city !== this.data.userInfo.city) {
        console.log('检测到用户信息变化，更新页面显示');
        this.setData({
          userInfo: app.globalData.userInfo
        });
      }
    }
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const app = getApp();
    this.setData({ loading: true });

    // 设置页面检查登录状态

    // 使用登录状态管理器验证登录状态
    loginStateManager.validateLoginState()
      .then(result => {
        // 设置页面登录状态验证结果

        if (result.isValid) {
          // 登录状态有效
          const userInfo = result.userInfo || wx.getStorageSync('userInfo');

          // 更新全局用户信息
          app.globalData.userInfo = userInfo;
          app.globalData.isLogin = true;

          this.setData({
            userInfo: userInfo,
            isLogin: true,
            loading: false
          });

          // 设置页面用户信息已更新
        } else {
          // 登录状态无效
          // 设置页面登录状态无效
          this.handleNotLoggedIn();
        }
      })
      .catch(err => {
        // 设置页面验证登录状态出错
        this.handleNotLoggedIn();
      });
  },

  // 使用本地存储的用户信息
  useLocalUserInfo: function() {
    const app = getApp();

    // 设置页面尝试使用本地存储的用户信息

    // 获取本地存储的登录状态
    const loginState = loginStateManager.getLoginState();

    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        const userInfo = res.data;

        // 验证用户ID一致性
        if (loginState && loginState.userId !== userInfo.id) {
          // 本地存储的用户信息与登录状态不一致

          // 清除登录状态
          loginStateManager.clearLoginState();

          // 处理未登录状态
          this.handleNotLoggedIn();
          return;
        }

        app.globalData.userInfo = userInfo;
        app.globalData.isLogin = true;
        this.setData({
          userInfo: userInfo,
          isLogin: true,
          loading: false
        });

        // 设置页面使用本地用户信息
      },
      fail: () => {
        // 设置页面未找到本地用户信息
        this.handleNotLoggedIn();
      }
    });
  },

  // 处理未登录状态
  handleNotLoggedIn: function() {
    const app = getApp();

    // 清除登录状态
    loginStateManager.clearLoginState();

    // 更新全局数据
    app.globalData.userInfo = null;
    app.globalData.isLogin = false;

    this.setData({
      userInfo: null,
      isLogin: false,
      loading: false
    });

    // 设置页面处理未登录状态

    // 未登录时跳转到登录页
    wx.navigateTo({
      url: '/pages/auth/auth'
    });
  },

  // 修改昵称
  editNickname: function() {
    wx.navigateTo({
      url: '/pages/settings/nickname'
    });
  },

  // 修改头像
  editAvatar: function() {
    wx.navigateTo({
      url: '/pages/settings/avatar'
    });
  },

  // 修改地区
  editRegion: function() {
    wx.navigateTo({
      url: '/pages/settings/region'
    });
  },

  // 修改手机号
  editPhone: function() {
    wx.navigateTo({
      url: '/pages/settings/phone'
    });
  },

  // 修改密码
  editPassword: function() {
    wx.navigateTo({
      url: '/pages/settings/password'
    });
  },

  // 关联微信
  bindWechat: function() {
    wx.navigateTo({
      url: '/pages/settings/wechat'
    });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 使用登录状态管理器登出
          loginStateManager.logout()
            .then(() => {
              // 更新全局数据
              const app = getApp();
              app.globalData.userInfo = null;
              app.globalData.isLogin = false;

              // 返回到上一页
              wx.navigateBack();
            });
        }
      }
    });
  }
})

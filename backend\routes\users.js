/**
 * 用户路由
 */
const express = require('express');
const { body } = require('express-validator');
const userController = require('../controllers/userController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 用户登录
router.post('/login', [
  body('username').isString().withMessage('用户名必须是字符串'),
  body('password').isString().withMessage('密码必须是字符串'),
  validate
], userController.login);

// 账号密码登录（与上面的/login路由功能相同，为了兼容前端API）
router.post('/login/account', [
  body('username').isString().withMessage('用户名必须是字符串'),
  body('password').isString().withMessage('密码必须是字符串'),
  validate
], userController.login);

// 手机号登录
router.post('/login/phone', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('code').isString().withMessage('验证码必须是字符串'),
  validate
], userController.loginByPhone);

// 用户注册
router.post('/register', [
  body('username').isString().withMessage('用户名必须是字符串'),
  body('password').isString().withMessage('密码必须是字符串'),
  body('nickname').optional().isString().withMessage('昵称必须是字符串'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.register);

// 获取用户信息
router.get('/info', checkAuth, userController.getUserInfo);

// 更新用户信息
router.put('/info', [
  checkAuth,
  body('nickname').optional().isString().withMessage('昵称必须是字符串'),
  body('avatar').optional().isString().withMessage('头像必须是字符串'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('email').optional().isEmail().withMessage('邮箱格式不正确'),
  body('gender').optional().isIn(['male', 'female', 'other']).withMessage('性别值无效'),
  body('country').optional().isString().withMessage('国家必须是字符串'),
  body('province').optional().isString().withMessage('省份必须是字符串'),
  body('city').optional().isString().withMessage('城市必须是字符串'),
  validate
], userController.updateUserInfo);

// 发送验证码
router.post('/send-code', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.sendVerificationCode);

// 更新密码
router.put('/password', [
  checkAuth,
  body('oldPassword').isString().withMessage('旧密码必须是字符串'),
  body('newPassword').isString().withMessage('新密码必须是字符串'),
  validate
], userController.updatePassword);

// 绑定微信
router.post('/bind/wechat', [
  checkAuth,
  body('code').isString().withMessage('微信授权码必须是字符串'),
  validate
], userController.bindWechat);

// 解绑微信
router.post('/unbind/wechat', checkAuth, userController.unbindWechat);

// 发送验证码
router.post('/send-verify-code', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  validate
], userController.sendVerifyCode);

// 通过手机号重置密码
router.post('/reset-password', [
  body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
  body('code').isString().withMessage('验证码必须是字符串'),
  body('newPassword').isString().withMessage('新密码必须是字符串'),
  validate
], userController.resetPasswordByPhone);

// 关注用户
router.post('/follow', checkAuth, userController.followUser);
// 取消关注
router.post('/unfollow', checkAuth, userController.unfollowUser);
// 检查关注状态
router.get('/follow/status', checkAuth, userController.checkFollowStatus);

// 我的关注列表
router.get('/my-follow', checkAuth, userController.getMyFollowList);
// 我的粉丝列表
router.get('/my-fans', checkAuth, userController.getMyFansList);

// 我的推广用户列表
router.get('/my-promotion', checkAuth, userController.getMyPromotionList);

// 我的评论记录
router.get('/my-comments', checkAuth, userController.getMyComments);

// 帖子评论列表
router.get('/post-comments', userController.getPostComments);

// 发表评论
router.post('/post-comment', checkAuth, userController.addPostComment);

module.exports = router;

// pages/search/search.js
const { productApi, postApi } = require('../../utils/api');

Page({
  data: {
    keyword: '',
    searchHistory: [],
    searchResults: [],
    loading: false,
    showResults: false,
    searchType: 'post', // 默认搜索类型为帖子
    productResults: [] // 存储商品搜索结果
  },

  onLoad: function (options) {
    // 从本地存储获取搜索历史
    this.getSearchHistory();

    // 设置搜索类型
    if (options.type && options.type === 'product') {
      this.setData({
        searchType: 'product',
        placeholder: '搜索商品'
      });
    } else {
      this.setData({
        searchType: 'post',
        placeholder: '搜索感兴趣的内容'
      });
    }

    // 如果有关键词参数，直接搜索
    if (options.keyword) {
      const keyword = decodeURIComponent(options.keyword);
      this.setData({ keyword });
      this.search(keyword);
    }
  },

  // 获取搜索历史
  getSearchHistory: function() {
    try {
      const history = wx.getStorageSync('searchHistory') || [];
      this.setData({
        searchHistory: history
      });
    } catch (e) {
      console.error('获取搜索历史失败', e);
    }
  },

  // 保存搜索历史
  saveSearchHistory: function(keyword) {
    if (!keyword) return;

    try {
      let history = wx.getStorageSync('searchHistory') || [];

      // 如果已存在相同关键词，先移除
      history = history.filter(item => item !== keyword);

      // 添加到历史记录开头
      history.unshift(keyword);

      // 限制最多保存10条
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      // 保存到本地存储
      wx.setStorageSync('searchHistory', history);

      // 更新数据
      this.setData({
        searchHistory: history
      });
    } catch (e) {
      console.error('保存搜索历史失败', e);
    }
  },

  // 清空搜索历史
  clearHistory: function() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('searchHistory');
            this.setData({
              searchHistory: []
            });
          } catch (e) {
            console.error('清空搜索历史失败', e);
          }
        }
      }
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      keyword: e.detail.value
    });

    // 如果清空了输入，隐藏结果
    if (!e.detail.value) {
      this.setData({
        showResults: false
      });
    }
  },
  // 点击清除按钮
  clearSearch: function() {
    this.setData({
      keyword: '',
      showResults: false
    });
  },

  // 点击搜索历史项
  onHistoryTap: function(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({ keyword });
    this.search(keyword);
  },

  // 搜索确认
  onSearch: function() {
    const keyword = this.data.keyword.trim();
    if (keyword) {
      this.search(keyword);
    }
  },

  // 执行搜索
  search: function(keyword) {
    if (!keyword) return;

    this.setData({
      loading: true,
      showResults: true
    });

    // 保存到搜索历史
    this.saveSearchHistory(keyword);

    // 根据搜索类型执行不同的搜索
    if (this.data.searchType === 'product') {
      // 搜索商品
      this.searchProducts(keyword);
    } else {
      // 搜索帖子（默认）
      this.searchPosts(keyword);
    }
  },

  // 搜索商品
  searchProducts: function(keyword) {
    // 使用API搜索商品
    productApi.getProducts({
      keyword: keyword,
      page: 1,
      pageSize: 20
    })
    .then(res => {
      console.log('商品搜索结果:', res);
      if (res.success) {
        this.setData({
          productResults: res.data || [],
          loading: false
        });
      } else {
        this.setData({
          productResults: [],
          loading: false
        });
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('搜索商品出错:', err);
      this.setData({
        productResults: [],
        loading: false
      });
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    });
  },

  // 搜索帖子
  searchPosts: function(keyword) {
    // 使用API搜索帖子
    postApi.getPosts({
      keyword: keyword,
      page: 1,
      pageSize: 20
    })
    .then(res => {
      console.log('帖子搜索结果:', res);
      if (res.success) {
        // 兼容数据结构
        let posts = Array.isArray(res.data) ? res.data : (res.data && res.data.list ? res.data.list : []);
        // 兼容id字段
        posts.forEach(post => {
          if (!post.id && post._id) {
            post.id = post._id;
          } else if (!post.id) {
            post.id = 'post_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
          }
          // 兼容userInfo.avatar/avatarUrl，nickname/nickName
          if (post.userInfo) {
            if (!post.userInfo.avatarUrl && post.userInfo.avatar) {
              post.userInfo.avatarUrl = post.userInfo.avatar;
            }
            if (!post.userInfo.avatar && post.userInfo.avatarUrl) {
              post.userInfo.avatar = post.userInfo.avatarUrl;
            }
            if (!post.userInfo.nickName && post.userInfo.nickname) {
              post.userInfo.nickName = post.userInfo.nickname;
            }
            if (!post.userInfo.nickname && post.userInfo.nickName) {
              post.userInfo.nickname = post.userInfo.nickName;
            }
          }
          // 兼容images为字符串或嵌套数组的情况
          if (typeof post.images === 'string') {
            try {
              post.images = JSON.parse(post.images);
            } catch (e) {
              post.images = [];
            }
          }
          // 扁平化嵌套数组（如[[...]] => [...], [[[...]]] => [...]）
          while (Array.isArray(post.images) && post.images.length === 1 && Array.isArray(post.images[0])) {
            post.images = post.images[0];
          }
        });
        this.setData({
          searchResults: posts,
          loading: false
        });
      } else {
        this.setData({
          searchResults: [],
          loading: false
        });
        wx.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      }
    })
    .catch(err => {
      console.error('搜索帖子出错:', err);
      this.setData({
        searchResults: [],
        loading: false
      });
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    });
  },

  // 获取测试数据
  getTestPosts: function() {
    return [
      {
        _id: '11',
        userId: 'user11',
        userInfo: {
          nickName: '建筑资质顾问',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '转让一家10年建筑公司，一般纳税人，甲级资质，有意者联系沟通详情。',
        images: ['/images/xinxi/上海.jpg'],
        createTime: '5分钟前',
        likeCount: 45,
        commentCount: 12,
        shareCount: 8,
        isLiked: false,
        product: {
          name: '建筑公司转让服务',
          price: '面议',
          imageUrl: '/images/xinxi/上海.jpg'
        }
      },
      {
        _id: '12',
        userId: 'user12',
        userInfo: {
          nickName: '医药企业并购专家',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '求购：收购一家医药企业，要求有药品生产资质，2016年以前的都可以，公司无异常，无处罚记录。',
        images: ['/images/xinxi/知识产权.jpg'],
        createTime: '8分钟前',
        likeCount: 67,
        commentCount: 23,
        shareCount: 15,
        isLiked: false,
        product: {
          name: '医药企业并购服务',
          price: '咨询报价',
          imageUrl: '/images/xinxi/知识产权.jpg'
        }
      },
      {
        _id: '1',
        userId: 'user1',
        userInfo: {
          nickName: '税务专家',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '2023年企业所得税汇算清缴即将开始，提醒各位企业财务人员注意以下几点：1. 确认收入成本是否准确入账；2. 检查各项税前扣除凭证是否齐全；3. 研发费用加计扣除资料是否完备。提前准备，避免汇算清缴时手忙脚乱。',
        images: ['/images/xinxi/上海.jpg'],
        createTime: '10分钟前',
        likeCount: 128,
        commentCount: 32,
        isLiked: false
      },
      {
        _id: '2',
        userId: 'user2',
        userInfo: {
          nickName: '工商注册顾问',
          avatarUrl: '/images/icons2/女头像.png'
        },
        content: '最新工商注册政策解读：自2023年起，企业注册资本实缴期限可延长至5年，大大减轻了创业者的资金压力。同时，小规模纳税人增值税起征点提高至10万元，进一步减轻小微企业税负。',
        images: ['/images/xinxi/知识产权.jpg'],
        createTime: '30分钟前',
        likeCount: 96,
        commentCount: 18,
        isLiked: true,
        product: {
          name: '公司注册服务套餐',
          price: '1998.00',
          imageUrl: '/images/xinxi/知识产权.jpg'
        }
      },
      {
        _id: '3',
        userId: 'user3',
        userInfo: {
          nickName: '知识产权律师',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '商标注册小贴士：提交申请前务必做好查询工作，避免与已有商标近似导致驳回。建议同时申请多个类别保护，并考虑注册防御商标。商标注册成功后，记得及时监测市场，发现侵权行为立即取证维权。',
        images: ['/images/xinxi/知识产权.jpg', '/images/xinxi/上海.jpg'],
        createTime: '2小时前',
        likeCount: 215,
        commentCount: 45,
        shareCount: 18,
        isLiked: false
      },
      {
        _id: '4',
        userId: 'user4',
        userInfo: {
          nickName: '企业并购专家',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '企业并购中的尽职调查至关重要，主要包括财务尽调、法律尽调、业务尽调和税务尽调四大方面。特别要关注目标公司的或有负债、诉讼风险、知识产权状况和核心客户稳定性。一次成功的并购，往往建立在全面细致的尽职调查基础上。',
        images: [],
        createTime: '3小时前',
        likeCount: 76,
        commentCount: 24,
        shareCount: 8,
        isLiked: false
      },
      {
        _id: '6',
        userId: 'user6',
        userInfo: {
          nickName: '企业转让顾问',
          avatarUrl: '/images/icons2/男头像.png'
        },
        content: '企业转让流程详解：1. 前期准备与资产梳理；2. 企业估值；3. 寻找买家并洽谈；4. 签署意向书；5. 尽职调查；6. 签署正式转让协议；7. 工商变更；8. 交接过渡。整个过程通常需要3-6个月，建议聘请专业顾问全程指导。',
        images: ['/images/xinxi/知识产权.jpg', '/images/xinxi/上海.jpg'],
        createTime: '6小时前',
        likeCount: 98,
        commentCount: 36,
        shareCount: 14,
        isLiked: false,
        product: {
          name: '企业转让顾问服务',
          price: '15999.00',
          imageUrl: '/images/xinxi/知识产权.jpg'
        }
      }
    ];
  },

  // 点击帖子
  onPostTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}`
    });
  },

  // 点击商品
  onProductTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },

  // 点击用户头像
  onUserTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user/user?id=${id}`
    });
  },

  // 点赞
  onLikeTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const post = this.data.searchResults[index];

    // 更新点赞状态和数量
    const searchResults = this.data.searchResults;
    searchResults[index].isLiked = !post.isLiked;
    searchResults[index].likeCount = post.isLiked ? post.likeCount - 1 : post.likeCount + 1;

    this.setData({
      searchResults: searchResults
    });
  },

  // 评论
  onCommentTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}&showComment=true`
    });
  },

  // 分享
  onShareTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const post = this.data.searchResults[index];

    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈'],
      success: (res) => {
        if (res.tapIndex === 0 || res.tapIndex === 1) {
          // 更新转发数
          const searchResults = this.data.searchResults;
          searchResults[index].shareCount = (searchResults[index].shareCount || 0) + 1;

          this.setData({
            searchResults: searchResults
          });

          if (res.tapIndex === 0) {
            // 分享给朋友，由onShareAppMessage处理
            wx.showShareMenu({
              withShareTicket: true
            });
          } else if (res.tapIndex === 1) {
            // 分享到朋友圈
            wx.showToast({
              title: '已分享到朋友圈',
              icon: 'success'
            });
          }
        }
      }
    });
  },

  // 分享给朋友
  onShareAppMessage: function(res) {
    if (res.from === 'button') {
      const index = res.target.dataset.index;
      const post = this.data.searchResults[index];

      return {
        title: post.content.substring(0, 30),
        path: `/pages/post/detail?id=${post._id}`,
        imageUrl: post.images && post.images.length > 0 ? post.images[0] : ''
      };
    }

    return {
      title: '搜索更多精彩内容',
      path: '/pages/search/search'
    };
  }
})

<!--pages/cart/cart.wxml-->
<view class="cart-container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="title">购物车</view>
    <view class="edit-btn" bindtap="toggleEditMode" wx:if="{{isLogin && cartItems.length > 0}}">
      {{isEdit ? '完成' : '编辑'}}
    </view>
  </view>

  <!-- 登录提示 -->
  <view class="login-prompt" wx:if="{{!isLogin}}">
    <image class="login-icon" src="/images/icons2/购物车.png"></image>
    <view class="login-text">登录后查看购物车</view>
    <view class="login-btn" bindtap="goToLogin">立即登录</view>
  </view>

  <!-- 购物车列表 -->
  <view class="cart-list" wx:elif="{{!loading && cartItems.length > 0}}">
    <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
      <view class="select-box {{item.selected ? 'selected' : ''}}" bindtap="toggleSelectItem" data-index="{{index}}">
        <image src="{{item.selected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
      </view>

      <image class="product-image" src="{{item.image}}" mode="aspectFill" bindtap="onItemTap" data-id="{{item.productId}}"></image>

      <view class="product-info" bindtap="onItemTap" data-id="{{item.productId}}">
        <view class="product-name">{{item.name}}</view>
        <view class="product-spec" wx:if="{{item.spec}}">{{item.spec}}</view>
        <view class="product-price">¥{{item.price}}</view>
      </view>

      <view class="quantity-control">
        <view class="quantity-btn" bindtap="decreaseQuantity" data-index="{{index}}">-</view>
        <input class="quantity-input" type="number" value="{{item.quantity}}" bindblur="inputQuantity" data-index="{{index}}" />
        <view class="quantity-btn" bindtap="increaseQuantity" data-index="{{index}}">+</view>
      </view>


    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:elif="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空购物车 -->
  <view class="empty-container" wx:elif="{{isLogin && !loading && cartItems.length === 0}}">
    <image class="empty-icon" src="/images/icons2/购物车.png"></image>
    <view class="empty-text">购物车还是空的</view>
    <view class="go-shop-btn" bindtap="goToShop">去逛逛</view>
  </view>

  <!-- 底部结算栏 -->
  <view class="checkout-bar" wx:if="{{isLogin && cartItems.length > 0}}">
    <view class="select-all" bindtap="toggleSelectAll">
      <view class="select-box {{allSelected ? 'selected' : ''}}">
        <image src="{{allSelected ? '/images/icons2/勾选.png' : '/images/icons/checkbox.png'}}"></image>
      </view>
      <text>全选</text>
    </view>

    <view class="total-info" wx:if="{{!isEdit}}">
      <view class="total-price">
        合计: <text class="price">¥{{totalPrice}}</text>
      </view>
      <view class="total-desc">不含运费</view>
    </view>

    <view class="checkout-btn {{selectedCount > 0 ? 'active' : ''}}" bindtap="{{isEdit ? '' : 'checkout'}}" wx:if="{{!isEdit}}">
      结算{{selectedCount > 0 ? '(' + selectedCount + ')' : ''}}
    </view>
  </view>

  <!-- 全局删除按钮 -->
  <view class="global-delete-btn {{selectedCount > 0 ? 'active' : 'disabled'}}" wx:if="{{isEdit && cartItems.length > 0}}" bindtap="{{selectedCount > 0 ? 'deleteSelected' : 'showSelectTip'}}">
    删除
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

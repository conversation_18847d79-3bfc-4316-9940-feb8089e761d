/**
 * 购物车控制器
 */
const cartService = require('../services/cartService');

exports.getCart = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await cartService.getCart(userId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.addToCart = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { productId, quantity } = req.body;
    
    // 参数验证
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: '商品ID不能为空'
      });
    }

    if (quantity && (isNaN(quantity) || quantity < 1)) {
      return res.status(400).json({
        success: false,
        message: '商品数量必须大于0'
      });
    }
    
    const result = await cartService.addToCart(userId, productId, quantity);
    
    if (!result.success) {
      return res.status(400).json(result);
    }
    
    res.status(201).json(result);
  } catch (error) {
    console.error('添加到购物车失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后再试'
    });
  }
};

exports.updateCart = async (req, res, next) => {
  try {
    const { quantity } = req.body;
    
    const result = await cartService.updateCart(req.params.id, quantity);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.removeFromCart = async (req, res, next) => {
  try {
    const result = await cartService.removeFromCart(req.params.id);
    
    if (!result.success) {
      return res.status(404).json(result);
    }
    
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.clearCart = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    
    const result = await cartService.clearCart(userId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getCartCount = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    
    const result = await cartService.getCartCount(userId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

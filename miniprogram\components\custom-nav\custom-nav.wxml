<!--components/custom-nav/custom-nav.wxml-->
<view class="custom-nav" style="background-color: {{bgColor}};">
  <!-- 状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 导航栏 -->
  <view class="nav-bar" style="height: {{menuButtonInfo.height + 12}}px;">
    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="onBack" wx:if="{{showBack}}">
      <image src="/images/icons2/返回.png"></image>
    </view>

    <!-- 标题 -->
    <view class="title {{showBack ? 'has-back' : ''}}">{{title}}</view>

    <!-- 设置按钮 -->
    <view class="settings-btn" bindtap="onSettings" wx:if="{{showSettings}}">
      <image src="/images/icons2/设置.png"></image>
    </view>
  </view>
</view>

<!-- 占位元素，保持与导航栏相同高度 -->
<view class="nav-placeholder" style="height: {{statusBarHeight + menuButtonInfo.height + 12}}px;"></view>

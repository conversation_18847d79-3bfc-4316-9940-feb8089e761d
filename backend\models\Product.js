/**
 * 商品模型
 */
const db = require('../config/db');

class Product {
  static async findAll(options = {}) {
    const { categoryId, subCategoryId, keyword, page = 1, pageSize = 10, sortType = 'default' } = options;

    let sql = 'SELECT * FROM products WHERE 1=1';
    const params = [];

    if (typeof categoryId !== 'undefined' && categoryId !== null && String(categoryId).trim() !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (typeof subCategoryId !== 'undefined' && subCategoryId !== null && String(subCategoryId).trim() !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 排序，兼容所有前端写法
    if (sortType === 'price_asc' || sortType === 'price-asc') {
      sql += ' ORDER BY price ASC';
    } else if (sortType === 'price_desc' || sortType === 'price-desc') {
      sql += ' ORDER BY price DESC';
    } else if (sortType === 'sales') {
      sql += ' ORDER BY salesCount DESC';
    } else {
      sql += ' ORDER BY id DESC';
    }

    // 分页
    const safePage = parseInt(page, 10) > 0 ? parseInt(page, 10) : 1;
    const safePageSize = parseInt(pageSize, 10) > 0 ? parseInt(pageSize, 10) : 10;
    const offset = (safePage - 1) * safePageSize;
    sql += ` LIMIT ${Number(safePageSize)} OFFSET ${Number(offset)}`;
    console.log('Product SQL params:', params);

    return await db.query(sql, params);
  }

  static async findById(id) {
    const result = await db.query('SELECT * FROM products WHERE id = ?', [id]);
    return result.length > 0 ? result[0] : null;
  }

  static async getCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopCategories() {
    return await db.query('SELECT * FROM categories ORDER BY id');
  }

  static async getShopSubCategories(parentId) {
    try {
      const sql = 'SELECT * FROM subCategories WHERE parentId = ?';
      const params = [parentId];
      return await db.query(sql, params);
    } catch (error) {
      console.error('执行SQL查询失败:', error);
      throw error;
    }
  }

  static async getBanners() {
    return await db.query('SELECT * FROM banners ORDER BY sort');
  }

  static async count(options = {}) {
    const { categoryId, subCategoryId, keyword } = options;

    let sql = 'SELECT COUNT(*) as total FROM products WHERE 1=1';
    const params = [];

    if (categoryId !== undefined && categoryId !== null && categoryId !== '') {
      sql += ' AND categoryId = ?';
      params.push(Number(categoryId));
    }

    if (subCategoryId !== undefined && subCategoryId !== null && subCategoryId !== '') {
      sql += ' AND subCategoryId = ?';
      params.push(Number(subCategoryId));
    }

    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    const result = await db.query(sql, params);
    return result[0].total;
  }
}

module.exports = Product;

/**
 * 购物车服务
 */
const Cart = require('../models/Cart');
const Product = require('../models/Product');

class CartService {
  async getCart(userId) {
    try {
      const cartItems = await Cart.findAll(userId);
      
      // 处理购物车数据
      const processedItems = cartItems.map(item => {
        // 如果images是JSON字符串，转换为数组
        if (item.images && typeof item.images === 'string') {
          try {
            item.images = JSON.parse(item.images);
            // 只取第一张图片
            item.image = item.images.length > 0 ? item.images[0] : '';
          } catch (e) {
            item.images = [];
            item.image = '';
          }
        }
        
        return {
          ...item,
          checked: true // 默认选中
        };
      });
      
      return {
        success: true,
        data: processedItems,
        message: '获取购物车成功'
      };
    } catch (error) {
      console.error('获取购物车失败:', error);
      throw error;
    }
  }
  
  async addToCart(userId, productId, quantity = 1) {
    try {
      // 检查商品是否存在
      const product = await Product.findById(productId);
      if (!product) {
        return {
          success: false,
          message: '商品不存在'
        };
      }
      
      // 检查购物车中是否已存在该商品
      const existingItem = await Cart.findByUserAndProduct(userId, productId);
      
      if (existingItem) {
        // 更新数量
        const updatedItem = await Cart.update(existingItem.id, {
          quantity: existingItem.quantity + quantity
        });
        return {
          success: true,
          data: updatedItem,
          message: '更新购物车成功'
        };
      } else {
        // 添加新商品
        const newItem = await Cart.create({
          id: 'cart_' + userId + '_' + productId,
          userId,
          productId,
          name: product.name || '',
          price: product.price != null ? product.price : 0,
          image: (product.images && Array.isArray(product.images) && product.images[0]) ? product.images[0] : (product.image || ''),
          quantity: quantity || 1,
          selected: true
        });
        return {
          success: true,
          data: newItem,
          message: '添加到购物车成功'
        };
      }
    } catch (error) {
      console.error('添加到购物车失败:', error);
      throw new Error('添加到购物车失败，请稍后再试');
    }
  }
  
  async updateCart(id, quantity) {
    try {
      const cartItem = await Cart.findById(id);
      if (!cartItem) {
        return {
          success: false,
          message: '购物车商品不存在'
        };
      }
      
      // 更新数量
      const updatedItem = await Cart.update(id, { quantity });
      
      return {
        success: true,
        data: updatedItem,
        message: '更新购物车成功'
      };
    } catch (error) {
      console.error('更新购物车失败:', error);
      throw error;
    }
  }
  
  async removeFromCart(id) {
    try {
      const cartItem = await Cart.findById(id);
      if (!cartItem) {
        return {
          success: false,
          message: '购物车商品不存在'
        };
      }
      
      // 删除商品
      await Cart.remove(id);
      
      return {
        success: true,
        data: { id },
        message: '删除购物车商品成功'
      };
    } catch (error) {
      console.error('删除购物车商品失败:', error);
      throw error;
    }
  }
  
  async clearCart(userId) {
    try {
      // 清空购物车
      await Cart.removeByUser(userId);
      
      return {
        success: true,
        message: '清空购物车成功'
      };
    } catch (error) {
      console.error('清空购物车失败:', error);
      throw error;
    }
  }
  
  async getCartCount(userId) {
    try {
      const count = await Cart.count(userId);
      
      return {
        success: true,
        data: { count },
        message: '获取购物车数量成功'
      };
    } catch (error) {
      console.error('获取购物车数量失败:', error);
      throw error;
    }
  }
}

module.exports = new CartService();

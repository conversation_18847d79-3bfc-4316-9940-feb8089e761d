<!--pages/auth/reset-password.wxml-->
<view class="reset-container">
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <image class="back-icon" src="../../images/icons2/返回.png"></image>
    </view>
    <view class="title">{{step === 1 ? '忘记密码' : '设置新密码'}}</view>
  </view>

  <!-- 步骤1：验证手机号 -->
  <block wx:if="{{step === 1}}">
    <view class="input-area">
      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/手机.png"></image>
        <input class="input" type="number" placeholder="请输入手机号" maxlength="11" bindinput="inputPhoneNumber" value="{{phone}}" />
      </view>

      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/验证码.png"></image>
        <input class="input" type="number" placeholder="请输入验证码" maxlength="6" bindinput="inputVerifyCode" value="{{verifyCode}}" />
        <view class="verify-btn {{canGetCode && countdown <= 0 ? 'active' : ''}}" bindtap="getVerifyCode">
          {{countdown > 0 ? countdown + 's' : '获取验证码'}}
        </view>
      </view>
    </view>

    <view class="tips">
      <text>请输入您注册时使用的手机号，我们将向该手机号发送验证码</text>
    </view>

    <view class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="verifyPhoneAndCode">下一步</view>
  </block>

  <!-- 步骤2：设置新密码 -->
  <block wx:if="{{step === 2}}">
    <view class="input-area">
      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/密码.png"></image>
        <input class="input" type="password" placeholder="请输入新密码" password="true" bindinput="inputNewPassword" />
      </view>

      <view class="input-group">
        <image class="input-icon" src="../../images/icons2/密码.png"></image>
        <input class="input" type="password" placeholder="请确认新密码" password="true" bindinput="inputConfirmPassword" />
      </view>
    </view>

    <view class="tips">
      <text>密码长度至少为6位，建议使用字母、数字和符号的组合</text>
    </view>

    <view class="submit-btn {{canSubmit ? 'active' : ''}}" bindtap="resetPassword">重置密码</view>
  </block>
</view>

/* pages/settings/settings.wxss */
.settings-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 40rpx;
}

.settings-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.settings-content {
  padding: 20rpx;
}

/* 用户ID信息区域 */
.user-id-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.user-id-content {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.user-nickname {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.user-id-text {
  font-size: 28rpx;
  color: #999999;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff4d4f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.settings-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.section-title {
  font-size: 28rpx;
  color: #999999;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 30rpx;
  color: #333333;
}

.item-value {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #999999;
}

.arrow-right {
  margin-left: 10rpx;
  color: #cccccc;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.logout-button {
  background-color: #ffffff;
  color: #ff4d4f;
  text-align: center;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}

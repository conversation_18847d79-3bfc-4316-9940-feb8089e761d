// pages/settings/wechat.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    isWechatBound: false,
    loading: false
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    // 先从全局获取
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        isWechatBound: !!app.globalData.userInfo.openid
      });
      return;
    }

    // 如果全局没有，从服务器获取
    const userInfo = wx.getStorageSync('userInfo');
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        if (res.success) {
          // 更新全局用户信息
          app.globalData.userInfo = res.data;
          app.globalData.isLogin = true;

          this.setData({
            isWechatBound: !!res.data.openid
          });
        } else {
          // 如果API调用失败，尝试从本地存储获取
          this.getLocalUserInfo();
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        // 如果API调用失败，尝试从本地存储获取
        this.getLocalUserInfo();
      });
  },

  // 从本地存储获取用户信息
  getLocalUserInfo: function() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        const app = getApp();
        app.globalData.userInfo = res.data;
        app.globalData.isLogin = true;

        this.setData({
          isWechatBound: !!res.data.openid
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 绑定微信
  bindWechat: function() {
    this.setData({ loading: true });

    // 获取微信用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo;

        // 获取登录凭证
        wx.login({
          success: (loginRes) => {
            if (loginRes.code) {
              // 调用后端接口绑定微信
              userApi.bindWechat(loginRes.code, userInfo)
                .then(res => {
                  this.setData({ loading: false });

                  if (res.success) {
                    // 更新全局用户信息
                    const app = getApp();
                    app.globalData.userInfo.openid = res.data.openid;

                    // 更新本地存储
                    wx.getStorage({
                      key: 'userInfo',
                      success: (result) => {
                        const userInfo = result.data;
                        userInfo.openid = res.data.openid;
                        wx.setStorage({
                          key: 'userInfo',
                          data: userInfo
                        });
                      }
                    });

                    this.setData({
                      isWechatBound: true
                    });

                    wx.showToast({
                      title: '微信绑定成功',
                      icon: 'success'
                    });

                    // 返回上一页
                    setTimeout(() => {
                      wx.navigateBack();
                    }, 1500);
                  } else {
                    wx.showToast({
                      title: res.message || '绑定失败',
                      icon: 'none'
                    });
                  }
                })
                .catch(err => {
                  this.setData({ loading: false });
                  wx.showToast({
                    title: '网络错误，请稍后再试',
                    icon: 'none'
                  });
                });
            } else {
              this.setData({ loading: false });
              wx.showToast({
                title: '获取微信授权失败',
                icon: 'none'
              });
            }
          },
          fail: () => {
            this.setData({ loading: false });
            wx.showToast({
              title: '获取微信授权失败',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        this.setData({ loading: false });
        wx.showToast({
          title: '您已取消授权',
          icon: 'none'
        });
      }
    });
  },

  // 解绑微信
  unbindWechat: function() {
    wx.showModal({
      title: '提示',
      content: '确定要解除微信绑定吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ loading: true });

          userApi.unbindWechat()
            .then(res => {
              this.setData({ loading: false });

              if (res.success) {
                // 更新全局用户信息
                const app = getApp();
                app.globalData.userInfo.openid = null;

                // 更新本地存储
                wx.getStorage({
                  key: 'userInfo',
                  success: (result) => {
                    const userInfo = result.data;
                    userInfo.openid = null;
                    wx.setStorage({
                      key: 'userInfo',
                      data: userInfo
                    });
                  }
                });

                this.setData({
                  isWechatBound: false
                });

                wx.showToast({
                  title: '解绑成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.message || '解绑失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              this.setData({ loading: false });
              wx.showToast({
                title: '网络错误，请稍后再试',
                icon: 'none'
              });
            });
        }
      }
    });
  }
})

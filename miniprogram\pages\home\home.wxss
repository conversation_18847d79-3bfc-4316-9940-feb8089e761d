/* pages/home/<USER>/
.home-container {
  padding-bottom: 20px;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 0 0 15px; /* 左侧添加内边距 */
  justify-content: flex-start; /* 左对齐 */
}

/* 导航栏占位 */
.nav-placeholder {
  width: 100%;
}

.search-input-container {
  height: 32px;
  background-color: #F5F5F5;
  border-radius: 16px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  padding: 0 36px 0 12px; /* 右侧留出空间给搜索按钮 */
}

.search-placeholder {
  font-size: 14px;
  color: #999999;
}

.search-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 4px;
}

.search-btn image {
  width: 20px;
  height: 20px;
}

.message-icon {
  position: relative;
  width: 24px;
  height: 24px;
  margin-left: 12px;
}

.message-icon image {
  width: 100%;
  height: 100%;
}

.badge-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #FF4D4F;
}

/* 轮播广告 */
.banner {
  height: 150px;
  margin: 0 16px 12px;
  border-radius: 12px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 14px;
  font-weight: bold;
}

/* 功能导航 */
.nav-grid {
  background-color: #FFFFFF;
  padding: 16px;
  margin-bottom: 12px;
}

.nav-row {
  display: flex;
  justify-content: space-between;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
}

.nav-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 6px;
}

.nav-text {
  font-size: 12px;
  color: #333333;
}

/* 内容分类 */
.tab-container {
  background-color: #FFFFFF;
  margin-bottom: 12px;
  position: sticky;
  /* top 值通过内联样式动态设置 */
  z-index: 99;
}

.tab-bar {
  display: flex;
  height: 40px;
  padding: 0 16px;
  white-space: nowrap;
  overflow-x: auto;
  justify-content: space-between;
  align-items: center;
}

.tab-bar::-webkit-scrollbar {
  display: none;
}

.tab-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  font-size: 15px;
  color: #666666;
}

.tab-item.active {
  color: #333333;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 3px;
  background-color: #FF4D4F;
  border-radius: 1.5px;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 32px;
  background-color: #F8F8F8;
  border-radius: 16px;
  margin-left: 10px;
}

.filter-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.filter-btn text {
  font-size: 15px; /* 与左侧卡片文字大小一致 */
  color: #666666;
}

.expand-icon {
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #666666;
  margin-left: 6px;
}

/* 信息流列表 */
.post-list {
  padding: 0 16px;
}

.post-item {
  margin-bottom: 12px;
  padding: 16px;
}

.post-user {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 2px;
}

.post-time {
  font-size: 12px;
  color: #999999;
}

.follow-btn {
  padding: 4px 12px;
  font-size: 12px;
  color: #FF4D4F;
  border: 1px solid #FF4D4F;
  border-radius: 15px;
}

.post-content {
  margin-bottom: 12px;
}

.content-text {
  font-size: 15px;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.post-image {
  border-radius: 4px;
  background: #f8f8f8;
}

/* 多图九宫格 */
.post-image {
  width: calc((100% - 8px) / 3);
  height: calc((100% - 8px) / 3);
  object-fit: cover;
}

/* 单图大图 */
.single-image-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  background: #eee;
  border-radius: 8px;
}

.single-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  display: block;
  border-radius: 8px;
  background: #eee;
  border: none;
}

.image-grid {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 4px;
  margin-bottom: 8px;
}

.grid-image {
  flex: 1 1 0;
  width: auto;
  max-width: unset;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  background: #eee;
  display: block;
  margin-right: 0;
}

.grid-image:last-child {
  margin-right: 0;
}

.product-link {
  display: flex;
  align-items: center;
  background-color: #F8F8F8;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 8px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.product-price {
  font-size: 14px;
  color: #FF4D4F;
  font-weight: bold;
}

.buy-btn {
  padding: 4px 12px;
  font-size: 12px;
  color: #FFFFFF;
  background-color: #FF4D4F;
  border-radius: 15px;
}

/* 发布区域和主题类别 */
.post-meta {
  display: flex;
  margin-top: 8px;
  margin-bottom: 2px;
}

.post-region, .post-topic {
  font-size: 12px;
  color: #666666;
  background-color: #F5F5F5;
  padding: 3px 10px;
  border-radius: 10px;
  margin-right: 8px;
  display: inline-block;
  border: 1px solid #EEEEEE;
}

.interaction-bar {
  display: flex;
  height: 38px;
  border-top: 1px solid #F5F5F5;
  padding-top: 6px;
  padding-bottom: 2px;
  margin-top: 2px;
}

.interaction-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interaction-item image {
  width: 21px;
  height: 21px;
  margin-right: 4px;
}

.interaction-item text {
  font-size: 14px;
  color: #666666;
  line-height: 1;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16px;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #ff5252;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.go-explore-btn {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background-color: #ff5252;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 82, 82, 0.2);
  transition: all 0.3s ease;
}

.go-explore-btn:active {
  transform: scale(0.95);
  background-color: #e64a4a;
}

/* 地区回退提示样式 */
.region-fallback-tip {
  background-color: #fff9e6;
  color: #ff9900;
  padding: 20rpx 24rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  margin: 20rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 153, 0, 0.15);
  line-height: 1.5;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.region-fallback-tip::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 8rpx;
  background-color: #ff9900;
}

.region-fallback-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.region-fallback-content {
  flex: 1;
}

.region-fallback-text {
  font-size: 26rpx;
  color: #ff9900;
  line-height: 1.6;
  display: block;
}

.post-video {
  width: 100%;
  max-height: 300px;
  border-radius: 8px;
  background: #000;
  display: block;
}

.back-to-top-btn {
  position: fixed;
  right: 32rpx;
  bottom: 50px;
  width: 85rpx;
  height: 85rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.back-to-top-icon {
  width: 66rpx;
  height: 66rpx;
  display: block;
}

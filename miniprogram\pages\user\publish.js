// pages/user/publish.js
const { postApi } = require('../../utils/api');

Page({
  data: {
    posts: [],
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    isLogin: false,
    userInfo: null
  },

  onLoad: function (options) {
    // 检查登录状态
    this._hasRedirect = false;
    const app = getApp();
    const isLogin = app.globalData.isLogin;
    const userInfo = app.globalData.userInfo;

    // 初始化我的发布页面

    this.setData({
      isLogin,
      userInfo
    });

    if (isLogin) {
      this.getPosts();
    } else {
      this.setData({
        loading: false
      });
    }
  },

  onShow: function () {
    // 每次显示页面时检查登录状态
    if (this._hasRedirect) return;
    const app = getApp();
    const isLogin = app.globalData.isLogin;

    if (!isLogin) {
      // 未登录时不再做任何跳转，避免重复跳转
      return;
    }

    if (isLogin && !this.data.isLogin) {
      // 登录状态发生变化，重新加载数据
      this.setData({
        isLogin: true,
        userInfo: app.globalData.userInfo,
        page: 1,
        posts: []
      });
      this.getPosts();
    }
  },

  // 时间格式化工具
  formatDate: function (ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    return `${y}-${m}-${d} ${h}:${min}`;
  },

  // 获取用户发布的帖子
  getPosts: function (append = false) {
    const { page, pageSize, userInfo } = this.data;

    if (!userInfo) {
      return;
    }

    // 显示加载中
    if (!append) {
      this.setData({
        loading: true
      });
    }

    // 构建请求参数
    const userId = userInfo && userInfo.id ? String(userInfo.id) : '';
    const params = {
      page: page,
      pageSize: pageSize,
      userId: userId // 只传唯一id
    };

    // 准备请求参数

    // 使用API获取帖子数据
    postApi.getPosts(params)
      .then(res => {
        // 获取我的帖子数据成功

        if (res.success) {
          // 检查返回的数据结构，兼容两种格式
          let posts = Array.isArray(res.data) ? res.data : (res.data && res.data.list ? res.data.list : []);
          const hasMore = posts.length === pageSize;

          // 再次用userId过滤，确保只显示自己的帖子
          posts = posts.filter(post => String(post.userId) === userId);

          // 给每条post补充当前用户的头像和昵称（兼容历史字段）
          posts.forEach(post => {
            post.userInfo = {
              avatar: userInfo.avatar || userInfo.avatarUrl || '/images/icons2/我的.png',
              nickname: userInfo.nickname || userInfo.nickName || '用户',
              avatarUrl: userInfo.avatarUrl || userInfo.avatar || '/images/icons2/我的.png', // 兼容旧字段
              nickName: userInfo.nickName || userInfo.nickname || '用户' // 兼容旧字段
            };
          });

          // 收集所有fileID（图片和视频）
          let allFileIDs = [];
          posts.forEach(post => {
            if (typeof post.images === 'string') {
              try { post.images = JSON.parse(post.images); } catch (e) { post.images = []; }
            }
            if (!Array.isArray(post.images)) post.images = [];
            allFileIDs = allFileIDs.concat(post.images.filter(fid => typeof fid === 'string' && fid.startsWith('cloud:')));
            if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud:')) {
              allFileIDs.push(post.video);
            }
            post.images = post.images.filter(img => typeof img === 'string' && img.trim() !== '');
            post.createTime = this.formatDate(post.createTime);
            
            // 处理地区显示
            if (post.region) {
              post.regionText = this.formatRegion(post.region);
            } else {
              post.regionText = '全国';
            }
            
            // 处理商品数据
            if (post.product && typeof post.product === 'string') {
              try { 
                post.product = JSON.parse(post.product); 
              } catch { 
                post.product = null; 
              }
            }
            if (post.product && typeof post.product === 'object') {
              // 保存原始商品图片URL
              let imgUrl = post.product.imageUrl || '';
              if (!imgUrl && post.product.img) {
                imgUrl = post.product.img;
                post.product.imageUrl = imgUrl; // 确保设置imageUrl字段
              }
              // 检查是否有images数组
              if (!imgUrl && post.product.images && Array.isArray(post.product.images) && post.product.images.length > 0) {
                imgUrl = post.product.images[0];
                post.product.imageUrl = imgUrl; // 使用第一张图片作为商品图片
              }
              // 如果是云开发文件ID，添加到转换列表
              if (typeof imgUrl === 'string' && imgUrl.startsWith('cloud://')) {
                allFileIDs.push(imgUrl);
              }
              // 如果是HTTP URL，直接保留
              if (typeof imgUrl === 'string' && (imgUrl.startsWith('http://') || imgUrl.startsWith('https://'))) {
                post.product.imageUrl = imgUrl;
              }
            }
            
            // 处理多主题数组 - 增强健壮性与安全性
            try {
              // 首先确保topic字段存在且有效
              if (!post.topic || post.topic === '') {
                post.topic = '企业服务';
              }
              
              // 处理topics字段 - 更严格的检查
              if (post.topics) {
                if (typeof post.topics === 'string') {
                  // 提前检查是否是有效的JSON格式
                  const trimmedTopics = post.topics.trim();
                  if (trimmedTopics === '' || trimmedTopics === '[]' || trimmedTopics === '{}' || 
                      trimmedTopics === 'null' || trimmedTopics === 'undefined') {
                    // 明确无效的值，直接使用默认主题
                    post.topicsArray = [post.topic];
                    // 使用默认主题
                  } else {
                    // 检查是否是有效的JSON格式（以 [ 开头或 " 开头）
                    const firstChar = trimmedTopics.charAt(0);
                    if (firstChar === '[' || firstChar === '"') {
                      try {
                        // 尝试解析JSON字符串
                        const parsed = JSON.parse(trimmedTopics);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                          // 有效数组
                          post.topicsArray = parsed.map(t => String(t)).filter(t => t.trim() !== '');
                          if (post.topicsArray.length === 0) {
                            post.topicsArray = [post.topic];
                          }
                        } else if (typeof parsed === 'string' && parsed.trim() !== '') {
                          // 单个字符串
                          post.topicsArray = [parsed];
                        } else if (parsed === null || parsed === undefined) {
                          // null或undefined
                          post.topicsArray = [post.topic];
                        } else {
                          // 其他情况回退到单个主题
                          post.topicsArray = [post.topic];
                        }
                      } catch (e) {
                        // 解析topics字段失败
                        // 解析失败，直接使用单个主题
                        post.topicsArray = [post.topic];
                      }
                    } else {
                      // 不是有效的JSON格式，直接作为单个主题使用
                      post.topicsArray = [trimmedTopics];
                      // 非JSON格式topics处理
                    }
                  }
                } else if (Array.isArray(post.topics)) {
                  // 已经是数组，过滤空值和非字符串值
                  post.topicsArray = post.topics
                    .map(t => t !== null && t !== undefined ? String(t) : null)
                    .filter(t => t && t.trim() !== '');
                  if (post.topicsArray.length === 0) {
                    post.topicsArray = [post.topic];
                  }
                } else if (post.topics === null || post.topics === undefined) {
                  // null或undefined
                  post.topicsArray = [post.topic];
                } else {
                  // 其他类型，尝试转换为字符串
                  try {
                    const topicStr = String(post.topics).trim();
                    post.topicsArray = topicStr ? [topicStr] : [post.topic];
                  } catch {
                    post.topicsArray = [post.topic];
                  }
                }
              } else {
                // 没有topics字段，使用单个主题
                post.topicsArray = [post.topic];
              }
              
              // 最后确保至少有一个有效的主题
              if (!post.topicsArray || !Array.isArray(post.topicsArray) || post.topicsArray.length === 0) {
                post.topicsArray = ['企业服务'];
              }
            } catch (err) {
              // 捕获所有异常，确保不会影响整体渲染
              // 处理主题时发生异常
              post.topicsArray = [post.topic || '企业服务'];
            }
          });
          if (allFileIDs.length > 0) {
            wx.cloud.getTempFileURL({
              fileList: allFileIDs,
              config: { env: 'prod-5geioww562624006' },
              success: res2 => {
                const urlMap = {};
                res2.fileList.forEach(item => {
                  urlMap[item.fileID] = item.tempFileURL;
                });
                posts.forEach(post => {
                  if (Array.isArray(post.images)) {
                    post.images = post.images.map(fid => (urlMap[fid] && urlMap[fid].startsWith('https')) ? urlMap[fid] : '').filter(img => typeof img === 'string' && img.startsWith('https'));
                  }
                  if (post.video && typeof post.video === 'string' && post.video.startsWith('cloud:')) {
                    post.video = (urlMap[post.video] && urlMap[post.video].startsWith('https')) ? urlMap[post.video] : '';
                  }
                  // 只保留https外链
                  if (Array.isArray(post.images)) {
                    post.images = post.images.filter(img => typeof img === 'string' && img.startsWith('https'));
                  }
                  if (post.video && typeof post.video === 'string' && !post.video.startsWith('https')) {
                    post.video = '';
                  }
                });
                // 增强调试输出
                // 最终渲染我的发布posts图片/视频URL
                this.setData({
                  posts: append ? this.data.posts.concat(posts) : posts,
                  loading: false,
                  refreshing: false,
                  hasMore: hasMore
                });
              },
              fail: err => {
                posts.forEach(post => {
                  if (Array.isArray(post.images)) post.images = [];
                  if (post.video) post.video = '';
                });
                this.setData({
                  posts: append ? this.data.posts.concat(posts) : posts,
                  loading: false,
                  refreshing: false,
                  hasMore: hasMore
                });
              }
            });
          } else {
            this.setData({
              posts: append ? this.data.posts.concat(posts) : posts,
              loading: false,
              refreshing: false,
              hasMore: hasMore
            });
          }
          return;
        } else {
          // 获取我的帖子数据失败

          // 显示错误提示
          wx.showToast({
            title: res.message || '获取帖子数据失败',
            icon: 'none'
          });

          this.setData({
            loading: false,
            refreshing: false
          });
        }
      })
      .catch(err => {
        // 获取我的帖子数据出错

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });

        this.setData({
          loading: false,
          refreshing: false
        });
      });
  },

  // 点击帖子
  onPostTap: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post/detail?id=${id}`
    });
  },

  // 删除帖子
  onDeleteTap: function (e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条帖子吗？删除后无法恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          this.performDelete(id, index);
        }
      }
    });
  },

  // 执行删除操作
  performDelete: function (id, index) {
    // 显示加载中
    wx.showLoading({
      title: '正在删除',
      mask: true
    });

    // 使用API删除帖子
    postApi.deletePost(id)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 从列表中移除该帖子
          const posts = this.data.posts;
          posts.splice(index, 1);

          this.setData({
            posts: posts
          });

          // 显示成功提示
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } else {
          // 删除帖子失败

          // 显示错误提示
          wx.showToast({
            title: res.message || '删除失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        // 删除帖子出错
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 隐藏/显示帖子
  onToggleVisibilityTap: function (e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const isHidden = e.currentTarget.dataset.hidden === 'true';
    const newStatus = !isHidden;

    // 显示加载中
    wx.showLoading({
      title: newStatus ? '正在隐藏' : '正在显示',
      mask: true
    });

    // 使用API修改帖子可见性
    postApi.togglePostVisibility(id, newStatus)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 更新帖子状态
          const posts = this.data.posts;
          posts[index].isHidden = newStatus;

          this.setData({
            posts: posts
          });

          // 显示成功提示
          wx.showToast({
            title: newStatus ? '已隐藏' : '已显示',
            icon: 'success'
          });
        } else {
          // 修改帖子可见性失败

          // 显示错误提示
          wx.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        // 修改帖子可见性出错
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      refreshing: true,
      page: 1
    });

    this.getPosts();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1,
      loading: true
    });

    this.getPosts(true);
  },

  // 发布新帖子
  onPublishTap: function () {
    wx.navigateTo({
      url: '/pages/publish/publish'
    });
  },
  
  // 点击悬浮按钮跳转到发布页面
  goToPublish: function() {
    console.log('点击发布按钮');
    try {
      // 直接使用switchTab跳转到tabBar中的发布页面
      wx.switchTab({
        url: '/pages/publish/publish',
        success: function() {
          console.log('跳转到发布页面成功');
        },
        fail: function(err) {
          console.error('跳转失败：', err);
          wx.showToast({
            title: '跳转失败，请手动打开发布页面',
            icon: 'none'
          });
        }
      });
    } catch (e) {
      console.error('跳转异常：', e);
      wx.showToast({
        title: '跳转异常，请手动打开发布页面',
        icon: 'none'
      });
    }
  },

  // 分享
  onShareAppMessage: function () {
    return {
      title: '我的发布',
      path: '/pages/user/publish'
    };
  },
  
  // 单图加载完成处理
  onSingleImageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    const { width, height } = e.detail;
    if (!width || !height) return;
    
    // 计算图片显示高度，宽度已通过CSS设置为100%
    const maxHeight = 300; // 最大高度
    const screenWidth = wx.getSystemInfoSync().windowWidth - 30; // 屏幕宽度减去padding
    const imageDisplayHeight = Math.min(maxHeight, height * screenWidth / width);
    
    const posts = this.data.posts;
    if (posts[index]) {
      posts[index].imageDisplayHeight = imageDisplayHeight;
      this.setData({
        posts: posts
      });
    }
  },
  
  // 多图加载完成处理
  onMultiImageLoad: function(e) {
    const { index, imgindex } = e.currentTarget.dataset;
    const { width, height } = e.detail;
    if (!width || !height) return;
    
    // 为多图设置统一高度
    const standardHeight = 120; // 标准高度
    
    const posts = this.data.posts;
    if (posts[index]) {
      if (!posts[index].multiImageHeights) {
        posts[index].multiImageHeights = [];
      }
      posts[index].multiImageHeights[imgindex] = standardHeight;
      this.setData({
        posts: posts
      });
    }
  },
  
  // 图片加载错误处理
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const imgIndex = e.currentTarget.dataset.imgindex;
    
    const posts = this.data.posts;
    if (posts[index]) {
      if (imgIndex !== undefined) {
        // 多图模式下移除出错的图片
        if (Array.isArray(posts[index].images)) {
          posts[index].images.splice(imgIndex, 1);
        }
      } else {
        // 单图模式下清空图片
        posts[index].images = [];
      }
      
      this.setData({
        posts: posts
      });
    }
  },
  
  // 点击商品
  onProductTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const product = this.data.posts[index].product;
    if (product && product.id) {
      wx.navigateTo({
        url: `/pages/product/detail?id=${product.id}`
      });
    }
  },

  // 添加去登录方法
  goToLogin: function() {
    if (!this._hasRedirect) {
      this._hasRedirect = true;
      wx.redirectTo({ url: '/pages/auth/auth?redirect=/pages/user/publish' });
    }
  },
  
  // 格式化地区显示
  formatRegion: function(region) {
    if (!region) return '';
    if (typeof region === 'string') {
      try {
        region = JSON.parse(region);
      } catch (e) {
        return region;
      }
    }
    if (region.province && region.province.name) {
      let text = region.province.name;
      if (region.city && region.city.name) {
        text += ' ' + region.city.name;
      }
      if (region.district && region.district.name) {
        text += ' ' + region.district.name;
      }
      return text;
    }
    return '';
  }
});

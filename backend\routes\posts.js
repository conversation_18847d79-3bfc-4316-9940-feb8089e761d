/**
 * 帖子路由
 */
const express = require('express');
const { body, query, param } = require('express-validator');
const postController = require('../controllers/postController');
const { checkAuth } = require('../middleware/auth');
const validate = require('../middleware/validation');

const router = express.Router();

// 获取帖子列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
  query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
  query('userId').optional().isString().withMessage('用户ID必须是字符串'),
  query('topic').optional().isString().withMessage('话题必须是字符串'),
  validate
], postController.getPosts);

// 获取帖子轮播图
router.get('/banners', postController.getBanners);

// 获取热门话题
router.get('/topics/hot', postController.getHotTopics);

// 获取帖子详情
router.get('/:id', [
  param('id').isString().withMessage('帖子ID必须是字符串'),
  validate
], postController.getPostById);

// 创建帖子
router.post('/', [
  checkAuth,
  body('content').optional().isString().withMessage('内容必须是字符串'),
  body('images').optional().custom(val => Array.isArray(val) || typeof val === 'string').withMessage('图片必须是数组或字符串'),
  body('video').optional().isString().withMessage('视频必须是字符串'),
  body('topic').optional().isString().withMessage('话题必须是字符串'),
  body('location').optional(), // 允许任意类型
  validate
], postController.createPost);

// 点赞帖子
router.post('/:id/like', [
  param('id').isString().withMessage('帖子ID必须是字符串'),
  validate
], postController.likePost);

// 删除帖子
router.delete('/:id', [
  checkAuth,
  param('id').isString().withMessage('帖子ID必须是字符串'),
  validate
], postController.deletePost);

// 修改帖子可见性
router.put('/:id/visibility', [
  checkAuth,
  param('id').isString().withMessage('帖子ID必须是字符串'),
  body('isHidden').isBoolean().withMessage('可见性必须是布尔值'),
  validate
], postController.togglePostVisibility);

module.exports = router;

<!--pages/user/space.wxml-->
<view class="user-space-container">
  <!-- 顶部用户信息 -->
  <view class="user-header">
    <image class="user-avatar" src="{{userInfo.avatarUrl}}"></image>
    <view class="user-info">
      <view class="user-nickname">{{userInfo.nickName}}</view>
      <view class="user-id">ID: {{userInfo.id}}</view>
    </view>
    <view class="contact-btn mini-btn" bindtap="onContactTap">联系方式</view>
  </view>

  <!-- 第二排按钮，间距加大 -->
  <view class="user-actions" style="margin-top: 24rpx;">
    <view class="follow-btn mini-btn" 
      bindtap="onFollowTap" 
      wx:if="{{userInfo.id !== (globalUserInfo.id || globalUserInfo.userId)}}"
    >{{isFollowed ? '已关注' : '关注'}}</view>
    <view class="follow-btn mini-btn disabled" 
      wx:if="{{userInfo.id === (globalUserInfo.id || globalUserInfo.userId)}}"
    >关注</view>
    <view class="message-btn mini-btn" bindtap="onMessageTap">私信</view>
  </view>

  <!-- 用户发布的所有帖子，结构与首页一致 -->
  <view class="user-posts">
    <view class="post-item card" wx:for="{{posts}}" wx:key="id" bindtap="onPostTap" data-id="{{item.id}}">
      <view class="post-user">
        <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
        <view class="user-info">
          <view class="username">{{userInfo.nickName}}</view>
          <view class="post-time">{{item.displayTime}}</view>
        </view>
      </view>
      <view class="post-content">
        <text class="content-text">{{item.content}}</text>
        <video wx:if="{{item.video}}" src="{{item.video}}" controls="true" class="post-video" style="width:100%;display:block;margin:0 auto 8px auto;"></video>
        <view class="single-image-wrapper" wx:if="{{item.images.length === 1}}">
          <image class="single-image" src="{{item.images[0]}}" mode="aspectFill"></image>
        </view>
        <view class="image-grid" wx:if="{{item.images.length > 1}}">
          <image wx:for="{{item.images}}" wx:for-item="image" wx:for-index="imgIndex" wx:key="imgIndex" src="{{image}}" class="grid-image" mode="aspectFill"></image>
        </view>
      </view>
      <!-- 发布区域和主题类别 -->
      <view class="post-meta" wx:if="{{item.region || item.topic || item.topicsArray}}">
        <view class="post-region" wx:if="{{item.region}}">{{item.regionText}}</view>
        <!-- 多主题标签显示 -->
        <block wx:if="{{item.topicsArray && item.topicsArray.length > 0}}">
          <view class="post-topic" wx:for="{{item.topicsArray}}" wx:for-item="topicItem" wx:key="*this">{{topicItem}}</view>
        </block>
        <!-- 兼容旧版单主题显示 -->
        <block wx:elif="{{item.topic}}">
          <view class="post-topic">{{item.topic}}</view>
        </block>
      </view>
      <!-- 互动栏 -->
      <view class="interaction-bar">
        <view class="interaction-item" catchtap="onLikeTap" data-index="{{index}}">
          <image src="{{item.isLiked ? '/images/icons2/已点赞.png' : '/images/icons2/未点赞.png'}}"></image>
          <text>({{item.likeCount || 0}})</text>
        </view>
        <view class="interaction-item" catchtap="onCommentTap" data-id="{{item.id}}">
          <image src="/images/icons2/评论.png"></image>
          <text>({{item.commentCount || 0}})</text>
        </view>
        <view class="interaction-item" catchtap="onShareTap" data-index="{{index}}">
          <image src="/images/icons2/分享.png"></image>
          <text>转发 ({{item.shareCount || 0}})</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部安全区和提示 -->
  <view style="height: 48px; display: flex; align-items: center; justify-content: center;">
    <text style="color: #999; font-size: 14px;">没有更多贴子</text>
  </view>
</view> 
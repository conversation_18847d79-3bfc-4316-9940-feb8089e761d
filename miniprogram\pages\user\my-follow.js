const app = getApp();
const { userApi } = require('../../utils/api');

Page({
  data: {
    followList: [],
    loading: true
  },
  onLoad() {
    this.loadFollowList();
  },
  onShow() {
    // 每次页面显示时同步本地 token 到全局
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    if (token && userInfo) {
      app.globalData.isLogin = true;
      app.globalData.userInfo = userInfo;
    } else {
      app.globalData.isLogin = false;
      app.globalData.userInfo = null;
    }
    // 检查登录状态
    if (!app.globalData.isLogin) {
      wx.showModal({
        title: '未登录',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({ url: '/pages/auth/auth' });
        }
      });
      return;
    }
    // 已登录则刷新关注列表
    this.loadFollowList();
  },
  loadFollowList() {
    this.setData({ loading: true });
    userApi.getMyFollowList().then(res => {
      // 我的关注接口返回
      if (res.success && Array.isArray(res.data) && res.data.length > 0) {
        this.setData({ followList: res.data, loading: false });
      } else {
        wx.showToast({ title: res.message || '暂无关注', icon: 'none' });
        this.setData({ followList: [], loading: false });
      }
    }).catch(() => {
      wx.showToast({ title: '网络错误', icon: 'none' });
      this.setData({ loading: false });
    });
  },
  onUserTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/user/space?id=${id}` });
  }
}); 
# 回滚到460dab3版本修复部署问题总结

## 问题背景

根据最新的部署失败日志分析，发现微信云托管平台会自动注入证书管理脚本，当检测到cert目录和相关配置时，会强制执行证书初始化，导致容器启动失败。

## 解决方案

采用回滚策略，恢复到已知可用的460dab3版本配置。该版本能够正常部署的关键原因：

1. **没有cert目录** - 避免触发微信云托管的证书管理
2. **简单的Dockerfile配置** - 使用node:18-alpine，只安装必要的编译工具
3. **没有复杂的生命周期钩子** - 避免poststart hook导致的启动问题
4. **端口配置一致** - 统一使用3000端口

## 具体修改

### 1. Dockerfile
```dockerfile
FROM node:18-alpine

# 安装Python和编译工具
RUN apk add --no-cache python3 make g++ gcc

# 创建工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖前切换npm源为淘宝镜像，加速安装
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖
RUN npm install --production

# 复制所有文件
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3001
ENV USE_MYSQL=true

# 暴露端口
EXPOSE 3001

# 启动命令
CMD ["node", "server.js"]
```

### 2. server.js
```javascript
const app = require('./app');

// 云托管环境统一用 process.env.PORT 或 3000
const port = process.env.PORT || 3000;

console.log('DB_PASSWORD:', process.env.DB_PASSWORD);

app.listen(port, () => {
  console.log(`猎优企后端服务已启动，端口：${port}`);
});
```

### 3. container.config.json
```json
{
  "containerPort": 3000,
  "minNum": 0,
  "maxNum": 5,
  "cpu": 1,
  "mem": 2,
  "policyType": "cpu",
  "policyThreshold": 60,
  "envParams": {
    "NODE_ENV": "production",
    "USE_MYSQL": "true",
    "DB_HOST": "mysql",
    "DB_PORT": "3306",
    "DB_USER": "root",
    "DB_PASSWORD": "{{MYSQL_PASSWORD}}",
    "DB_NAME": "lieyouqi"
  },
  "customLogs": "stdout",
  "initialDelaySeconds": 30,
  "readinessProbe": {
    "httpGet": {
      "path": "/health",
      "port": 3000
    },
    "initialDelaySeconds": 60,
    "periodSeconds": 15
  },
  "livenessProbe": {
    "httpGet": {
      "path": "/health",
      "port": 3000
    },
    "initialDelaySeconds": 60,
    "periodSeconds": 15
  }
}
```

## 删除的内容

1. **backend/cert/** 整个目录
2. **backend/cert/initenv.sh** 证书初始化脚本
3. **backend/cert/app-init.sh** 应用初始化脚本
4. **lifecycleHook配置** 从container.config.json中移除
5. **复杂的目录初始化逻辑** 从server.js中移除
6. **异常处理和优雅关闭** 从server.js中移除

## 预期效果

1. **完全避免证书管理冲突** - 没有cert目录，微信云托管不会触发证书管理
2. **简化启动流程** - 回到最简单的配置，减少出错可能
3. **配置一致性** - 端口配置统一，避免连接问题
4. **部署稳定性** - 使用已验证可用的460dab3版本配置

## 部署验证要点

1. 容器应该能够快速启动，无证书相关错误
2. 应用启动时间应在60秒内完成
3. 健康检查应在90秒内开始响应
4. 数据库连接正常
5. 无SIGKILL错误

## 总结

通过回滚到460dab3版本的简单配置，避免了微信云托管平台的证书管理机制干扰，确保应用能够正常部署和运行。这个版本已经过生产环境验证，具有良好的稳定性。

Page({
  data: {
    groupName: '',
    groupDesc: '',
    visible: true,
    needApprove: false,
    coverUrl: '/images/icons2/qun.png',
    coverLocalPath: ''
  },
  onGroupNameInput(e) {
    this.setData({ groupName: e.detail.value });
  },
  onGroupDescInput(e) {
    this.setData({ groupDesc: e.detail.value });
  },
  onVisibleChange(e) {
    this.setData({ visible: e.detail.value });
  },
  onApproveChange(e) {
    this.setData({ needApprove: e.detail.value });
  },
  onChooseCover() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {        const file = res.tempFiles[0];
        if (file.size > 1.2 * 1024 * 1024) {
          wx.showToast({ title: '图片不能大于1.2M', icon: 'none' });
          return;
        }
        // 直接传递本地路径给裁剪页
        wx.navigateTo({
          url: `/pages/message/cropper/cropper?src=${encodeURIComponent(file.tempFilePath)}`
        });
      }
    });
  },
  async onSubmit() {
    const { groupName, groupDesc, visible, needApprove, coverUrl, coverLocalPath } = this.data;
    if (!groupName.trim()) {
      wx.showToast({ title: '请输入群名称', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '创建中...', mask: true });
    let avatarUrl = coverUrl;
    if (coverLocalPath && coverUrl === coverLocalPath) {
      try {
        const { uploadFile } = require('../../utils/api');
        avatarUrl = await uploadFile(coverLocalPath);
      } catch (e) {
        wx.hideLoading();
        wx.showToast({ title: '封面上传失败', icon: 'none' });
        return;
      }
    }
    const { groupApi } = require('../../utils/api');
    groupApi.createGroup({
      name: groupName,
      description: groupDesc,
      visible,
      needApprove,
      avatar: avatarUrl
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.showToast({ title: '创建成功', icon: 'success' });
        setTimeout(() => {
          wx.navigateBack();
        }, 800);
      } else {
        wx.showToast({ title: res.message || '创建失败', icon: 'none' });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误', icon: 'none' });
    });
  }
}); 
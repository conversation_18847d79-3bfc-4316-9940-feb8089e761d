console.log('开始启动服务器...');

const app = require('./app');

console.log('应用加载完成');

// 云托管环境统一用 process.env.PORT 或 3000
const port = process.env.PORT || 3000;

console.log('环境变量 PORT:', process.env.PORT);
console.log('最终使用端口:', port);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);

console.log('开始监听端口...');
app.listen(port, () => {
  console.log(`猎优企后端服务已启动，端口：${port}`);
  console.log('服务器启动完成！');
});
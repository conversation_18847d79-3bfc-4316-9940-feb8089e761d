const app = require('./app');
const fs = require('fs');
const path = require('path');

// 确保必要的目录存在
const ensureDirectories = () => {
  const dirs = [
    path.join(__dirname, 'uploads'),
    path.join(__dirname, 'public', 'qrcode'),
    path.join(__dirname, '__tmp__')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true, mode: 0o777 });
      console.log(`创建目录: ${dir}`);
    }
  });
};

// 执行目录初始化
ensureDirectories();

// 云托管环境统一用 process.env.PORT 或 3001
const port = process.env.PORT || 3001;

// 添加未捕获异常处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的 Promise 拒绝:', reason);
});

// 启动服务器
const server = app.listen(port, () => {
  console.log(`猎优企后端服务已启动，端口：${port}`);
  console.log('环境变量:', {
    NODE_ENV: process.env.NODE_ENV,
    DB_HOST: process.env.DB_HOST,
    DB_PORT: process.env.DB_PORT,
    DB_NAME: process.env.DB_NAME
  });
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，准备关闭服务...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
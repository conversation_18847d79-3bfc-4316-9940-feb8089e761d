# 关于我们页面

## 📋 功能概述

关于我们页面展示公司的详细信息，包括公司简介、联系方式、基本信息等，所有数据从数据库动态读取。

## 🎨 页面设计

### 页面结构
1. **公司头部**
   - 公司Logo（默认使用关于图标）
   - 公司名称
   - 企业类型

2. **公司简介**
   - 详细的公司介绍文本
   - 支持长文本显示

3. **基本信息**
   - 成立时间
   - 企业类型
   - 企业状态

4. **联系方式**
   - 📱 联系电话（可点击拨打）
   - 📍 公司地址（可点击复制）
   - ✉️ 邮箱地址（可点击复制）
   - 🌐 官方网站（可点击复制）
   - 🕒 营业时间
   - 👤 联系人

5. **底部信息**
   - 感谢语和服务承诺

## 🔧 技术实现

### 数据来源
- 从 `company_info` 数据库表读取
- 使用现有的公司信息API：`/api/company/info`
- 支持容错处理，API失败时自动尝试列表接口

### 交互功能
- **电话拨打**：点击电话号码直接拨打
- **地址复制**：点击地址复制到剪贴板
- **邮箱复制**：点击邮箱复制到剪贴板
- **网站复制**：点击网站复制到剪贴板
- **下拉刷新**：支持下拉刷新数据

### 页面配置
```json
{
  "navigationBarTitleText": "关于我们",
  "enablePullDownRefresh": true,
  "backgroundColor": "#667eea",
  "backgroundTextStyle": "light",
  "navigationBarBackgroundColor": "#667eea",
  "navigationBarTextStyle": "white"
}
```

## 📱 使用方式

### 用户访问路径
1. 进入小程序"我的"页面
2. 点击"其他功能"区域的"关于我们"
3. 查看公司详细信息

### 管理员更新信息
通过后端API或数据库直接更新 `company_info` 表中的数据。

## 🎯 页面特色

### 视觉设计
- 渐变背景色（蓝紫色渐变）
- 卡片式布局
- 圆角设计
- 阴影效果
- 响应式适配

### 用户体验
- 加载状态提示
- 错误处理和提示
- 一键操作（拨打、复制）
- 流畅的动画效果

## 🔗 相关文件

### 前端文件
- `index.wxml` - 页面结构
- `index.wxss` - 页面样式
- `index.js` - 页面逻辑
- `index.json` - 页面配置

### 后端支持
- `/api/company/info` - 获取公司信息API
- `/api/company/list` - 获取公司信息列表API
- `company_info` 表 - 数据存储

### 入口配置
- `app.json` - 页面注册
- `pages/profile/profile.js` - 跳转逻辑

## 📊 数据字段

页面使用的数据库字段：
- `company_name` - 公司名称
- `company_description` - 公司简介
- `company_address` - 公司地址
- `company_phone` - 联系电话
- `company_email` - 邮箱地址
- `company_website` - 官方网站
- `business_hours` - 营业时间
- `contact_person` - 联系人
- `company_type` - 企业类型
- `established_date` - 成立时间
- `company_status` - 企业状态

## 🚀 部署说明

1. 确保数据库中有 `company_info` 表和数据
2. 后端API服务正常运行
3. 小程序编译并上传

## 🔍 测试验证

运行测试脚本：
```bash
node backend/scripts/test-about-page.js
```

测试内容：
- 数据库连接
- 公司信息数据
- API接口响应
- 页面功能验证

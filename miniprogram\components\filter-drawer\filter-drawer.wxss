/* 整体容器 */
.filter-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
  transition: all 0.3s ease;
  opacity: 0;
}

.filter-container.visible {
  opacity: 1;
  pointer-events: auto;
}

/* 蒙层 */
.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.visible .filter-mask {
  opacity: 1;
}

/* 抽屉主体 */
.filter-drawer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60vh;
  background-color: #FFFFFF;
  border-radius: 20px 20px 0 0;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.visible .filter-drawer {
  transform: translateY(0);
}

/* 顶部拖动条 */
.drag-handle {
  width: 36px;
  height: 4px;
  background-color: #E0E0E0;
  border-radius: 2px;
  margin: 12px auto 4px;
}

/* 筛选内容区域 - 可滚动 */
.filter-content {
  flex: 1;
  overflow-y: auto;
}

/* 标题栏 */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 12px;
  border-bottom: 1px solid #EEEEEE;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.header-reset {
  font-size: 14px;
  color: #666666;
}

.header-reset:active {
  opacity: 0.7;
}

/* 筛选区块 */
.filter-section {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F5F5;
}

.section-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

/* 区域选择器 */
.region-selector {
  width: 100%;
}

.region-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 38px;
  padding: 0 12px;
  background-color: #F8F8F8;
  border-radius: 8px;
}

.region-text {
  font-size: 14px;
  color: #333333;
}

.region-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}

/* 主题标签容器 */
.topic-container {
  display: flex;
  flex-wrap: wrap;
  margin: -4px;
}

/* 主题标签 */
.topic-tag {
  padding: 6px 12px;
  background-color: #F8F8F8;
  border-radius: 16px;
  margin: 4px;
  font-size: 13px;
  color: #333333;
  transition: all 0.2s;
  position: relative;
  display: inline-flex;
  align-items: center;
}

.topic-tag:active {
  opacity: 0.7;
  transform: scale(0.97);
}

.topic-tag.active {
  color: #FFFFFF;
  background-color: #333333;
  font-weight: 500;
}

.check-icon {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 14px;
  height: 14px;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 9px;
  font-weight: bold;
  z-index: 10;
  border: 1px solid #FFFFFF;
}

/* 底部按钮 */
.filter-footer {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #EEEEEE;
}

.footer-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s;
}

.footer-btn.cancel {
  background-color: #F5F5F5;
  color: #666666;
  margin-right: 12px;
}

.footer-btn.confirm {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

.btn-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

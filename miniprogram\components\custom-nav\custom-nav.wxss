/* components/custom-nav/custom-nav.wxss */
.custom-nav {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-sizing: border-box;
  position: relative; /* 添加相对定位，使标题的绝对定位相对于导航栏 */
}

.back-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2; /* 确保按钮在标题上方 */
}

.back-btn image {
  width: 20px;
  height: 20px;
}

.title {
  font-size: 20px; /* 增大字号 */
  font-weight: bold;
  color: #333333;
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 1;
}

.title.has-back {
  /* 不需要特殊处理，使用绝对定位实现真正的居中 */
}

.settings-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2; /* 确保按钮在标题上方 */
}

.settings-btn image {
  width: 20px;
  height: 20px;
}

.nav-placeholder {
  width: 100%;
}

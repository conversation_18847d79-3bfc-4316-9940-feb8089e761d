/**
 * 用户服务
 */
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config');
const db = require('../config/db');
const Follow = require('../models/Follow');

class UserService {
  async login(username, password) {
    try {
      // 查找用户
      const user = await User.findByUsername(username);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 验证密码
      console.log('验证密码:', password);
      console.log('用户数据:', user);

      // 尝试验证不同的密码字段
      let passwordValid = false;

      // 1. 尝试验证MD5哈希的password字段
      if (user.password) {
        const hashedPasswordMD5 = crypto.createHash('md5').update(password).digest('hex');
        passwordValid = user.password === hashedPasswordMD5;
        console.log('MD5密码验证:', passwordValid);
      }

      // 2. 尝试验证SHA-256哈希的account_password字段
      if (!passwordValid && user.account_password) {
        const hashedPasswordSHA256 = crypto.createHash('sha256').update(password + (user.password_salt || '')).digest('hex');
        passwordValid = user.account_password === hashedPasswordSHA256;
        console.log('SHA-256密码验证:', passwordValid);
      }

      if (!passwordValid) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 生成token
      const token = jwt.sign(
        { userId: user.id, username: user.username },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );

      // 获取用户统计信息
      const stats = await User.getUserStats(user.id);

      return {
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            ...stats
          }
        },
        message: '登录成功'
      };
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  async loginByPhone(phone, code) {
    try {
      console.log('手机号登录:', phone, code);
      // 1. 校验验证码（5分钟内未用）
      const now = Date.now();
      const validTime = 5 * 60 * 1000;
      const verifyRes = await db.query(
        'SELECT * FROM verifycodes WHERE phone = ? AND code = ? AND used = 0 ORDER BY createTime DESC LIMIT 1',
        [phone, code]
      );
      if (!verifyRes.length || now - verifyRes[0].createTime > validTime) {
        return { success: false, message: '验证码错误或已过期' };
      }
      // 标记验证码为已用
      await db.query('UPDATE verifycodes SET used = 1 WHERE id = ?', [verifyRes[0].id]);
      // 2. 查找用户
      let user = await User.findByPhone(phone);
      if (!user) {
        const userData = {
          phone,
          username: `user_${phone.substring(phone.length - 4)}`,
          nickname: `用户${phone.substring(phone.length - 4)}`,
          avatar: '/images/icons2/男头像.png'
        };
        user = await User.create(userData);
      }
      // 3. 生成token
      const token = jwt.sign(
        { userId: user.id, username: user.username },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );
      // 4. 获取用户统计信息
      const stats = await User.getUserStats(user.id);
      return {
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone,
            ...stats
          }
        },
        message: '登录成功'
      };
    } catch (error) {
      console.error('手机登录失败:', error);
      throw error;
    }
  }

  async register(userData) {
    try {
      // 检查用户名是否已存在
      const existingUser = await User.findByUsername(userData.username);
      if (existingUser) {
        return {
          success: false,
          message: '用户名已存在'
        };
      }

      // 检查手机号是否已存在
      if (userData.phone) {
        const existingPhone = await User.findByPhone(userData.phone);
        if (existingPhone) {
          return {
            success: false,
            message: '手机号已被注册'
          };
        }
      }

      // 自动写入推荐人字段
      if (userData.referrerId) {
        userData.referrerId = userData.referrerId;
      } else if (userData.scene) {
        userData.referrerId = userData.scene;
      }

      // 创建用户
      const user = await User.create(userData);

      // 生成token
      const token = jwt.sign(
        { userId: user.id, username: user.username },
        config.jwtSecret,
        { expiresIn: config.jwtExpiration }
      );

      return {
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            nickname: user.nickname,
            avatar: user.avatar,
            phone: user.phone
          }
        },
        message: '注册成功'
      };
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  async getUserInfo(userId) {
    try {
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 获取用户统计信息
      const stats = await User.getUserStats(userId);

      return {
        success: true,
        data: {
          id: user.id,
          username: user.username,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          email: user.email,
          gender: user.gender,
          country: user.country,
          province: user.province,
          city: user.city,
          openid: user.openid,
          role: user.role,
          createTime: user.createTime,
          ...stats
        },
        message: '获取用户信息成功'
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  async updateUserInfo(userId, userData) {
    try {
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 如果有手机号和验证码，需要验证验证码
      if (userData.phone && userData.verifyCode) {
        // 在实际环境中，这里应该验证验证码是否正确
        // 测试环境下，验证码123456始终通过
        if (userData.verifyCode !== '123456') {
          return {
            success: false,
            message: '验证码错误'
          };
        }

        // 验证通过后，删除验证码字段，不保存到数据库
        delete userData.verifyCode;
      }

      // 更新用户信息
      const updatedUser = await User.update(userId, userData);

      // 获取用户统计信息
      const stats = await User.getUserStats(userId);

      return {
        success: true,
        data: {
          id: updatedUser.id,
          username: updatedUser.username,
          nickname: updatedUser.nickname,
          avatar: updatedUser.avatar,
          phone: updatedUser.phone,
          email: updatedUser.email,
          gender: updatedUser.gender,
          country: updatedUser.country,
          province: updatedUser.province,
          city: updatedUser.city,
          openid: updatedUser.openid,
          role: updatedUser.role,
          createTime: updatedUser.createTime,
          ...stats
        },
        message: '更新用户信息成功'
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  async sendVerificationCode(phone) {
    try {
      // 生成6位随机验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const now = Date.now();

      // 检查是否存在验证码表
      try {
        await db.query('SELECT 1 FROM verifycodes LIMIT 1');
      } catch (error) {
        // 如果表不存在，创建表
        console.log('验证码表不存在，创建表');
        await db.query(`
          CREATE TABLE IF NOT EXISTS verifycodes (
            id INTEGER PRIMARY KEY AUTO_INCREMENT,
            phone VARCHAR(20) NOT NULL,
            code VARCHAR(10) NOT NULL,
            createTime BIGINT NOT NULL,
            used BOOLEAN DEFAULT 0
          )
        `);
      }

      // 保存验证码到数据库
      await db.query(
        'INSERT INTO verifycodes (phone, code, createTime) VALUES (?, ?, ?)',
        [phone, code, now]
      );

      console.log('验证码已生成:', code);

      // 在实际环境中，这里应该调用短信服务发送验证码
      // 但在测试环境中，我们直接返回验证码
      return {
        success: true,
        data: { code },
        message: '验证码发送成功'
      };
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  }

  async updatePassword(userId, oldPassword, newPassword) {
    try {
      // 查找用户
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      console.log('修改密码 - 用户ID:', userId);
      console.log('修改密码 - 用户数据:', user);

      // 验证旧密码
      let passwordValid = false;

      // 1. 尝试验证MD5哈希的password字段
      if (user.password) {
        const hashedOldPasswordMD5 = crypto.createHash('md5').update(oldPassword).digest('hex');
        passwordValid = user.password === hashedOldPasswordMD5;
        console.log('修改密码 - MD5密码验证:', passwordValid);
      }

      // 2. 尝试验证SHA-256哈希的account_password字段
      if (!passwordValid && user.account_password) {
        const hashedOldPasswordSHA256 = crypto.createHash('sha256').update(oldPassword + (user.password_salt || '')).digest('hex');
        passwordValid = user.account_password === hashedOldPasswordSHA256;
        console.log('修改密码 - SHA-256密码验证:', passwordValid);
      }

      if (!passwordValid) {
        return {
          success: false,
          message: '旧密码错误'
        };
      }

      // 更新密码 - 同时更新两个密码字段
      const hashedNewPasswordMD5 = crypto.createHash('md5').update(newPassword).digest('hex');

      // 生成随机盐值
      const salt = crypto.randomBytes(16).toString('hex');

      // 使用SHA-256哈希新密码
      const hashedNewPasswordSHA256 = crypto.createHash('sha256')
        .update(newPassword + salt)
        .digest('hex');

      // 直接使用SQL更新，确保更新成功
      const sql = 'UPDATE users SET password = ?, account_password = ?, password_salt = ? WHERE id = ?';
      const params = [hashedNewPasswordMD5, hashedNewPasswordSHA256, salt, userId];

      console.log('修改密码 - 执行SQL更新:', sql);
      console.log('修改密码 - 参数:', params);

      await db.query(sql, params);

      console.log('修改密码 - 密码已更新');

      return {
        success: true,
        message: '密码修改成功'
      };
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }

  async bindWechat(userId, code, userInfo) {
    try {
      // 查找用户
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 在实际环境中，这里应该调用微信API获取openid
      // 这里简化处理，直接使用code作为openid
      const openid = code;

      // 更新用户信息
      await User.update(userId, {
        openid,
        // 如果用户没有头像，使用微信头像
        avatar: user.avatar || userInfo.avatarUrl,
        // 如果用户没有昵称，使用微信昵称
        nickname: user.nickname || userInfo.nickName
      });

      return {
        success: true,
        data: { openid },
        message: '微信绑定成功'
      };
    } catch (error) {
      console.error('绑定微信失败:', error);
      throw error;
    }
  }

  async unbindWechat(userId) {
    try {
      // 查找用户
      const user = await User.findById(userId);

      if (!user) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      // 更新用户信息
      await User.update(userId, { openid: null });

      return {
        success: true,
        message: '微信解绑成功'
      };
    } catch (error) {
      console.error('解绑微信失败:', error);
      throw error;
    }
  }

  async sendVerifyCode(phone) {
    try {
      console.log('发送验证码到手机:', phone);

      // 允许新手机号直接发送验证码，不再校验用户是否已注册
      // const user = await User.findByPhone(phone);
      // if (!user) {
      //   return {
      //     success: false,
      //     message: '该手机号未注册'
      //   };
      // }

      // 生成6位随机验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const now = Date.now();

      // 检查是否存在验证码表
      try {
        await db.query('SELECT 1 FROM verifycodes LIMIT 1');
      } catch (error) {
        // 如果表不存在，创建表
        console.log('验证码表不存在，创建表');
        await db.query(`
          CREATE TABLE IF NOT EXISTS verifycodes (
            id INTEGER PRIMARY KEY AUTO_INCREMENT,
            phone VARCHAR(20) NOT NULL,
            code VARCHAR(10) NOT NULL,
            createTime BIGINT NOT NULL,
            used BOOLEAN DEFAULT 0
          )
        `);
      }

      // 保存验证码到数据库
      await db.query(
        'INSERT INTO verifycodes (phone, code, createTime) VALUES (?, ?, ?)',
        [phone, code, now]
      );

      console.log('验证码已生成:', code);

      // 在实际环境中，这里应该调用短信服务发送验证码
      // 但在测试环境中，我们直接返回验证码
      return {
        success: true,
        message: '验证码已发送',
        data: {
          code: code // 仅在测试环境返回验证码
        }
      };
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  }

  async resetPasswordByPhone(phone, code, newPassword) {
    try {
      console.log('通过手机号重置密码:', phone);

      // 验证手机号是否存在
      const user = await User.findByPhone(phone);
      if (!user) {
        return {
          success: false,
          message: '该手机号未注册'
        };
      }

      // 验证验证码
      // 在实际环境中，应该验证验证码是否正确
      // 测试环境下，验证码123456始终通过
      if (code !== '123456') {
        // 查询验证码表
        const verifyCodeResult = await db.query(
          'SELECT * FROM verifycodes WHERE phone = ? ORDER BY createTime DESC LIMIT 1',
          [phone]
        );

        let isCodeValid = false;
        if (verifyCodeResult.length > 0) {
          const verifyCode = verifyCodeResult[0];
          // 验证码有效期为5分钟
          const validTime = 5 * 60 * 1000;
          const now = Date.now();
          const createTime = verifyCode.createTime;

          if (now - createTime <= validTime && verifyCode.code === code) {
            isCodeValid = true;

            // 标记验证码为已使用
            await db.query(
              'UPDATE verifycodes SET used = 1 WHERE phone = ? AND code = ?',
              [phone, code]
            );
          }
        }

        if (!isCodeValid) {
          return {
            success: false,
            message: '验证码错误或已过期'
          };
        }
      }

      // 更新密码 - 同时更新两个密码字段
      const hashedNewPasswordMD5 = crypto.createHash('md5').update(newPassword).digest('hex');

      // 生成随机盐值
      const salt = crypto.randomBytes(16).toString('hex');

      // 使用SHA-256哈希新密码
      const hashedNewPasswordSHA256 = crypto.createHash('sha256')
        .update(newPassword + salt)
        .digest('hex');

      // 直接使用SQL更新，确保更新成功
      const sql = 'UPDATE users SET password = ?, account_password = ?, password_salt = ? WHERE id = ?';
      const params = [hashedNewPasswordMD5, hashedNewPasswordSHA256, salt, user.id];

      console.log('重置密码 - 执行SQL更新:', sql);
      console.log('重置密码 - 参数:', params);

      await db.query(sql, params);

      console.log('重置密码 - 密码已更新');

      return {
        success: true,
        message: '密码重置成功'
      };
    } catch (error) {
      console.error('重置密码失败:', error);
      throw error;
    }
  }

  async followUser(followerId, followingId) {
    try {
      // 检查用户是否存在
      const followingUser = await User.findById(followingId);
      if (!followingUser) {
        return {
          success: false,
          message: '被关注的用户不存在'
        };
      }
      return await Follow.follow(followerId, followingId);
    } catch (error) {
      console.error('关注用户失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  async unfollowUser(followerId, followingId) {
    try {
      return await Follow.unfollow(followerId, followingId);
    } catch (error) {
      console.error('取消关注失败:', error);
      throw error;
    }
  }

  async checkFollowStatus(followerId, followingId) {
    try {
      // 参数校验，未登录或参数缺失时直接返回未关注
      if (!followerId || !followingId) {
        return {
          success: true,
          data: { isFollowing: false },
          message: '未登录或参数缺失，默认未关注'
        };
      }
      const isFollowing = await Follow.isFollowing(followerId, followingId);
      return {
        success: true,
        data: { isFollowing },
        message: '获取关注状态成功'
      };
    } catch (error) {
      console.error('获取关注状态失败:', error);
      // 异常时也返回未关注，避免前端报错
      return {
        success: true,
        data: { isFollowing: false },
        message: '获取关注状态异常，默认未关注'
      };
    }
  }

  async getUserStats(userId) {
    try {
      const postCount = await db.query('SELECT COUNT(*) as count FROM posts WHERE userId = ?', [userId]);
      const likeCount = await db.query('SELECT SUM(likeCount) as count FROM posts WHERE userId = ?', [userId]);
      const followCount = await Follow.getFollowingCount(userId);
      const fansCount = await Follow.getFollowerCount(userId);

      return {
        postCount: postCount[0].count || 0,
        likeCount: likeCount[0].count || 0,
        followCount,
        fansCount
      };
    } catch (error) {
      console.error('获取用户统计信息失败:', error);
      throw error;
    }
  }

  async getMyFollowList(userId) {
    try {
      // 使用 JOIN 查询直接获取关注用户的详细信息
      const users = await db.query(`
        SELECT 
          u.id, 
          u.nickname, 
          u.nickName, 
          u.avatar,
          u.username,
          u.phone,
          u.createTime
        FROM follows f
        INNER JOIN users u ON f.followingId = u.id
        WHERE f.followerId = ?
        ORDER BY f.createTime DESC
      `, [userId]);

      return { 
        success: true, 
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户' // 确保昵称字段统一
        }))
      };
    } catch (error) {
      console.error('获取我的关注列表失败:', error);
      return { 
        success: false, 
        message: '服务异常: ' + (error && error.message ? error.message : String(error)) 
      };
    }
  }

  async getMyFansList(userId) {
    try {
      // 使用 JOIN 查询直接获取粉丝用户的详细信息
      const users = await db.query(`
        SELECT 
          u.id, 
          u.nickname, 
          u.nickName, 
          u.avatar,
          u.username,
          u.phone,
          u.createTime
        FROM follows f
        INNER JOIN users u ON f.followerId = u.id
        WHERE f.followingId = ?
        ORDER BY f.createTime DESC
      `, [userId]);

      return { 
        success: true, 
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户'
        }))
      };
    } catch (error) {
      console.error('获取我的粉丝列表失败:', error);
      return { 
        success: false, 
        message: '服务异常: ' + (error && error.message ? error.message : String(error)) 
      };
    }
  }

  async getMyPromotionList(userId) {
    try {
      const users = await db.query(`
        SELECT id, nickname, nickName, avatar, createTime
        FROM users
        WHERE referrerId = ?
        ORDER BY createTime DESC
      `, [userId]);
      return {
        success: true,
        data: users.map(user => ({
          ...user,
          nickname: user.nickname || user.nickName || '未知用户'
        }))
      };
    } catch (error) {
      console.error('获取我的推广用户列表失败:', error);
      return {
        success: false,
        message: '服务异常: ' + (error && error.message ? error.message : String(error))
      };
    }
  }

  async getMyComments(userId) {
    try {
      // 查询我对其他用户帖子所有评论
      const comments = await db.query(`
        SELECT c.id as commentId, c.content as myComment, c.createTime as commentTime, 
               p.id as postId, p.content as postContent, p.userId as postUserId,
               u.nickname as postUserNickname, u.avatar as postUserAvatar,
               (
                 SELECT JSON_ARRAYAGG(JSON_OBJECT('id', oc.id, 'userId', oc.userId, 'content', oc.content, 'createTime', oc.createTime))
                 FROM comments oc WHERE oc.postId = p.id AND oc.userId != ?
               ) as otherComments
        FROM comments c
        INNER JOIN posts p ON c.postId = p.id
        INNER JOIN users u ON p.userId = u.id
        WHERE c.userId = ?
        ORDER BY c.createTime DESC
      `, [userId, userId]);
      // 解析otherComments为对象数组
      const result = comments.map(item => ({
        ...item,
        otherComments: item.otherComments ? JSON.parse(item.otherComments) : []
      }));
      return { success: true, data: result };
    } catch (error) {
      console.error('获取我的评论失败:', error);
      return { success: false, message: '服务异常: ' + (error && error.message ? error.message : String(error)) };
    }
  }

  async getPostComments(postId) {
    try {
      // 查询帖子内容及作者信息，补充所有扩展字段
      const posts = await db.query(`
        SELECT p.id, p.content, p.images, p.video, p.topic, p.location, p.linkedProducts, p.product, p.region, p.userInfo, p.createTime, u.nickname, u.nickName, u.avatar
        FROM posts p
        INNER JOIN users u ON p.userId = u.id
        WHERE p.id = ?
        LIMIT 1
      `, [postId]);
      let post = posts && posts.length > 0 ? posts[0] : null;
      if (post) {
        post.nickname = post.nickname || post.nickName || '用户';
        post.avatar = post.avatar || '/images/icons2/男头像.png';
        post.createTime = post.createTime || 0;
        
        // 查询关联商品信息
        try {
          // 先尝试从linkedProducts字段解析
          if (post.linkedProducts) {
            if (typeof post.linkedProducts === 'string') {
              try {
                post.linkedProducts = JSON.parse(post.linkedProducts);
              } catch (e) {
                post.linkedProducts = [];
              }
            }
          } else {
            post.linkedProducts = [];
          }
          
          // 如果linkedProducts为空，查询post_products关联表
          if (!Array.isArray(post.linkedProducts) || post.linkedProducts.length === 0) {
            const linkedProductsResult = await db.query(`
              SELECT p.id, p.name, p.price, p.imageUrl, p.description
              FROM products p
              INNER JOIN post_products pp ON p.id = pp.productId
              WHERE pp.postId = ?
            `, [postId]);
            
            if (linkedProductsResult && linkedProductsResult.length > 0) {
              post.linkedProducts = linkedProductsResult.map(product => {
                // 确保图片URL格式正确
                let imageUrl = product.imageUrl;
                if (imageUrl) {
                  if (imageUrl.startsWith('//')) imageUrl = 'https:' + imageUrl;
                  if (!imageUrl.startsWith('http') && !imageUrl.startsWith('/')) imageUrl = 'https://' + imageUrl;
                }
                
                return {
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  imageUrl: imageUrl,
                  description: product.description
                };
              });
              
              // 打印调试信息
              console.log('【后端】关联商品数据:', JSON.stringify(post.linkedProducts));
            }
          }
          
          // 如果还是空，尝试从product字段获取
          if (!Array.isArray(post.linkedProducts) || post.linkedProducts.length === 0) {
            if (post.product) {
              let productObj;
              if (typeof post.product === 'string') {
                try {
                  productObj = JSON.parse(post.product);
                } catch (e) {
                  console.error('解析product字段失败:', e.message);
                  productObj = null;
                }
              } else {
                productObj = post.product;
              }
              
              if (productObj && (productObj.id || productObj._id)) {
                // 确保图片URL格式正确
                if (productObj.imageUrl) {
                  if (productObj.imageUrl.startsWith('//')) productObj.imageUrl = 'https:' + productObj.imageUrl;
                  if (!productObj.imageUrl.startsWith('http') && !productObj.imageUrl.startsWith('/')) productObj.imageUrl = 'https://' + productObj.imageUrl;
                }
                post.linkedProducts = [productObj];
                console.log('【后端】product字段解析商品数据:', JSON.stringify(productObj));
              }
            }
          }
          
          // 最终调试输出
          console.log('【后端】最终关联商品数据:', JSON.stringify(post.linkedProducts));
        } catch (e) {
          console.error('处理关联商品信息失败:', e);
        }
      }
      const comments = await db.query(`
        SELECT c.id, c.userId, c.content, c.createTime,
               COALESCE(c.userNickname, u.nickname, u.nickName, '用户') as nickname,
               COALESCE(c.userAvatar, u.avatar, '/images/icons2/男头像.png') as avatar
        FROM comments c
        LEFT JOIN users u ON c.userId = u.id
        WHERE c.postId = ?
        ORDER BY c.createTime ASC
      `, [postId]);
      return { success: true, data: { post, comments } };
    } catch (error) {
      console.error('获取帖子评论失败:', error);
      return { success: false, message: '服务异常: ' + (error && error.message ? error.message : String(error)) };
    }
  }

  async addPostComment(userId, postId, content) {
    try {
      if (!content || !postId || !userId) {
        return { success: false, message: '参数不完整' };
      }
      // 查询用户昵称和头像
      const users = await db.query('SELECT nickname, nickName, avatar FROM users WHERE id = ?', [userId]);
      const user = users && users.length > 0 ? users[0] : {};
      const userNickname = user.nickname || user.nickName || '用户';
      const userAvatar = user.avatar || '/images/icons2/男头像.png';
      const now = Date.now();
      await db.query(
        'INSERT INTO comments (userId, postId, content, createTime, userNickname, userAvatar) VALUES (?, ?, ?, ?, ?, ?)',
        [userId, postId, content, now, userNickname, userAvatar]
      );
      return { success: true, message: '评论成功' };
    } catch (error) {
      console.error('发表评论失败:', error);
      return { success: false, message: '服务异常: ' + (error && error.message ? error.message : String(error)) };
    }
  }
}

module.exports = new UserService();

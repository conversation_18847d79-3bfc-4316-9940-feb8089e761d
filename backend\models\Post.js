/**
 * 帖子模型
 */
const db = require('../config/db');
const crypto = require('crypto');

class Post {
  static async findAll(options = {}) {
    // 解构参数并设置默认值
    const { 
      page = 1, 
      pageSize = 10, 
      tab = '0', 
      keyword = '', 
      region = '', 
      useRegionFilter = false, 
      followedOnly = false, 
      userId = '', 
      sortBy = '', 
      sortOrder = '', 
      topics = '',
      topic = '',
      noRegionSet = false 
    } = options;
    
    // 输出调试信息，查看参数值
    console.log('接收到的参数:', options);
    console.log('解构后的noRegionSet值:', noRegionSet);

    let sql = 'SELECT * FROM posts WHERE 1=1';
    const params = [];

    // 基本过滤条件
    if (userId && !followedOnly) {
      sql += ' AND userId = ?';
      params.push(userId);
    }

    // 单个主题筛选
    if (topic) {
      sql += ' AND topic = ?';
      params.push(topic);
    }

    // 多主题筛选（来自筛选页面）
    if (topics) {
      try {
        console.log('原始主题数据:', topics, '类型:', typeof topics);
        
        // 处理多种可能的格式
        let topicArray = topics;
        if (typeof topics === 'string') {
          // 尝试解析逗号分隔的字符串
          topicArray = topics.split(',').map(t => t.trim()).filter(t => t);
          console.log('逗号分隔后:', topicArray);
          
          // 尝试解析JSON字符串
          if (topicArray.length === 1 && (topicArray[0].includes('[') || topicArray[0].includes('{'))) {
            try {
              topicArray = JSON.parse(topics);
              console.log('JSON解析后:', topicArray);
            } catch (e) {
              console.error('解析主题JSON失败:', e);
            }
          }
        }
        
        // 确保 topicArray 是数组
        if (!Array.isArray(topicArray)) {
          topicArray = [topicArray];
        }
        
        // 去除空值并去重
        topicArray = [...new Set(topicArray.filter(t => t))];
        
        console.log('最终处理后的主题数组:', topicArray);

        if (topicArray.length > 0) {
          console.log('使用多主题筛选:', topicArray);
          
          // 创建主题筛选条件
          const topicConditions = [];
          const topicParams = [];
          
          // 对每个主题，建立多条件查询
          const topicOrConditions = [];
          
          topicArray.forEach(topic => {
            if (!topic) return; // 跳过空值
            
            // 为每个主题创建一个条件组
            const topicGroup = [];
            const groupParams = [];
            
            // 1. 检查topic字段
            topicGroup.push('(topic = ? OR topic LIKE ?)');
            // 对topic进行转义处理，特别是处理空格和引号
            const escapedTopic = topic.replace(/[\s'"]/g, match => '\\' + match);
            groupParams.push(topic, `%${topic}%`);
            
            // 2. 检查topics字段（JSON数组）
            // 处理带空格的主题名称，确保JSON格式正确
            const quotedTopic = topic.includes(' ') ? `"${topic}"` : topic;
            const escapedJsonTopic = topic.replace(/"/g, '\\"');
            
            topicGroup.push('(topics LIKE ? OR topics LIKE ? OR topics LIKE ? OR topics LIKE ?)');
            groupParams.push(
              `%${quotedTopic}%`,      // 处理带引号的完整主题名
              `%${escapedJsonTopic}%`, // 处理转义引号的主题名
              `%${topic}%`,            // 模糊匹配主题
              `%${topic.replace(/\s+/g, '%')}%` // 处理主题名中的空格
            );
            
            console.log('处理主题:', topic, '转义后:', escapedTopic, 'JSON格式:', quotedTopic);
            
            // 将当前主题的条件组添加到OR条件中
            if (topicGroup.length > 0) {
              topicOrConditions.push(`(${topicGroup.join(' OR ')})`);
              topicParams.push(...groupParams);
            }
          });
          
          // 添加主题筛选条件到主SQL查询
          if (topicOrConditions.length > 0) {
            sql += ` AND (${topicOrConditions.join(' OR ')})`;
            params.push(...topicParams);
            console.log('主题筛选SQL条件:', topicOrConditions.join(' OR '));
            console.log('主题查询参数:', topicParams);
          }
        }
      } catch (error) {
        console.error('处理主题筛选参数失败:', error);
      }
    }

    // 关键词过滤
    if (keyword) {
      sql += ' AND (content LIKE ? OR title LIKE ? OR userInfo LIKE ?)';
      const likeKeyword = `%${keyword}%`;
      params.push(likeKeyword, likeKeyword, likeKeyword);
    }

    // 关注的用户发布的帖子
    if (followedOnly && userId) {
      console.log('【调试-关注卡片】查询关注用户的帖子，用户ID:', userId);
      
      try {
        // 直接查询关注用户的帖子，使用最简单的SQL语法
        const sql1 = `
          SELECT p.* 
          FROM posts p, follows f 
          WHERE f.followerId = ? AND f.followingId = p.userId AND p.isHidden = 0 
          ORDER BY p.createTime DESC
        `;
        console.log('【调试-关注卡片】简化查询SQL:', sql1);
        console.log('【调试-关注卡片】查询参数:', [userId]);
        
        // 直接执行查询，不使用原来的params和sql变量
        const posts = await db.query(sql1, [userId]);
        console.log('【调试-关注卡片】查询结果数量:', posts.length);
        
        // 如果没有帖子，检查用户是否有关注的人
        if (posts.length === 0) {
          // 检查用户关注的数量
          const followCountSql = 'SELECT COUNT(*) as count FROM follows WHERE followerId = ?';
          const followCountResult = await db.query(followCountSql, [userId]);
          const followCount = followCountResult[0] ? followCountResult[0].count : 0;
          console.log('【调试-关注卡片】用户关注数量:', followCount);
          
          if (followCount === 0) {
            console.log('【调试-关注卡片】用户没有关注任何人，返回特殊标记');
            return [{ _noFollows: true, _message: '您还没有关注任何人' }];
          } else {
            console.log('【调试-关注卡片】用户关注的人没有发帖，返回特殊标记');
            return [{ _noFollows: true, _message: '您关注的用户暂无发贴' }];
          }
        }
        
        // 如果有帖子，直接返回结果
        console.log('【调试-关注卡片】找到关注用户的帖子，返回结果');
        return posts;
      } catch (error) {
        console.error('【错误-关注卡片】处理关注查询时出错:', error);
        // 如果出错，返回空数组，不影响前端显示
        return [];
      }
    }

    // 地区筛选
    let userRegionInfo = null;
    
    // 仅对附近卡片进行特殊处理
    if (tab === '3') {
      // 如果是附近卡片且noRegionSet参数为true，表示用户未设置地区
      if (noRegionSet === true || noRegionSet === 'true') {
        console.log('附近卡片：用户未设置地区');
        // 返回特殊标记，表示用户需要设置地区
        return [{
          id: 'no-region-set',
          _noRegionSet: true,
          _message: '请设置您的所在地区',
          createTime: Date.now()
        }];
      }
    }
    
    // 处理地区筛选
    if (useRegionFilter && region) {
      console.log('开始处理地区筛选:', region, '类型:', typeof region);
      try {
        // 处理地区字符串
        let regionStr = region;
        let regionParts = [];
        
        // 确保 regionStr 是字符串
        if (typeof regionStr !== 'string') {
          regionStr = String(regionStr);
          console.log('将非字符串地区转换为字符串:', regionStr);
        }
        
        // 解析地区字符串
        if (regionStr.includes(' ')) {
          regionParts = regionStr.split(' ').filter(p => p);
          console.log('使用空格分隔地区字符串:', regionParts);
        } else if (regionStr.includes(',')) {
          regionParts = regionStr.split(',').filter(p => p);
          console.log('使用逗号分隔地区字符串:', regionParts);
        } else {
          regionParts = [regionStr];
          console.log('地区字符串不包含分隔符，直接使用:', regionParts);
        }
        
        // 过滤掉“全部”或“全国”等无效值
        regionParts = regionParts.filter(part => 
          part && !['全部', '全国', 'all', 'ALL'].includes(part)
        );
        
        console.log('处理后的地区部分:', regionParts);
        
        // 构建地区筛选条件
        if (regionParts.length > 0) {
          const regionConditions = [];
          const regionParams = [];
          
          // 为每个地区部分创建条件
          regionParts.forEach(part => {
            // 1. 精确匹配
            regionConditions.push('region = ?');
            regionParams.push(part);
            
            // 2. 模糊匹配
            regionConditions.push('region LIKE ?');
            regionParams.push(`%${part}%`);
          });
          
          // 添加地区筛选条件
          if (regionConditions.length > 0) {
            sql += ` AND (${regionConditions.join(' OR ')})`;
            params.push(...regionParams);
            console.log('添加地区筛选条件:', regionConditions.join(' OR '));
            console.log('地区查询参数:', regionParams);
          }
        } else {
          console.log('未添加地区筛选条件 - 无有效地区');
        }
        
        // 保存用户地区信息，用于后续处理
        userRegionInfo = {
          regionParts,
          fullRegion: regionParts.join(',')
        };
        
        console.log('用户地区信息:', userRegionInfo);    
      } catch (error) {
        console.error('解析地区信息失败:', error);
      }
    }

    // 排序
    if (sortBy && sortOrder) {
      const safeColumns = ['createTime', 'likeCount', 'commentCount', 'viewCount'];
      const safeOrders = ['asc', 'desc'];
      
      if (safeColumns.includes(sortBy) && safeOrders.includes(sortOrder.toLowerCase())) {
        sql += ` ORDER BY ${sortBy} ${sortOrder}`;
      } else {
        sql += ' ORDER BY createTime DESC';
      }
    } else if (tab === '2') { // 热门卡片，确保点赞最多的排在最前面
      sql += ' ORDER BY likeCount DESC, createTime DESC';
    } else {
      sql += ' ORDER BY createTime DESC';
    }

    // 分页
    const safePage = parseInt(page, 10) > 0 ? parseInt(page, 10) : 1;
    const safePageSize = parseInt(pageSize, 10) > 0 ? parseInt(pageSize, 10) : 10;
    const offset = (safePage - 1) * safePageSize;
    sql += ` LIMIT ${Number(safePageSize)} OFFSET ${Number(offset)}`;
    console.log('Post SQL查询:', sql);
    console.log('Post SQL参数:', params);

    // 执行查询
    const posts = await db.query(sql, params);
    console.log(`查询到 ${posts.length} 条帖子`);
    
    // 附近卡片的特殊处理：当用户所在地区没有帖子时，显示其他地区的帖子
    if (tab === '3' && userRegionInfo && posts.length === 0) {
      console.log('用户当前地区没有帖子，将显示全国和其他地区的帖子');
      
      // 构建查询全国帖子的SQL
      let nationalSql = 'SELECT *, "national" as postSource FROM posts WHERE isHidden = 0';
      const nationalParams = [];
      
      // 添加其他必要的筛选条件（保留原来的非地区筛选条件）
      if (keyword) {
        nationalSql += ' AND (content LIKE ? OR title LIKE ? OR userInfo LIKE ?)';
        const likeKeyword = `%${keyword}%`;
        nationalParams.push(likeKeyword, likeKeyword, likeKeyword);
      }
      
      // 构建查询其他地区帖子的SQL（排除用户当前地区）
      let otherRegionsSql = 'SELECT *, "other" as postSource FROM posts WHERE isHidden = 0';
      const otherRegionsParams = [];
      
      // 排除用户当前地区
      if (userRegionInfo.city && userRegionInfo.city !== '全部') {
        otherRegionsSql += ' AND (region NOT LIKE ? OR region IS NULL)';
        otherRegionsParams.push(`%${userRegionInfo.city}%`);
      } else if (userRegionInfo.province && userRegionInfo.province !== '全部') {
        otherRegionsSql += ' AND (region NOT LIKE ? OR region IS NULL)';
        otherRegionsParams.push(`%${userRegionInfo.province}%`);
      }
      
      // 添加关键词筛选（如果有）
      if (keyword) {
        otherRegionsSql += ' AND (content LIKE ? OR title LIKE ? OR userInfo LIKE ?)';
        const likeKeyword = `%${keyword}%`;
        otherRegionsParams.push(likeKeyword, likeKeyword, likeKeyword);
      }
      
      // 排序和分页
      nationalSql += ' ORDER BY createTime DESC';
      otherRegionsSql += ' ORDER BY createTime DESC';
      
      // 分页参数调整，确保有足够的数据显示
      const halfPageSize = Math.ceil(safePageSize / 2);
      nationalSql += ` LIMIT ${halfPageSize}`;
      otherRegionsSql += ` LIMIT ${safePageSize}`;
      
      console.log('全国帖子查询SQL:', nationalSql);
      console.log('全国帖子查询参数:', nationalParams);
      console.log('其他地区帖子查询SQL:', otherRegionsSql);
      console.log('其他地区帖子查询参数:', otherRegionsParams);
      
      // 执行查询
      const nationalPosts = await db.query(nationalSql, nationalParams);
      const otherRegionsPosts = await db.query(otherRegionsSql, otherRegionsParams);
      
      console.log(`查询到 ${nationalPosts.length} 条全国帖子`);
      console.log(`查询到 ${otherRegionsPosts.length} 条其他地区帖子`);
      
      // 合并结果，先显示全国的，然后是其他地区的
      const combinedPosts = [...nationalPosts, ...otherRegionsPosts];
      
      // 添加标记，指示这是全国或其他地区的帖子，而非用户当前地区的
      combinedPosts.forEach(post => {
        post._isNationalFallback = true;
        post._userRegionInfo = userRegionInfo;
        // 保留来源标记（全国或其他地区）
        post._source = post.postSource || 'national';
        delete post.postSource; // 删除临时字段
      });
      
      // 去重，确保不会显示重复帖子
      const uniquePostMap = new Map();
      combinedPosts.forEach(post => {
        if (!uniquePostMap.has(post.id)) {
          uniquePostMap.set(post.id, post);
        }
      });
      
      // 转换为数组
      const uniquePosts = Array.from(uniquePostMap.values());
      
      // 解析商品字段
      uniquePosts.forEach(post => {
        if (post.product && typeof post.product === 'string') {
          try { post.product = JSON.parse(post.product); } catch { post.product = null; }
        }
      });
      
      // 返回合并后的帖子
      return uniquePosts;
    }
    
    // 对于非特殊情况，正常解析帖子数据
    posts.forEach(post => {
      if (post.product && typeof post.product === 'string') {
        try { post.product = JSON.parse(post.product); } catch { post.product = null; }
      }
    });
    
    return posts;
  }

  static async findById(id) {
    const result = await db.query('SELECT * FROM posts WHERE id = ?', [id]);
    if (result.length > 0) {
      const post = result[0];
      // 新增：解析商品字段
      if (post.product && typeof post.product === 'string') {
        try { post.product = JSON.parse(post.product); } catch { post.product = null; }
      }
      return post;
    }
    return null;
  }

  static async create(postData) {
    try {
      console.log('开始创建帖子，原始数据:', JSON.stringify(postData, null, 2));

      // 生成24位随机ID
      function genPostId() {
        return require('crypto').randomBytes(12).toString('hex');
      }

      // 生成新ID
      const id = genPostId();
      console.log('生成的帖子ID:', id);

      // 极简化版：使用最少必要的字段，确保兼容性
      const insertData = {
        id: id,
        userId: postData.userId || null,
        content: postData.content || '',
        createTime: postData.createTime || Date.now(),
        topic: postData.topic || '企业服务' // 确保主题字段始终存在，默认为"企业服务"
      };

      // 处理图片数组
      if (Array.isArray(postData.images)) {
        insertData.images = JSON.stringify(postData.images);
      } else if (typeof postData.images === 'string') {
        insertData.images = postData.images;
      } else {
        insertData.images = '[]';
      }

      // 处理用户信息
      if (typeof postData.userInfo === 'object' && postData.userInfo !== null) {
        insertData.userInfo = JSON.stringify(postData.userInfo);
      } else if (typeof postData.userInfo === 'string') {
        insertData.userInfo = postData.userInfo;
      }

      // 处理可能存在的其他字段
      if (postData.video) {
        insertData.video = postData.video;
      }

      if (postData.topic) {
        insertData.topic = postData.topic;
      }
      
      // 新增：处理多主题数组
      if (postData.topics) {
        if (typeof postData.topics === 'object') {
          insertData.topics = JSON.stringify(postData.topics);
        } else if (typeof postData.topics === 'string') {
          insertData.topics = postData.topics;
        }
      }

      if (postData.region) {
        insertData.region = postData.region;
      }

      // 新增：处理商品字段
      if (postData.product) {
        if (typeof postData.product === 'object') {
          insertData.product = JSON.stringify(postData.product);
        } else if (typeof postData.product === 'string') {
          insertData.product = postData.product;
        }
      }

      // 处理可见性字段
      if (postData.isPublic !== undefined) {
        insertData.isPublic = postData.isPublic ? 1 : 0;
      }

      // 处理交互设置
      if (postData.allowComment !== undefined) {
        insertData.allowComment = postData.allowComment ? 1 : 0;
      }

      if (postData.allowForward !== undefined) {
        insertData.allowForward = postData.allowForward ? 1 : 0;
      }

      // 生成插入SQL
      const insertFields = Object.keys(insertData).join(', ');
      const insertPlaceholders = Object.keys(insertData).map(() => '?').join(', ');
      const insertValues = Object.values(insertData);

      const sql = `INSERT INTO posts (${insertFields}) VALUES (${insertPlaceholders})`;

      console.log('最终SQL:', sql);
      console.log('插入字段:', insertFields);
      console.log('插入值:', JSON.stringify(insertValues));

      // 执行插入
      await db.query(sql, insertValues);
      console.log('帖子创建成功:', id);

      // 查询刚创建的帖子
      const [rows] = await db.query('SELECT * FROM posts WHERE id = ?', [id]);
      if (!rows || rows.length === 0) {
        throw new Error('无法查询创建的帖子');
      }

      const createdPost = rows[0];

      // 将JSON字符串转换回对象
      try {
        if (createdPost.images && typeof createdPost.images === 'string') {
          createdPost.images = JSON.parse(createdPost.images);
        }
        if (createdPost.userInfo && typeof createdPost.userInfo === 'string') {
          createdPost.userInfo = JSON.parse(createdPost.userInfo);
        }
        // 新增：解析商品字段
        if (createdPost.product && typeof createdPost.product === 'string') {
          createdPost.product = JSON.parse(createdPost.product);
        }
      } catch (err) {
        console.error('解析JSON字段时出错:', err);
      }

      return createdPost;
    } catch (error) {
      console.error('帖子创建失败:', error.message);
      console.error('错误详情:', error);
      console.error('错误堆栈:', error.stack);
      throw error;
    }
  }

  static async update(id, postData) {
    // 如果images是数组，转换为JSON字符串
    if (Array.isArray(postData.images)) {
      postData.images = JSON.stringify(postData.images);
    }

    // 如果userInfo是对象，转换为JSON字符串
    if (typeof postData.userInfo === 'object') {
      postData.userInfo = JSON.stringify(postData.userInfo);
    }

    await db.update('posts', { id }, postData);
    return await this.findById(id);
  }

  static async like(id) {
    await db.query('UPDATE posts SET likeCount = likeCount + 1 WHERE id = ?', [id]);
    return await this.findById(id);
  }

  static async getHotTopics() {
    return await db.query('SELECT * FROM hotTopics ORDER BY count DESC');
  }

  static async getBanners() {
    const banners = await db.query('SELECT * FROM postBanners ORDER BY id');
    return banners.map(banner => ({
      id: banner.id,
      imageUrl: banner.imageUrl || banner.image,
      linkUrl: banner.linkUrl || banner.url,
      title: banner.title,
      type: banner.type || 'page'
    }));
  }

  static async count(options = {}) {
    const { userId, topic } = options;

    let sql = 'SELECT COUNT(*) as total FROM posts WHERE 1=1';
    const params = [];

    if (userId) {
      sql += ' AND userId = ?';
      params.push(userId);
    }

    if (topic) {
      sql += ' AND topic = ?';
      params.push(topic);
    }

    const result = await db.query(sql, params);
    return result[0].total;
  }

  static async delete(id) {
    return await db.query('DELETE FROM posts WHERE id = ?', [id]);
  }

  static async updateVisibility(id, isHidden) {
    await db.query('UPDATE posts SET isHidden = ? WHERE id = ?', [isHidden ? 1 : 0, id]);
    return await this.findById(id);
  }
}

module.exports = Post;

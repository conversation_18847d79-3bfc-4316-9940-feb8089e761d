/* 全局容器样式 */
.service-container {
  padding: 30rpx 24rpx 120rpx 24rpx;
  background: #f7f7f7;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片通用样式 */
.card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 30rpx;
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
  background: #07c160;
  border-radius: 4rpx 0 0 4rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  position: relative;
}

/* 留言表单样式 */
.message-card {
  margin-top: 20rpx;
}

.msg-box {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 8rpx 16rpx;
  background: #fafafa;
}

.msg-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 32rpx;
  border: none;
  outline: none;
  background: transparent;
  padding: 16rpx 0;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.input-label {
  width: 140rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.input-field {
  flex: 1;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  padding: 16rpx 20rpx;
  margin-left: 12rpx;
  background: #fafafa;
}

.submit-btn {
  width: 280rpx;
  margin: 10rpx auto 10rpx auto;
  display: block;
  background: #07c160;
  color: #fff;
  border-radius: 40rpx;
  font-size: 32rpx;
  padding: 16rpx 0;
  font-weight: 500;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.2);
}

/* 联系方式卡片样式 */
.contact-card {
  margin-top: 40rpx;
}

.contact-content {
  padding: 10rpx 0;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 10rpx 0;
}

.contact-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(7, 193, 96, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.contact-icon image {
  width: 40rpx;
  height: 40rpx;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
}

/* 在线对话按钮 */
.chat-float-btn {
  position: fixed;
  bottom: 80px;
  right: 40rpx;
  width: 95rpx;
  height: 95rpx;
  background: #666666;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
  z-index: 99;
  flex-direction: column;
  padding: 8rpx;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  box-sizing: border-box;
}

.chat-float-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
  background: #555555;
}

.chat-text {
  font-size: 24rpx;
  line-height: 1.2;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.chat-text text {
  display: block;
  line-height: 1.2;
}

.chat-text text:first-child {
  margin-bottom: 2rpx;
}
[2025-05-29 22:56:22] Started by user coding
[2025-05-29 22:56:22] Running in Durability level: MAX_SURVIVABILITY
[2025-05-29 22:56:24] [Pipeline] Start of Pipeline
[2025-05-29 22:56:24] [Pipeline] node
[2025-05-29 22:56:24] Running on <PERSON> in /root/workspace
[2025-05-29 22:56:24] [Pipeline] {
[2025-05-29 22:56:24] [Pipeline] stage
[2025-05-29 22:56:24] [Pipeline] { (检出软件包)
[2025-05-29 22:56:25] Stage "检出软件包" skipped due to when conditional
[2025-05-29 22:56:25] [Pipeline] }
[2025-05-29 22:56:25] [Pipeline] // stage
[2025-05-29 22:56:25] [Pipeline] stage
[2025-05-29 22:56:25] [Pipeline] { (检出 ZIP 包)
[2025-05-29 22:56:25] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-29 22:56:25] [Pipeline] }
[2025-05-29 22:56:25] [Pipeline] // stage
[2025-05-29 22:56:25] [Pipeline] stage
[2025-05-29 22:56:25] [<PERSON>peline] { (检出代码仓库)
[2025-05-29 22:56:25] [Pipeline] sh
[2025-05-29 22:56:25] + git clone ****** .
[2025-05-29 22:56:25] Cloning into '.'...
[2025-05-29 22:56:28] [Pipeline] sh
[2025-05-29 22:56:28] + git checkout main
[2025-05-29 22:56:28] Already on 'main'
[2025-05-29 22:56:28] Your branch is up to date with 'origin/main'.
[2025-05-29 22:56:28] [Pipeline] }
[2025-05-29 22:56:28] [Pipeline] // stage
[2025-05-29 22:56:28] [Pipeline] stage
[2025-05-29 22:56:28] [Pipeline] { (写入 dockerfile)
[2025-05-29 22:56:28] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-29 22:56:28] [Pipeline] }
[2025-05-29 22:56:28] [Pipeline] // stage
[2025-05-29 22:56:28] [Pipeline] stage
[2025-05-29 22:56:28] [Pipeline] { (构建 Docker 镜像)
[2025-05-29 22:56:28] [Pipeline] sh
[2025-05-29 22:56:28] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-144-20250529225619
[2025-05-29 22:56:28] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-29 22:56:28] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-29 22:56:28] Configure a credential helper to remove this warning. See
[2025-05-29 22:56:28] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-29 22:56:28] 
[2025-05-29 22:56:28] Login Succeeded
[2025-05-29 22:56:28] [Pipeline] sh
[2025-05-29 22:56:29] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-144-20250529225619 backend
[2025-05-29 22:56:29] #0 building with "default" instance using docker driver
[2025-05-29 22:56:29] 
[2025-05-29 22:56:29] #1 [internal] load .dockerignore
[2025-05-29 22:56:29] #1 transferring context: 2B done
[2025-05-29 22:56:29] #1 DONE 0.0s
[2025-05-29 22:56:29] 
[2025-05-29 22:56:29] #2 [internal] load build definition from Dockerfile
[2025-05-29 22:56:29] #2 transferring dockerfile: 1.17kB done
[2025-05-29 22:56:29] #2 DONE 0.0s
[2025-05-29 22:56:29] 
[2025-05-29 22:56:29] #3 [internal] load metadata for docker.io/library/node:20-alpine3.18
[2025-05-29 22:56:30] #3 DONE 1.1s
[2025-05-29 22:56:30] 
[2025-05-29 22:56:30] #4 [internal] load build context
[2025-05-29 22:56:30] #4 DONE 0.0s
[2025-05-29 22:56:30] 
[2025-05-29 22:56:30] #5 [ 1/10] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 22:56:30] #5 resolve docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 0.0s done
[2025-05-29 22:56:30] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0B / 42.09MB 0.1s
[2025-05-29 22:56:30] #5 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0B / 1.38MB 0.1s
[2025-05-29 22:56:30] #5 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 0B / 451B 0.1s
[2025-05-29 22:56:30] #5 sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c 1.43kB / 1.43kB done
[2025-05-29 22:56:30] #5 sha256:991e09934c996820b55f872e292ca2eec916ae1f65f44bb22404d43d93e52e78 1.16kB / 1.16kB done
[2025-05-29 22:56:30] #5 sha256:37173254e18884787979e033e408a4bae4f2c8b2bddb6f5bc3a727a58fd114e4 7.21kB / 7.21kB done
[2025-05-29 22:56:30] #5 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0B / 3.40MB 0.1s
[2025-05-29 22:56:31] #5 ...
[2025-05-29 22:56:31] 
[2025-05-29 22:56:31] #4 [internal] load build context
[2025-05-29 22:56:31] #4 transferring context: 35.49MB 0.2s done
[2025-05-29 22:56:31] #4 DONE 0.2s
[2025-05-29 22:56:31] 
[2025-05-29 22:56:31] #5 [ 1/10] FROM docker.io/library/node:20-alpine3.18@sha256:53108f67824964a573ea435fed258f6cee4d88343e9859a99d356883e71b490c
[2025-05-29 22:56:31] #5 sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d 451B / 451B 0.2s done
[2025-05-29 22:56:31] #5 sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 1.38MB / 1.38MB 0.4s done
[2025-05-29 22:56:31] #5 sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 3.40MB / 3.40MB 0.4s done
[2025-05-29 22:56:31] #5 extracting sha256:619be1103602d98e1963557998c954c892b3872986c27365e9f651f5bc27cab8 0.0s done
[2025-05-29 22:56:31] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 3.15MB / 42.09MB 0.6s
[2025-05-29 22:56:31] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 9.44MB / 42.09MB 0.9s
[2025-05-29 22:56:31] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 12.58MB / 42.09MB 1.0s
[2025-05-29 22:56:31] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 20.97MB / 42.09MB 1.2s
[2025-05-29 22:56:32] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 26.21MB / 42.09MB 1.3s
[2025-05-29 22:56:32] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 39.85MB / 42.09MB 1.5s
[2025-05-29 22:56:32] #5 sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 42.09MB / 42.09MB 1.5s done
[2025-05-29 22:56:32] #5 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709
[2025-05-29 22:56:32] #5 extracting sha256:0f45eb22fa20b605d639b56c941eee09d35f22e3308138f9ca549682f1ca3709 0.8s done
[2025-05-29 22:56:33] #5 extracting sha256:7c695c2a88c4c3a8f44d4dcbbe13fe8035850eed2d96d550d162ac2494ce1057 0.0s done
[2025-05-29 22:56:33] #5 extracting sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d
[2025-05-29 22:56:33] #5 extracting sha256:eb35f839ffe35efef3413fa7d570e3b7f760e058e0ce4e2d559a6cfeef49ed2d done
[2025-05-29 22:56:33] #5 DONE 2.6s
[2025-05-29 22:56:33] 
[2025-05-29 22:56:33] #6 [ 2/10] RUN apk add --no-cache python3 make g++ gcc netcat-openbsd dos2unix
[2025-05-29 22:56:33] #6 0.205 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/main/x86_64/APKINDEX.tar.gz
[2025-05-29 22:56:35] #6 1.823 fetch https://dl-cdn.alpinelinux.org/alpine/v3.18/community/x86_64/APKINDEX.tar.gz
[2025-05-29 22:56:36] #6 2.427 (1/34) Upgrading musl (1.2.4-r2 -> 1.2.4-r3)
[2025-05-29 22:56:36] #6 2.616 (2/34) Installing dos2unix (7.4.4-r1)
[2025-05-29 22:56:36] #6 2.789 (3/34) Installing libstdc++-dev (12.2.1_git20220924-r10)
[2025-05-29 22:56:37] #6 3.731 (4/34) Installing zstd-libs (1.5.5-r4)
[2025-05-29 22:56:37] #6 3.910 (5/34) Installing binutils (2.40-r8)
[2025-05-29 22:56:37] #6 4.128 (6/34) Installing libgomp (12.2.1_git20220924-r10)
[2025-05-29 22:56:37] #6 4.303 (7/34) Installing libatomic (12.2.1_git20220924-r10)
[2025-05-29 22:56:37] #6 4.475 (8/34) Installing gmp (6.2.1-r3)
[2025-05-29 22:56:37] #6 4.651 (9/34) Installing isl26 (0.26-r1)
[2025-05-29 22:56:38] #6 4.838 (10/34) Installing mpfr4 (4.2.0_p12-r0)
[2025-05-29 22:56:38] #6 5.017 (11/34) Installing mpc1 (1.3.1-r1)
[2025-05-29 22:56:38] #6 5.189 (12/34) Installing gcc (12.2.1_git20220924-r10)
[2025-05-29 22:56:41] #6 7.924 (13/34) Installing musl-dev (1.2.4-r3)
[2025-05-29 22:56:41] #6 8.273 (14/34) Installing libc-dev (0.7.2-r5)
[2025-05-29 22:56:42] #6 8.445 (15/34) Installing g++ (12.2.1_git20220924-r10)
[2025-05-29 22:56:42] #6 9.481 (16/34) Installing make (4.4.1-r1)
[2025-05-29 22:56:43] #6 9.656 (17/34) Installing libmd (1.0.4-r2)
[2025-05-29 22:56:43] #6 9.828 (18/34) Installing libbsd (0.11.7-r1)
[2025-05-29 22:56:43] #6 10.000 (19/34) Installing netcat-openbsd (1.219-r1)
[2025-05-29 22:56:43] #6 10.17 (20/34) Installing libexpat (2.7.0-r0)
[2025-05-29 22:56:43] #6 10.34 (21/34) Installing libbz2 (1.0.8-r5)
[2025-05-29 22:56:43] #6 10.52 (22/34) Installing libffi (3.4.4-r2)
[2025-05-29 22:56:44] #6 10.69 (23/34) Installing gdbm (1.23-r1)
[2025-05-29 22:56:44] #6 10.86 (24/34) Installing xz-libs (5.4.3-r1)
[2025-05-29 22:56:44] #6 11.03 (25/34) Installing mpdecimal (2.5.1-r2)
[2025-05-29 22:56:44] #6 11.21 (26/34) Installing ncurses-terminfo-base (6.4_p20230506-r0)
[2025-05-29 22:56:44] #6 11.38 (27/34) Installing libncursesw (6.4_p20230506-r0)
[2025-05-29 22:56:44] #6 11.55 (28/34) Installing libpanelw (6.4_p20230506-r0)
[2025-05-29 22:56:45] #6 11.73 (29/34) Installing readline (8.2.1-r1)
[2025-05-29 22:56:45] #6 11.90 (30/34) Installing sqlite-libs (3.41.2-r3)
[2025-05-29 22:56:45] #6 12.08 (31/34) Installing python3 (3.11.12-r1)
[2025-05-29 22:56:46] #6 12.81 (32/34) Installing python3-pycache-pyc0 (3.11.12-r1)
[2025-05-29 22:56:46] #6 13.20 (33/34) Installing pyc (0.1-r0)
[2025-05-29 22:56:46] #6 13.37 (34/34) Installing python3-pyc (3.11.12-r1)
[2025-05-29 22:56:47] #6 13.54 Executing busybox-1.36.1-r5.trigger
[2025-05-29 22:56:47] #6 13.55 OK: 276 MiB in 50 packages
[2025-05-29 22:56:47] #6 DONE 13.6s
[2025-05-29 22:56:47] 
[2025-05-29 22:56:47] #7 [ 3/10] WORKDIR /app
[2025-05-29 22:56:49] #7 DONE 2.4s
[2025-05-29 22:56:49] 
[2025-05-29 22:56:49] #8 [ 4/10] RUN mkdir -p /app/uploads /app/public/qrcode /app/__tmp__
[2025-05-29 22:56:49] #8 DONE 0.3s
[2025-05-29 22:56:49] 
[2025-05-29 22:56:49] #9 [ 5/10] COPY package*.json ./
[2025-05-29 22:56:49] #9 DONE 0.0s
[2025-05-29 22:56:49] 
[2025-05-29 22:56:49] #10 [ 6/10] RUN npm config set registry https://registry.npmmirror.com
[2025-05-29 22:56:50] #10 DONE 0.7s
[2025-05-29 22:56:50] 
[2025-05-29 22:56:50] #11 [ 7/10] RUN npm install --production
[2025-05-29 22:56:50] #11 0.488 npm WARN config production Use `--omit=dev` instead.
[2025-05-29 22:56:52] #11 2.194 npm WARN deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-29 22:56:52] #11 2.428 npm WARN deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-29 22:56:52] #11 2.498 npm WARN deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-29 22:56:53] #11 2.852 
[2025-05-29 22:56:53] #11 2.852 added 221 packages in 2s
[2025-05-29 22:56:53] #11 2.853 
[2025-05-29 22:56:53] #11 2.853 31 packages are looking for funding
[2025-05-29 22:56:53] #11 2.853   run `npm fund` for details
[2025-05-29 22:56:53] #11 DONE 2.9s
[2025-05-29 22:56:53] 
[2025-05-29 22:56:53] #12 [ 8/10] COPY . .
[2025-05-29 22:56:53] #12 DONE 0.1s
[2025-05-29 22:56:53] 
[2025-05-29 22:56:53] #13 [ 9/10] RUN chmod +x /app/cert/initenv.sh &&     chmod 777 /app/uploads /app/public/qrcode /app/__tmp__ &&     dos2unix /app/cert/initenv.sh &&     sed -i 's/\r$//' /app/cert/initenv.sh
[2025-05-29 22:56:53] #13 0.306 dos2unix: converting file /app/cert/initenv.sh to Unix format...
[2025-05-29 22:56:53] #13 DONE 0.3s
[2025-05-29 22:56:53] 
[2025-05-29 22:56:53] #14 [10/10] RUN rm -f wait-for-it.sh Dockerfile.cloud generate-cert.js &&     rm -rf test docs temp __tmp__ &&     rm -f config/db-backup.js config/db-init.js config/db-seed.js config/db-validate.js
[2025-05-29 22:56:54] #14 DONE 0.3s
[2025-05-29 22:56:54] 
[2025-05-29 22:56:54] #15 exporting to image
[2025-05-29 22:56:54] #15 exporting layers
[2025-05-29 22:56:55] #15 exporting layers 1.3s done
[2025-05-29 22:56:55] #15 writing image sha256:6ef17600b8fcd7186e46f484a2d72930d093585d2c0965fb17d11f7ca227c8fd done
[2025-05-29 22:56:55] #15 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-144-20250529225619 done
[2025-05-29 22:56:55] #15 DONE 1.3s
[2025-05-29 22:56:55] [Pipeline] sh
[2025-05-29 22:56:55] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-29 22:56:55] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-144-20250529225619
[2025-05-29 22:56:55] + awk {print $NF}
[2025-05-29 22:56:55] + echo 镜像的大小是：460MB
[2025-05-29 22:56:55] 镜像的大小是：460MB
[2025-05-29 22:56:55] [Pipeline] echo
[2025-05-29 22:56:55] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-29 22:56:55] [Pipeline] }
[2025-05-29 22:56:55] [Pipeline] // stage
[2025-05-29 22:56:55] [Pipeline] stage
[2025-05-29 22:56:55] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-29 22:56:55] [Pipeline] sh
[2025-05-29 22:56:56] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-144-20250529225619
[2025-05-29 22:56:56] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-29 22:56:56] 4acede84bacb: Preparing
[2025-05-29 22:56:56] 430f1ea85c9a: Preparing
[2025-05-29 22:56:56] 4d182105adbb: Preparing
[2025-05-29 22:56:56] 78d505f9a4dc: Preparing
[2025-05-29 22:56:56] 9b04195f1512: Preparing
[2025-05-29 22:56:56] 1e94c38cb048: Preparing
[2025-05-29 22:56:56] a0758e1f1212: Preparing
[2025-05-29 22:56:56] a6b5eac99cbf: Preparing
[2025-05-29 22:56:56] 2e4ccb8baf81: Preparing
[2025-05-29 22:56:56] 1709f4dd23fa: Preparing
[2025-05-29 22:56:56] 92f7edb51836: Preparing
[2025-05-29 22:56:56] af0fd4ac5053: Preparing
[2025-05-29 22:56:56] aedc3bda2944: Preparing
[2025-05-29 22:56:56] 92f7edb51836: Waiting
[2025-05-29 22:56:56] af0fd4ac5053: Waiting
[2025-05-29 22:56:56] aedc3bda2944: Waiting
[2025-05-29 22:56:56] 1709f4dd23fa: Layer already exists
[2025-05-29 22:56:56] 92f7edb51836: Layer already exists
[2025-05-29 22:56:56] af0fd4ac5053: Layer already exists
[2025-05-29 22:56:56] aedc3bda2944: Layer already exists
[2025-05-29 22:56:57] 4acede84bacb: Pushed
[2025-05-29 22:56:57] a6b5eac99cbf: Pushed
[2025-05-29 22:56:57] 430f1ea85c9a: Pushed
[2025-05-29 22:56:57] 9b04195f1512: Pushed
[2025-05-29 22:56:57] a0758e1f1212: Pushed
[2025-05-29 22:56:57] 1e94c38cb048: Pushed
[2025-05-29 22:57:02] 78d505f9a4dc: Pushed
[2025-05-29 22:57:17] 4d182105adbb: Pushed
[2025-05-29 22:57:49] 2e4ccb8baf81: Pushed
[2025-05-29 22:57:50] lieyouqi-144-20250529225619: digest: sha256:c0271993e3316518f89f4d98e7e26583b4f5fa2f01272608fbd8449e9ab74e4e size: 3037
[2025-05-29 22:57:50] [Pipeline] }
[2025-05-29 22:57:50] [Pipeline] // stage
[2025-05-29 22:57:50] [Pipeline] }
[2025-05-29 22:57:50] [Pipeline] // node
[2025-05-29 22:57:50] [Pipeline] End of Pipeline
[2025-05-29 22:57:50] Finished: SUCCESS
***
-----------构建lieyouqi-144-----------
2025-05-29 22:56:21 create_build_image : creating
2025-05-29 22:57:55 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-144-----------
2025-05-29 22:57:56 create_eks_virtual_service : creating
2025-05-29 22:57:56 check_eks_virtual_service : process, DescribeVersion_user_error_Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-144" in Pod "lieyouqi-144-869b6c4dd9-x298s_yhrfubyv(d6752cf0-37ab-48e9-b364-aa52e04124f1)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
+ '[' 0 -eq 0 ]
+ echo 'User is root, patching env and certs.'
+ '[' '!' -f /app/cert/certificate.crt ]
+ '[' '!' -f /etc/os-release ]
+ . /etc/os-release
+ NAME='Alpine Linux'
+ ID=alpine
+ VERSION_ID=3.18.6
+ PRETTY_NAME='Alpine Linux v3.18'
+ HOME_URL=https://alpinelinux.org/
+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues
+ echo '[I]: os release is alpine'
+ update-ca-certificates -h
/app/cert/initenv.sh: line 66: update-ca-certificates: not found
+ grep ************
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n+ '[' 0 -eq 0 ]\n+ echo 'User is root, patching env and certs.'\n+ '[' '!' -f /app/cert/certificate.crt ]\n+ '[' '!' -f /etc/os-release ]\n+ . /etc/os-release\n+ NAME='Alpine Linux'\n+ ID=alpine\n+ VERSION_ID=3.18.6\n+ PRETTY_NAME='Alpine Linux v3.18'\n+ HOME_URL=https://alpinelinux.org/\n+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues\n+ echo '[I]: os release is alpine'\n+ update-ca-certificates -h\n/app/cert/initenv.sh: line 66: update-ca-certificates: not found\n+ grep ************\n", [service]:[Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-144" in Pod "lieyouqi-144-869b6c4dd9-x298s_yhrfubyv(d6752cf0-37ab-48e9-b364-aa52e04124f1)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: , message: "",Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-144" in Pod "lieyouqi-144-869b6c4dd9-x298s_yhrfubyv(d6752cf0-37ab-48e9-b364-aa52e04124f1)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n",Back-off restarting failed container,Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-144" in Pod "lieyouqi-144-869b6c4dd9-x298s_yhrfubyv(d6752cf0-37ab-48e9-b364-aa52e04124f1)" failed - error: command '/bin/sh /app/cert/initenv.sh' exited with 137: + certFile=/app/cert/certificate.crt
+ certLog=/app/cert.log
+ srcIp=************
+ srcHost=api.weixin.qq.com
+ checkFileCnt=0
+ is_user_root
+ id -u
+ '[' 0 -eq 0 ]
+ echo 'User is root, patching env and certs.'
+ '[' '!' -f /app/cert/certificate.crt ]
+ '[' '!' -f /etc/os-release ]
+ . /etc/os-release
+ NAME='Alpine Linux'
+ ID=alpine
+ VERSION_ID=3.18.6
+ PRETTY_NAME='Alpine Linux v3.18'
+ HOME_URL=https://alpinelinux.org/
+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues
+ echo '[I]: os release is alpine'
+ update-ca-certificates -h
/app/cert/initenv.sh: line 66: update-ca-certificates: not found
+ grep ************
, message: "+ certFile=/app/cert/certificate.crt\n+ certLog=/app/cert.log\n+ srcIp=************\n+ srcHost=api.weixin.qq.com\n+ checkFileCnt=0\n+ is_user_root\n+ id -u\n+ '[' 0 -eq 0 ]\n+ echo 'User is root, patching env and certs.'\n+ '[' '!' -f /app/cert/certificate.crt ]\n+ '[' '!' -f /etc/os-release ]\n+ . /etc/os-release\n+ NAME='Alpine Linux'\n+ ID=alpine\n+ VERSION_ID=3.18.6\n+ PRETTY_NAME='Alpine Linux v3.18'\n+ HOME_URL=https://alpinelinux.org/\n+ BUG_REPORT_URL=https://gitlab.alpinelinux.org/alpine/aports/-/issues\n+ echo '[I]: os release is alpine'\n+ update-ca-certificates -h\n/app/cert/initenv.sh: line 66: update-ca-certificates: not found\n+ grep ************\n",]
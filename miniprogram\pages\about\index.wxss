/* 关于我们页面样式 */
.about-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  color: #666;
  font-size: 32rpx;
}

/* 主要内容 */
.content {
  padding: 40rpx 30rpx;
}

/* 公司头部 */
.company-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 60rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.company-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 30rpx;
  border: 4rpx solid #fff;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.company-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.company-type {
  font-size: 28rpx;
  color: #666;
  background: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 区块样式 */
.section {
  margin-bottom: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.title-icon {
  margin-right: 15rpx;
  font-size: 40rpx;
}

/* 公司简介 */
.company-description {
  font-size: 30rpx;
  line-height: 1.8;
  color: #555;
  text-align: justify;
}

/* 联系方式列表 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  transition: all 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.contact-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
}

.contact-value {
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
}

.contact-value.phone {
  color: #007aff;
  font-weight: 500;
}

.contact-arrow {
  font-size: 30rpx;
  color: #ccc;
  margin-left: 15rpx;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  margin-top: 60rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
}

.footer-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.footer-text:last-child {
  margin-bottom: 0;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .content {
    padding: 30rpx 20rpx;
  }

  .company-header {
    padding: 40rpx 30rpx;
  }

  .section {
    padding: 30rpx;
  }

  .company-name {
    font-size: 42rpx;
  }
}

/* pages/profile/profile.wxss */
.profile-container {
  min-height: 100vh;
  background-color: #FFFFFF;
}

/* 顶部个人信息 */
.profile-header {
  position: relative;
  padding: 20px 16px;
  background-color: #FFFFFF;
  color: #333333;
  border-bottom: 1px solid #EEEEEE;
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  background-color: #1E6A9E;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 16px;
}

.user-info-content {
  flex: 1;
  margin-left: 15px;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333333;
}

.user-id {
  font-size: 14px;
  color: #666666;
}

.vip-badge {
  display: flex;
  align-items: center;
  color: #1E6A9E;
  font-size: 14px;
}

.vip-icon {
  width: 20px;
  height: 20px;
  margin-left: 5px;
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.login-text {
  font-size: 16px;
  margin: 10px 0;
  color: #333333;
}

.login-btn {
  padding: 6px 20px;
  background-color: #1E6A9E;
  color: #FFFFFF;
  border-radius: 18px;
  font-size: 14px;
  font-weight: bold;
}

/* 数据统计 */
.stats-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 5px;
  border-top: 1px solid #EEEEEE;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-num {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
  color: #333333;
}

.stats-label {
  font-size: 12px;
  color: #666666;
}

/* 统计分隔线 */
.stats-divider {
  height: 1px;
  background-color: #EEEEEE;
  margin: 2px 15px;
  opacity: 0.6;
}

/* 内容统计 */
.content-stats {
  display: flex;
  justify-content: space-around;
  padding: 5px 15px 0;
  background-color: #FFFFFF;
  border-bottom: none;
}

.content-stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.content-stats-num {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0;
  line-height: 1.2;
}

.content-stats-label {
  font-size: 12px;
  color: #666666;
  line-height: 1.2;
}

/* 轮播图 */
.banner-section {
  padding: 0 15px 5px;
  background-color: #FFFFFF;
  margin-top: 10px;
  margin-bottom: 10px;
}

.banner-swiper {
  width: 100%;
  height: 130px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: block; /* 确保图片正确显示 */
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.4);
  color: #FFFFFF;
  font-size: 14px;
  line-height: 1.4;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* 内容卡片 */
.content-card {
  margin-top: 0;
  padding: 0 15px;
  background-color: #FFFFFF;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #EEEEEE;
}

.order-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.view-all {
  font-size: 14px;
  color: #666666;
}

/* 订单网格 */
.order-grid {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.order-item {
  position: relative;
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 5px;
}

.order-name {
  font-size: 13px;
  color: #333333;
}

.order-badge {
  position: absolute;
  top: -5px;
  right: 15px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 8px;
  font-size: 10px;
  padding: 0 4px;
}

/* 分隔线 */
.divider {
  height: 6px;
  background-color: #F5F5F5;
}

/* 其他功能 */
.other-functions {
  background-color: #FFFFFF;
  padding: 0 15px;
}

.other-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  padding: 8px 0;
  border-bottom: 1px solid #EEEEEE;
}

.menu-item {
  display: flex;
  align-items: center;
  height: 44px;
  border-bottom: 1px solid #EEEEEE;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 22px;
  height: 22px;
  margin-right: 12px;
}

.menu-name {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

/* 退出登录按钮样式 */
.logout-item {
  margin-top: 10px;
  border-top: 1px solid #EEEEEE;
  padding-top: 5px;
}

.logout-text {
  color: #FF4D4F;
  font-weight: 500;
}

.arrow-right {
  font-size: 14px;
  color: #999999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 20px;
}

/* 开发者选项 */
.dev-options {
  background-color: #f5f5f5;
  padding: 15px;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.dev-options-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.dev-option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
}

.dev-option-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  width: 80px;
}

/* 开发者选项触发区域 - 隐藏的点击区域 */
.dev-trigger {
  position: fixed;
  bottom: 0;
  right: 0;
  width: 50px;
  height: 50px;
  z-index: 100;
  /* 透明不可见 */
  opacity: 0;
}

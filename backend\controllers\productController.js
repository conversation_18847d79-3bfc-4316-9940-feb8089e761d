/**
 * 商品控制器
 */
const productService = require('../services/productService');

exports.getProducts = async (req, res, next) => {
  try {
    // 参数类型转换，page和pageSize为数字，其余保持原类型
    const query = { ...req.query };
    if (query.page) query.page = Number(query.page);
    if (query.pageSize) query.pageSize = Number(query.pageSize);
    // categoryId和subCategoryId保持字符串类型
    const result = await productService.getProducts(query);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getProductById = async (req, res, next) => {
  try {
    const result = await productService.getProductById(req.params.id);
    if (!result.success) {
      return res.status(404).json(result);
    }
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getCategories = async (req, res, next) => {
  try {
    const result = await productService.getCategories();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getShopCategories = async (req, res, next) => {
  try {
    const result = await productService.getShopCategories();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getShopSubCategories = async (req, res, next) => {
  try {
    const result = await productService.getShopSubCategories(req.query.parentId);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getBanners = async (req, res, next) => {
  try {
    const result = await productService.getBanners();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

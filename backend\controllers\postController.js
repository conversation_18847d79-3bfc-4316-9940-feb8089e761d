/**
 * 帖子控制器
 */
const postService = require('../services/postService');

exports.getPosts = async (req, res, next) => {
  try {
    const result = await postService.getPosts(req.query);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getPostById = async (req, res, next) => {
  try {
    const result = await postService.getPostById(req.params.id);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.createPost = async (req, res, next) => {
  try {
    // 验证用户ID
    const userId = req.userData.userId;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录或会话已过期'
      });
    }

    // 基本数据验证
    const { content, images, topic, topics } = req.body;
    if (!content && (!images || images.length === 0)) {
      return res.status(400).json({
        success: false,
        message: '发帖内容不能为空'
      });
    }

    // 处理主题字段
    // 1. 如果topics数组存在且有内容，优先使用
    // 2. 如果没有topics但有topic，使用单个topic
    // 3. 如果都没有，设置默认主题
    if (Array.isArray(topics) && topics.length > 0) {
      // 确保topics数组保留在请求体中
    } else if (!topic || topic.trim() === '') {
      req.body.topic = '企业服务';
      req.body.topics = ['企业服务'];
    } else {
      // 有topic但没有topics，创建topics数组
      req.body.topics = [topic];
    }

    // 处理发帖
    const result = await postService.createPost(req.body, userId);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误，请稍后再试',
      error: error.message
    });
  }
};

exports.likePost = async (req, res, next) => {
  try {
    const result = await postService.likePost(req.params.id);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getHotTopics = async (req, res, next) => {
  try {
    const result = await postService.getHotTopics();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getBanners = async (req, res, next) => {
  try {
    const result = await postService.getBanners();
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.deletePost = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await postService.deletePost(req.params.id, userId);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.togglePostVisibility = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { isHidden } = req.body;
    const result = await postService.togglePostVisibility(req.params.id, userId, isHidden);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

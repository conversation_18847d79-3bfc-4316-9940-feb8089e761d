/**
 * 用户身份管理工具
 * 用于确保用户ID和登录状态的一致性
 */

// 用户身份存储键
const USER_IDENTITY_KEY = 'user_identity';
const TOKEN_KEY = 'token';
const USER_INFO_KEY = 'userInfo';

/**
 * 保存用户身份信息
 * @param {Object} userInfo 用户信息对象
 * @param {String} token 用户令牌
 */
function saveUserIdentity(userInfo, token) {
  if (!userInfo || !userInfo.id) {
    console.error('保存用户身份失败: 无效的用户信息', userInfo);
    return false;
  }

  // 创建身份对象
  const identity = {
    userId: userInfo.id,
    token: token,
    timestamp: Date.now()
  };

  // 保存到本地存储
  try {
    wx.setStorageSync(USER_IDENTITY_KEY, identity);
    console.log('用户身份已保存:', identity);
    return true;
  } catch (error) {
    console.error('保存用户身份失败:', error);
    return false;
  }
}

/**
 * 获取用户身份信息
 * @returns {Object|null} 用户身份对象或null
 */
function getUserIdentity() {
  try {
    const identity = wx.getStorageSync(USER_IDENTITY_KEY);
    return identity || null;
  } catch (error) {
    console.error('获取用户身份失败:', error);
    return null;
  }
}

/**
 * 清除用户身份信息
 */
function clearUserIdentity() {
  try {
    wx.removeStorageSync(USER_IDENTITY_KEY);
    wx.removeStorageSync(TOKEN_KEY);
    wx.removeStorageSync(USER_INFO_KEY);
    console.log('用户身份已清除');
    return true;
  } catch (error) {
    console.error('清除用户身份失败:', error);
    return false;
  }
}

/**
 * 验证用户身份一致性
 * @param {Object} userInfo 用户信息对象
 * @returns {Boolean} 身份是否一致
 */
function validateUserIdentity(userInfo) {
  if (!userInfo || !userInfo.id) {
    console.error('验证用户身份失败: 无效的用户信息', userInfo);
    return false;
  }

  const identity = getUserIdentity();
  if (!identity) {
    console.warn('没有找到用户身份信息');
    return false;
  }

  const isValid = identity.userId === userInfo.id;
  console.log(`用户身份验证: ${isValid ? '一致' : '不一致'}`);
  console.log(`当前用户ID: ${userInfo.id}, 存储的用户ID: ${identity.userId}`);
  
  return isValid;
}

/**
 * 确保用户身份一致性
 * 如果发现不一致，则清除所有用户信息并返回false
 * @param {Object} userInfo 用户信息对象
 * @returns {Boolean} 身份是否一致
 */
function ensureUserIdentity(userInfo) {
  const isValid = validateUserIdentity(userInfo);
  
  if (!isValid) {
    console.warn('用户身份不一致，清除所有用户信息');
    clearUserIdentity();
  }
  
  return isValid;
}

module.exports = {
  saveUserIdentity,
  getUserIdentity,
  clearUserIdentity,
  validateUserIdentity,
  ensureUserIdentity
};

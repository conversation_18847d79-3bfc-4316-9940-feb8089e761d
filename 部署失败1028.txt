[2025-05-30 10:23:11] Started by user coding
[2025-05-30 10:23:11] Running in Durability level: MAX_SURVIVABILITY
[2025-05-30 10:23:13] [Pipeline] Start of Pipeline
[2025-05-30 10:23:13] [Pipeline] node
[2025-05-30 10:23:14] Running on <PERSON> in /root/workspace
[2025-05-30 10:23:14] [Pipeline] {
[2025-05-30 10:23:14] [Pipeline] stage
[2025-05-30 10:23:14] [Pipeline] { (检出软件包)
[2025-05-30 10:23:14] Stage "检出软件包" skipped due to when conditional
[2025-05-30 10:23:14] [Pipeline] }
[2025-05-30 10:23:14] [Pipeline] // stage
[2025-05-30 10:23:14] [Pipeline] stage
[2025-05-30 10:23:14] [Pipeline] { (检出 ZIP 包)
[2025-05-30 10:23:14] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-30 10:23:14] [Pipeline] }
[2025-05-30 10:23:14] [Pipeline] // stage
[2025-05-30 10:23:14] [Pipeline] stage
[2025-05-30 10:23:14] [Pipeline] { (检出代码仓库)
[2025-05-30 10:23:14] [Pipeline] sh
[2025-05-30 10:23:14] + git clone ****** .
[2025-05-30 10:23:14] Cloning into '.'...
[2025-05-30 10:23:18] [Pipeline] sh
[2025-05-30 10:23:18] + git checkout main
[2025-05-30 10:23:18] Already on 'main'
[2025-05-30 10:23:18] Your branch is up to date with 'origin/main'.
[2025-05-30 10:23:18] [Pipeline] }
[2025-05-30 10:23:18] [Pipeline] // stage
[2025-05-30 10:23:18] [Pipeline] stage
[2025-05-30 10:23:18] [Pipeline] { (写入 dockerfile)
[2025-05-30 10:23:18] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-30 10:23:18] [Pipeline] }
[2025-05-30 10:23:18] [Pipeline] // stage
[2025-05-30 10:23:18] [Pipeline] stage
[2025-05-30 10:23:18] [Pipeline] { (构建 Docker 镜像)
[2025-05-30 10:23:18] [Pipeline] sh
[2025-05-30 10:23:18] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-149-20250530102308
[2025-05-30 10:23:18] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-30 10:23:18] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-30 10:23:18] Configure a credential helper to remove this warning. See
[2025-05-30 10:23:18] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-30 10:23:18] 
[2025-05-30 10:23:18] Login Succeeded
[2025-05-30 10:23:18] [Pipeline] sh
[2025-05-30 10:23:19] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-149-20250530102308 backend
[2025-05-30 10:23:20] #0 building with "default" instance using docker driver
[2025-05-30 10:23:20] 
[2025-05-30 10:23:20] #1 [internal] load build definition from Dockerfile
[2025-05-30 10:23:20] #1 transferring dockerfile: 578B done
[2025-05-30 10:23:20] #1 DONE 0.1s
[2025-05-30 10:23:20] 
[2025-05-30 10:23:20] #2 [internal] load .dockerignore
[2025-05-30 10:23:20] #2 transferring context: 2B done
[2025-05-30 10:23:20] #2 DONE 0.1s
[2025-05-30 10:23:20] 
[2025-05-30 10:23:20] #3 [internal] load metadata for docker.io/library/node:18-alpine
[2025-05-30 10:23:20] #3 DONE 1.0s
[2025-05-30 10:23:20] 
[2025-05-30 10:23:20] #4 [internal] load build context
[2025-05-30 10:23:21] #4 transferring context: 35.48MB 0.2s done
[2025-05-30 10:23:21] #4 DONE 0.3s
[2025-05-30 10:23:21] 
[2025-05-30 10:23:21] #5 [1/7] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 10:23:21] #5 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 0.0s done
[2025-05-30 10:23:21] #5 sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 7.67kB / 7.67kB done
[2025-05-30 10:23:21] #5 sha256:929b04d7c782f04f615cf785488fed452b6569f87c73ff666ad553a7554f0006 1.72kB / 1.72kB done
[2025-05-30 10:23:21] #5 sha256:ee77c6cd7c1886ecc802ad6cedef3a8ec1ea27d1fb96162bf03dd3710839b8da 6.18kB / 6.18kB done
[2025-05-30 10:23:21] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0B / 3.64MB 0.2s
[2025-05-30 10:23:21] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0B / 40.01MB 0.2s
[2025-05-30 10:23:21] #5 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0B / 1.26MB 0.2s
[2025-05-30 10:23:21] #5 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 0B / 446B 0.2s
[2025-05-30 10:23:21] #5 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 446B / 446B 0.3s done
[2025-05-30 10:23:21] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 1.05MB / 3.64MB 0.5s
[2025-05-30 10:23:21] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 2.10MB / 3.64MB 0.6s
[2025-05-30 10:23:21] #5 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 1.26MB / 1.26MB 0.7s done
[2025-05-30 10:23:21] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.15MB / 3.64MB 0.9s
[2025-05-30 10:23:21] #5 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.64MB / 3.64MB 0.9s done
[2025-05-30 10:23:21] #5 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0.1s done
[2025-05-30 10:23:22] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 2.10MB / 40.01MB 1.3s
[2025-05-30 10:23:22] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 4.19MB / 40.01MB 1.9s
[2025-05-30 10:23:23] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 6.29MB / 40.01MB 2.4s
[2025-05-30 10:23:23] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 8.39MB / 40.01MB 2.9s
[2025-05-30 10:23:24] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 10.49MB / 40.01MB 3.4s
[2025-05-30 10:23:24] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 12.58MB / 40.01MB 3.8s
[2025-05-30 10:23:25] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 14.68MB / 40.01MB 4.2s
[2025-05-30 10:23:25] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 16.78MB / 40.01MB 4.5s
[2025-05-30 10:23:25] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 18.87MB / 40.01MB 4.9s
[2025-05-30 10:23:26] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 20.97MB / 40.01MB 5.2s
[2025-05-30 10:23:26] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 23.07MB / 40.01MB 5.5s
[2025-05-30 10:23:26] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 25.17MB / 40.01MB 5.8s
[2025-05-30 10:23:27] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 27.26MB / 40.01MB 6.1s
[2025-05-30 10:23:27] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 30.41MB / 40.01MB 6.4s
[2025-05-30 10:23:27] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 32.51MB / 40.01MB 6.7s
[2025-05-30 10:23:28] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 34.60MB / 40.01MB 7.0s
[2025-05-30 10:23:28] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 36.70MB / 40.01MB 7.2s
[2025-05-30 10:23:28] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 38.80MB / 40.01MB 7.4s
[2025-05-30 10:23:28] #5 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 7.6s done
[2025-05-30 10:23:28] #5 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e
[2025-05-30 10:23:29] #5 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 1.0s done
[2025-05-30 10:23:29] #5 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0.0s done
[2025-05-30 10:23:29] #5 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 done
[2025-05-30 10:23:30] #5 DONE 9.0s
[2025-05-30 10:23:30] 
[2025-05-30 10:23:30] #6 [2/7] RUN apk add --no-cache python3 make g++ gcc
[2025-05-30 10:23:30] #6 0.168 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
[2025-05-30 10:23:30] #6 0.439 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
[2025-05-30 10:23:30] #6 0.735 (1/29) Installing libstdc++-dev (14.2.0-r4)
[2025-05-30 10:23:31] #6 0.891 (2/29) Installing jansson (2.14-r4)
[2025-05-30 10:23:31] #6 0.924 (3/29) Installing zstd-libs (1.5.6-r2)
[2025-05-30 10:23:31] #6 0.963 (4/29) Installing binutils (2.43.1-r2)
[2025-05-30 10:23:31] #6 1.065 (5/29) Installing libgomp (14.2.0-r4)
[2025-05-30 10:23:31] #6 1.101 (6/29) Installing libatomic (14.2.0-r4)
[2025-05-30 10:23:31] #6 1.134 (7/29) Installing gmp (6.3.0-r2)
[2025-05-30 10:23:31] #6 1.170 (8/29) Installing isl26 (0.26-r1)
[2025-05-30 10:23:31] #6 1.218 (9/29) Installing mpfr4 (4.2.1-r0)
[2025-05-30 10:23:31] #6 1.256 (10/29) Installing mpc1 (1.3.1-r1)
[2025-05-30 10:23:31] #6 1.290 (11/29) Installing gcc (14.2.0-r4)
[2025-05-30 10:23:33] #6 2.960 (12/29) Installing musl-dev (1.2.5-r9)
[2025-05-30 10:23:33] #6 3.075 (13/29) Installing g++ (14.2.0-r4)
[2025-05-30 10:23:33] #6 3.458 (14/29) Installing make (4.4.1-r2)
[2025-05-30 10:23:33] #6 3.495 (15/29) Installing libbz2 (1.0.8-r6)
[2025-05-30 10:23:33] #6 3.528 (16/29) Installing libexpat (2.7.0-r0)
[2025-05-30 10:23:33] #6 3.562 (17/29) Installing libffi (3.4.7-r0)
[2025-05-30 10:23:33] #6 3.595 (18/29) Installing gdbm (1.24-r0)
[2025-05-30 10:23:33] #6 3.628 (19/29) Installing xz-libs (5.6.3-r1)
[2025-05-30 10:23:33] #6 3.663 (20/29) Installing mpdecimal (4.0.0-r0)
[2025-05-30 10:23:33] #6 3.698 (21/29) Installing ncurses-terminfo-base (6.5_p20241006-r3)
[2025-05-30 10:23:33] #6 3.732 (22/29) Installing libncursesw (6.5_p20241006-r3)
[2025-05-30 10:23:33] #6 3.770 (23/29) Installing libpanelw (6.5_p20241006-r3)
[2025-05-30 10:23:33] #6 3.802 (24/29) Installing readline (8.2.13-r0)
[2025-05-30 10:23:33] #6 3.838 (25/29) Installing sqlite-libs (3.48.0-r2)
[2025-05-30 10:23:33] #6 3.890 (26/29) Installing python3 (3.12.10-r1)
[2025-05-30 10:23:34] #6 4.122 (27/29) Installing python3-pycache-pyc0 (3.12.10-r1)
[2025-05-30 10:23:34] #6 4.255 (28/29) Installing pyc (3.12.10-r1)
[2025-05-30 10:23:34] #6 4.255 (29/29) Installing python3-pyc (3.12.10-r1)
[2025-05-30 10:23:34] #6 4.256 Executing busybox-1.37.0-r12.trigger
[2025-05-30 10:23:34] #6 4.261 OK: 269 MiB in 46 packages
[2025-05-30 10:23:37] #6 DONE 6.9s
[2025-05-30 10:23:37] 
[2025-05-30 10:23:37] #7 [3/7] WORKDIR /app
[2025-05-30 10:23:37] #7 DONE 0.0s
[2025-05-30 10:23:37] 
[2025-05-30 10:23:37] #8 [4/7] COPY package*.json ./
[2025-05-30 10:23:37] #8 DONE 0.0s
[2025-05-30 10:23:37] 
[2025-05-30 10:23:37] #9 [5/7] RUN npm config set registry https://registry.npmmirror.com
[2025-05-30 10:23:37] #9 DONE 0.6s
[2025-05-30 10:23:37] 
[2025-05-30 10:23:37] #10 [6/7] RUN npm install --production
[2025-05-30 10:23:38] #10 0.395 npm warn config production Use `--omit=dev` instead.
[2025-05-30 10:23:39] #10 2.323 npm warn deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-30 10:23:40] #10 2.693 npm warn deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-30 10:23:40] #10 2.782 npm warn deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-30 10:23:40] #10 3.236 
[2025-05-30 10:23:40] #10 3.236 added 221 packages in 3s
[2025-05-30 10:23:40] #10 3.237 
[2025-05-30 10:23:40] #10 3.237 31 packages are looking for funding
[2025-05-30 10:23:40] #10 3.237   run `npm fund` for details
[2025-05-30 10:23:41] #10 DONE 3.4s
[2025-05-30 10:23:41] 
[2025-05-30 10:23:41] #11 [7/7] COPY . .
[2025-05-30 10:23:41] #11 DONE 0.1s
[2025-05-30 10:23:41] 
[2025-05-30 10:23:41] #12 exporting to image
[2025-05-30 10:23:41] #12 exporting layers
[2025-05-30 10:23:42] #12 exporting layers 1.4s done
[2025-05-30 10:23:42] #12 writing image sha256:66b6d83737773e02816483b1830711d06735c0eab53974866b86467aefe759af done
[2025-05-30 10:23:42] #12 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-149-20250530102308 done
[2025-05-30 10:23:42] #12 DONE 1.4s
[2025-05-30 10:23:42] [Pipeline] sh
[2025-05-30 10:23:42] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-30 10:23:42] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-149-20250530102308
[2025-05-30 10:23:42] + awk {print $NF}
[2025-05-30 10:23:42] + echo 镜像的大小是：456MB
[2025-05-30 10:23:42] 镜像的大小是：456MB
[2025-05-30 10:23:42] [Pipeline] echo
[2025-05-30 10:23:42] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-30 10:23:42] [Pipeline] }
[2025-05-30 10:23:42] [Pipeline] // stage
[2025-05-30 10:23:42] [Pipeline] stage
[2025-05-30 10:23:42] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-30 10:23:43] [Pipeline] sh
[2025-05-30 10:23:43] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-149-20250530102308
[2025-05-30 10:23:43] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-30 10:23:43] 8900f9e0f9a4: Preparing
[2025-05-30 10:23:43] 1ed255f4fc14: Preparing
[2025-05-30 10:23:43] bb82b6318c71: Preparing
[2025-05-30 10:23:43] 93ef2fd5e5b9: Preparing
[2025-05-30 10:23:43] 3e5cbd6be297: Preparing
[2025-05-30 10:23:43] 381b96c226c9: Preparing
[2025-05-30 10:23:43] 82140d9a70a7: Preparing
[2025-05-30 10:23:43] f3b40b0cdb1c: Preparing
[2025-05-30 10:23:43] 0b1f26057bd0: Preparing
[2025-05-30 10:23:43] 08000c18d16d: Preparing
[2025-05-30 10:23:43] f3b40b0cdb1c: Layer already exists
[2025-05-30 10:23:43] 0b1f26057bd0: Layer already exists
[2025-05-30 10:23:43] 82140d9a70a7: Layer already exists
[2025-05-30 10:23:43] 08000c18d16d: Layer already exists
[2025-05-30 10:23:44] bb82b6318c71: Pushed
[2025-05-30 10:23:44] 3e5cbd6be297: Pushed
[2025-05-30 10:23:44] 93ef2fd5e5b9: Pushed
[2025-05-30 10:23:49] 1ed255f4fc14: Pushed
[2025-05-30 10:24:01] 8900f9e0f9a4: Pushed
[2025-05-30 10:24:33] 381b96c226c9: Pushed
[2025-05-30 10:24:33] lieyouqi-149-20250530102308: digest: sha256:faf555b9fb84bb1a159c82f4648cd9be2ea8aeda42f9ef6566685f00f25a276d size: 2416
[2025-05-30 10:24:33] [Pipeline] }
[2025-05-30 10:24:33] [Pipeline] // stage
[2025-05-30 10:24:33] [Pipeline] }
[2025-05-30 10:24:33] [Pipeline] // node
[2025-05-30 10:24:33] [Pipeline] End of Pipeline
[2025-05-30 10:24:33] Finished: SUCCESS
***
-----------构建lieyouqi-149-----------
2025-05-30 10:23:10 create_build_image : creating
2025-05-30 10:24:39 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-149-----------
2025-05-30 10:24:40 create_eks_virtual_service : creating
2025-05-30 10:24:40 check_eks_virtual_service : process, DescribeVersion_user_error_Back-off restarting failed container, [service]:[Back-off restarting failed container,]
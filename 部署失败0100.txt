[2025-05-30 01:00:56] Started by user coding
[2025-05-30 01:00:56] Running in Durability level: MAX_SURVIVABILITY
[2025-05-30 01:00:59] [Pipeline] Start of Pipeline
[2025-05-30 01:00:59] [Pipeline] node
[2025-05-30 01:00:59] Running on <PERSON> in /root/workspace
[2025-05-30 01:00:59] [Pipeline] {
[2025-05-30 01:00:59] [Pipeline] stage
[2025-05-30 01:00:59] [Pipeline] { (检出软件包)
[2025-05-30 01:01:00] Stage "检出软件包" skipped due to when conditional
[2025-05-30 01:01:00] [Pipeline] }
[2025-05-30 01:01:00] [Pipeline] // stage
[2025-05-30 01:01:00] [Pipeline] stage
[2025-05-30 01:01:00] [Pipeline] { (检出 ZIP 包)
[2025-05-30 01:01:00] Stage "检出 ZIP 包" skipped due to when conditional
[2025-05-30 01:01:00] [Pipeline] }
[2025-05-30 01:01:00] [Pipeline] // stage
[2025-05-30 01:01:00] [Pipeline] stage
[2025-05-30 01:01:00] [Pipeline] { (检出代码仓库)
[2025-05-30 01:01:00] [Pipeline] sh
[2025-05-30 01:01:00] + git clone ****** .
[2025-05-30 01:01:00] Cloning into '.'...
[2025-05-30 01:01:03] [Pipeline] sh
[2025-05-30 01:01:03] + git checkout main
[2025-05-30 01:01:03] Already on 'main'
[2025-05-30 01:01:03] Your branch is up to date with 'origin/main'.
[2025-05-30 01:01:03] [Pipeline] }
[2025-05-30 01:01:03] [Pipeline] // stage
[2025-05-30 01:01:03] [Pipeline] stage
[2025-05-30 01:01:03] [Pipeline] { (写入 dockerfile)
[2025-05-30 01:01:03] Stage "写入 dockerfile" skipped due to when conditional
[2025-05-30 01:01:03] [Pipeline] }
[2025-05-30 01:01:03] [Pipeline] // stage
[2025-05-30 01:01:03] [Pipeline] stage
[2025-05-30 01:01:03] [Pipeline] { (构建 Docker 镜像)
[2025-05-30 01:01:03] [Pipeline] sh
[2025-05-30 01:01:03] + docker login -u ****** -p ****** ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-148-20250530010053
[2025-05-30 01:01:03] WARNING! Using --password via the CLI is insecure. Use --password-stdin.
[2025-05-30 01:01:03] WARNING! Your password will be stored unencrypted in /root/.docker/config.json.
[2025-05-30 01:01:03] Configure a credential helper to remove this warning. See
[2025-05-30 01:01:03] https://docs.docker.com/engine/reference/commandline/login/#credentials-store
[2025-05-30 01:01:03] 
[2025-05-30 01:01:03] Login Succeeded
[2025-05-30 01:01:04] [Pipeline] sh
[2025-05-30 01:01:04] + docker build -f ./backend/Dockerfile -t ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-148-20250530010053 backend
[2025-05-30 01:01:05] #0 building with "default" instance using docker driver
[2025-05-30 01:01:05] 
[2025-05-30 01:01:05] #1 [internal] load .dockerignore
[2025-05-30 01:01:05] #1 transferring context: 2B done
[2025-05-30 01:01:05] #1 DONE 0.0s
[2025-05-30 01:01:05] 
[2025-05-30 01:01:05] #2 [internal] load build definition from Dockerfile
[2025-05-30 01:01:05] #2 transferring dockerfile: 674B done
[2025-05-30 01:01:05] #2 DONE 0.1s
[2025-05-30 01:01:05] 
[2025-05-30 01:01:05] #3 [internal] load metadata for docker.io/library/node:18-alpine
[2025-05-30 01:01:06] #3 DONE 1.2s
[2025-05-30 01:01:06] 
[2025-05-30 01:01:06] #4 [1/8] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 01:01:06] #4 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 0.0s done
[2025-05-30 01:01:06] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0B / 3.64MB 0.1s
[2025-05-30 01:01:06] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0B / 40.01MB 0.1s
[2025-05-30 01:01:06] #4 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0B / 1.26MB 0.1s
[2025-05-30 01:01:06] #4 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 0B / 446B 0.1s
[2025-05-30 01:01:06] #4 sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 7.67kB / 7.67kB done
[2025-05-30 01:01:06] #4 sha256:929b04d7c782f04f615cf785488fed452b6569f87c73ff666ad553a7554f0006 1.72kB / 1.72kB done
[2025-05-30 01:01:06] #4 sha256:ee77c6cd7c1886ecc802ad6cedef3a8ec1ea27d1fb96162bf03dd3710839b8da 6.18kB / 6.18kB done
[2025-05-30 01:01:06] #4 ...
[2025-05-30 01:01:06] 
[2025-05-30 01:01:06] #5 [internal] load build context
[2025-05-30 01:01:06] #5 transferring context: 35.48MB 0.3s done
[2025-05-30 01:01:06] #5 DONE 0.3s
[2025-05-30 01:01:06] 
[2025-05-30 01:01:06] #4 [1/8] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
[2025-05-30 01:01:06] #4 sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 446B / 446B 0.2s done
[2025-05-30 01:01:06] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 1.05MB / 3.64MB 0.5s
[2025-05-30 01:01:06] #4 sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 1.26MB / 1.26MB 0.4s done
[2025-05-30 01:01:07] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.15MB / 3.64MB 0.8s
[2025-05-30 01:01:07] #4 sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 3.64MB / 3.64MB 0.9s done
[2025-05-30 01:01:07] #4 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870
[2025-05-30 01:01:07] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 2.10MB / 40.01MB 1.1s
[2025-05-30 01:01:07] #4 extracting sha256:f18232174bc91741fdf3da96d85011092101a032a93a388b79e99e69c2d5c870 0.1s done
[2025-05-30 01:01:07] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 4.19MB / 40.01MB 1.6s
[2025-05-30 01:01:08] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 6.29MB / 40.01MB 2.1s
[2025-05-30 01:01:09] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 8.39MB / 40.01MB 2.6s
[2025-05-30 01:01:09] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 10.49MB / 40.01MB 3.1s
[2025-05-30 01:01:09] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 12.58MB / 40.01MB 3.4s
[2025-05-30 01:01:10] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 14.68MB / 40.01MB 3.8s
[2025-05-30 01:01:10] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 16.78MB / 40.01MB 4.2s
[2025-05-30 01:01:10] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 18.87MB / 40.01MB 4.5s
[2025-05-30 01:01:11] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 20.97MB / 40.01MB 4.9s
[2025-05-30 01:01:11] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 23.07MB / 40.01MB 5.2s
[2025-05-30 01:01:12] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 25.17MB / 40.01MB 5.5s
[2025-05-30 01:01:12] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 28.31MB / 40.01MB 5.9s
[2025-05-30 01:01:12] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 30.41MB / 40.01MB 6.3s
[2025-05-30 01:01:12] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 32.51MB / 40.01MB 6.5s
[2025-05-30 01:01:13] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 34.60MB / 40.01MB 6.8s
[2025-05-30 01:01:13] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 36.70MB / 40.01MB 7.0s
[2025-05-30 01:01:13] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 38.80MB / 40.01MB 7.3s
[2025-05-30 01:01:13] #4 sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 40.01MB / 40.01MB 7.4s done
[2025-05-30 01:01:13] #4 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 0.1s
[2025-05-30 01:01:15] #4 extracting sha256:dd71dde834b5c203d162902e6b8994cb2309ae049a0eabc4efea161b2b5a3d0e 1.4s done
[2025-05-30 01:01:15] #4 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3
[2025-05-30 01:01:15] #4 extracting sha256:1e5a4c89cee5c0826c540ab06d4b6b491c96eda01837f430bd47f0d26702d6e3 0.0s done
[2025-05-30 01:01:15] #4 extracting sha256:25ff2da83641908f65c3a74d80409d6b1b62ccfaab220b9ea70b80df5a2e0549 done
[2025-05-30 01:01:15] #4 DONE 9.1s
[2025-05-30 01:01:15] 
[2025-05-30 01:01:15] #6 [2/8] RUN apk add --no-cache python3 make g++ gcc ca-certificates
[2025-05-30 01:01:15] #6 0.211 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
[2025-05-30 01:01:16] #6 0.938 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
[2025-05-30 01:01:17] #6 1.588 (1/30) Installing ca-certificates (20241121-r1)
[2025-05-30 01:01:17] #6 1.634 (2/30) Installing libstdc++-dev (14.2.0-r4)
[2025-05-30 01:01:17] #6 1.856 (3/30) Installing jansson (2.14-r4)
[2025-05-30 01:01:17] #6 1.888 (4/30) Installing zstd-libs (1.5.6-r2)
[2025-05-30 01:01:17] #6 1.929 (5/30) Installing binutils (2.43.1-r2)
[2025-05-30 01:01:17] #6 2.058 (6/30) Installing libgomp (14.2.0-r4)
[2025-05-30 01:01:17] #6 2.095 (7/30) Installing libatomic (14.2.0-r4)
[2025-05-30 01:01:17] #6 2.127 (8/30) Installing gmp (6.3.0-r2)
[2025-05-30 01:01:17] #6 2.166 (9/30) Installing isl26 (0.26-r1)
[2025-05-30 01:01:17] #6 2.221 (10/30) Installing mpfr4 (4.2.1-r0)
[2025-05-30 01:01:17] #6 2.261 (11/30) Installing mpc1 (1.3.1-r1)
[2025-05-30 01:01:17] #6 2.294 (12/30) Installing gcc (14.2.0-r4)
[2025-05-30 01:01:19] #6 3.660 (13/30) Installing musl-dev (1.2.5-r9)
[2025-05-30 01:01:19] #6 3.781 (14/30) Installing g++ (14.2.0-r4)
[2025-05-30 01:01:20] #6 4.442 (15/30) Installing make (4.4.1-r2)
[2025-05-30 01:01:20] #6 4.479 (16/30) Installing libbz2 (1.0.8-r6)
[2025-05-30 01:01:20] #6 4.513 (17/30) Installing libexpat (2.7.0-r0)
[2025-05-30 01:01:20] #6 4.548 (18/30) Installing libffi (3.4.7-r0)
[2025-05-30 01:01:20] #6 4.581 (19/30) Installing gdbm (1.24-r0)
[2025-05-30 01:01:20] #6 4.614 (20/30) Installing xz-libs (5.6.3-r1)
[2025-05-30 01:01:20] #6 4.650 (21/30) Installing mpdecimal (4.0.0-r0)
[2025-05-30 01:01:20] #6 4.686 (22/30) Installing ncurses-terminfo-base (6.5_p20241006-r3)
[2025-05-30 01:01:20] #6 4.723 (23/30) Installing libncursesw (6.5_p20241006-r3)
[2025-05-30 01:01:20] #6 4.761 (24/30) Installing libpanelw (6.5_p20241006-r3)
[2025-05-30 01:01:20] #6 4.793 (25/30) Installing readline (8.2.13-r0)
[2025-05-30 01:01:20] #6 4.830 (26/30) Installing sqlite-libs (3.48.0-r2)
[2025-05-30 01:01:20] #6 4.885 (27/30) Installing python3 (3.12.10-r1)
[2025-05-30 01:01:20] #6 5.169 (28/30) Installing python3-pycache-pyc0 (3.12.10-r1)
[2025-05-30 01:01:20] #6 5.353 (29/30) Installing pyc (3.12.10-r1)
[2025-05-30 01:01:20] #6 5.353 (30/30) Installing python3-pyc (3.12.10-r1)
[2025-05-30 01:01:20] #6 5.354 Executing busybox-1.37.0-r12.trigger
[2025-05-30 01:01:20] #6 5.359 Executing ca-certificates-20241121-r1.trigger
[2025-05-30 01:01:20] #6 5.401 OK: 270 MiB in 47 packages
[2025-05-30 01:01:23] #6 DONE 7.6s
[2025-05-30 01:01:23] 
[2025-05-30 01:01:23] #7 [3/8] WORKDIR /app
[2025-05-30 01:01:23] #7 DONE 0.0s
[2025-05-30 01:01:23] 
[2025-05-30 01:01:23] #8 [4/8] COPY package*.json ./
[2025-05-30 01:01:23] #8 DONE 0.0s
[2025-05-30 01:01:23] 
[2025-05-30 01:01:23] #9 [5/8] RUN npm config set registry https://registry.npmmirror.com
[2025-05-30 01:01:23] #9 DONE 0.8s
[2025-05-30 01:01:23] 
[2025-05-30 01:01:23] #10 [6/8] RUN npm install --production
[2025-05-30 01:01:24] #10 0.467 npm warn config production Use `--omit=dev` instead.
[2025-05-30 01:01:27] #10 3.321 npm warn deprecated har-validator@5.1.5: this library is no longer supported
[2025-05-30 01:01:27] #10 3.550 npm warn deprecated request@2.88.2: request has been deprecated, see https://github.com/request/request/issues/3142
[2025-05-30 01:01:27] #10 3.590 npm warn deprecated uuid@3.4.0: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
[2025-05-30 01:01:28] #10 4.212 
[2025-05-30 01:01:28] #10 4.212 added 221 packages in 4s
[2025-05-30 01:01:28] #10 4.212 
[2025-05-30 01:01:28] #10 4.212 31 packages are looking for funding
[2025-05-30 01:01:28] #10 4.212   run `npm fund` for details
[2025-05-30 01:01:28] #10 DONE 4.3s
[2025-05-30 01:01:28] 
[2025-05-30 01:01:28] #11 [7/8] COPY . .
[2025-05-30 01:01:28] #11 DONE 0.1s
[2025-05-30 01:01:28] 
[2025-05-30 01:01:28] #12 [8/8] RUN chmod +x /app/cert/initenv.sh
[2025-05-30 01:01:28] #12 DONE 0.3s
[2025-05-30 01:01:28] 
[2025-05-30 01:01:28] #13 exporting to image
[2025-05-30 01:01:28] #13 exporting layers
[2025-05-30 01:01:30] #13 exporting layers 1.7s done
[2025-05-30 01:01:30] #13 writing image sha256:b9c4f11156185e4e49da918bd376bf12c270ea4d0c0c0ab46f1ccf0a61fcece7 done
[2025-05-30 01:01:30] #13 naming to ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-148-20250530010053 done
[2025-05-30 01:01:30] #13 DONE 1.7s
[2025-05-30 01:01:30] [Pipeline] sh
[2025-05-30 01:01:30] + docker image ls --format {{.Repository}}:{{.Tag}} {{.Size}}
[2025-05-30 01:01:30] + grep ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-148-20250530010053
[2025-05-30 01:01:30] + awk {print $NF}
[2025-05-30 01:01:30] + echo 镜像的大小是：457MB
[2025-05-30 01:01:30] 镜像的大小是：457MB
[2025-05-30 01:01:30] [Pipeline] echo
[2025-05-30 01:01:30] 优化镜像大小具体可参考： https://docs.cloudbase.net/run/develop/image-optimization
[2025-05-30 01:01:30] [Pipeline] }
[2025-05-30 01:01:30] [Pipeline] // stage
[2025-05-30 01:01:30] [Pipeline] stage
[2025-05-30 01:01:30] [Pipeline] { (推送 Docker 镜像到 TCR)
[2025-05-30 01:01:31] [Pipeline] sh
[2025-05-30 01:01:31] + docker push ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi:lieyouqi-148-20250530010053
[2025-05-30 01:01:31] The push refers to repository [ccr.ccs.tencentyun.com/tcb-******-buta/ca-gdfyaqdd_lieyouqi]
[2025-05-30 01:01:31] c130da6d23bb: Preparing
[2025-05-30 01:01:31] f60c4209b753: Preparing
[2025-05-30 01:01:31] ee09ed72aaad: Preparing
[2025-05-30 01:01:31] e616540f06f6: Preparing
[2025-05-30 01:01:31] 8e854f8f93b9: Preparing
[2025-05-30 01:01:31] 74f1eda5628d: Preparing
[2025-05-30 01:01:31] 7f8d0a457c3e: Preparing
[2025-05-30 01:01:31] 82140d9a70a7: Preparing
[2025-05-30 01:01:31] f3b40b0cdb1c: Preparing
[2025-05-30 01:01:31] 0b1f26057bd0: Preparing
[2025-05-30 01:01:31] 08000c18d16d: Preparing
[2025-05-30 01:01:31] 08000c18d16d: Waiting
[2025-05-30 01:01:31] f3b40b0cdb1c: Layer already exists
[2025-05-30 01:01:31] 82140d9a70a7: Layer already exists
[2025-05-30 01:01:31] 0b1f26057bd0: Layer already exists
[2025-05-30 01:01:31] 08000c18d16d: Layer already exists
[2025-05-30 01:01:32] 74f1eda5628d: Pushed
[2025-05-30 01:01:32] c130da6d23bb: Pushed
[2025-05-30 01:01:32] e616540f06f6: Pushed
[2025-05-30 01:01:32] 8e854f8f93b9: Pushed
[2025-05-30 01:01:39] ee09ed72aaad: Pushed
[2025-05-30 01:01:49] f60c4209b753: Pushed
[2025-05-30 01:02:20] 7f8d0a457c3e: Pushed
[2025-05-30 01:02:20] lieyouqi-148-20250530010053: digest: sha256:5a323258c59dbc149b9ed8dec82c18d222355d296c8ff261c9682151ee7baafc size: 2623
[2025-05-30 01:02:21] [Pipeline] }
[2025-05-30 01:02:21] [Pipeline] // stage
[2025-05-30 01:02:21] [Pipeline] }
[2025-05-30 01:02:21] [Pipeline] // node
[2025-05-30 01:02:21] [Pipeline] End of Pipeline
[2025-05-30 01:02:21] Finished: SUCCESS
***
-----------构建lieyouqi-148-----------
2025-05-30 01:00:55 create_build_image : creating
2025-05-30 01:02:23 check_build_image : succ
-----------服务lieyouqi部署lieyouqi-148-----------
2025-05-30 01:02:25 create_eks_virtual_service : creating
2025-05-30 01:02:25 check_eks_virtual_service : process, DescribeVersion_user_error_Back-off restarting failed container
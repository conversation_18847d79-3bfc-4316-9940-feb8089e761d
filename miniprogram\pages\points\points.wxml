<!--pages/points/points.wxml-->
<view class="points-center-container">
  <!-- 用户信息区 -->
  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
    <view class="user-details">
      <view class="nickname">{{userInfo.nickName || '用户昵称'}}</view>
      <view class="user-id">ID: {{userInfo.id || 'XXXXXXXXXX'}}</view>
    </view>
    <view class="points-block">
      <view class="points-label">当前积分</view>
      <view class="points-value">{{userInfo.points || 0}}</view>
    </view>
  </view>
  <!-- 轮播图区 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#1E6A9E">
      <block wx:for="{{banners}}" wx:key="index">
        <swiper-item>
          <image class="banner-image" src="{{item}}" mode="aspectFill"></image>
          <view class="banner-title" wx:if="{{bannerTitles[index]}}">{{bannerTitles[index]}}</view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 规则与方式区 -->
  <view class="rules-tabs">
    <view class="tab-btn {{activeTab === 'consume' ? 'active' : ''}}" data-tab="consume" bindtap="onTabChange">积分消耗规则</view>
    <view class="tab-btn {{activeTab === 'gain' ? 'active' : ''}}" data-tab="gain" bindtap="onTabChange">积分获取方式</view>
  </view>
  <view class="rules-card">
    <view wx:if="{{activeTab === 'consume'}}">{{consumeRule}}</view>
    <view wx:if="{{activeTab === 'gain'}}">{{gainWays}}</view>
  </view>
  <!-- 积分记录区 -->
  <view class="points-record-section">
    <view class="record-title">积分记录</view>
    <block wx:if="{{loadingRecords}}">
      <view class="loading-records">
        <view class="loading-icon"></view>
        <text>加载中...</text>
      </view>
    </block>
    <block wx:elif="{{pointsRecords && pointsRecords.length > 0}}">
      <block wx:for="{{pointsRecords}}" wx:key="id">
        <view class="record-item">
          <view class="record-row">
            <view class="record-time">{{item.time}}</view>
            <view class="record-event">{{item.event}}</view>
            <view class="record-change {{item.change < 0 ? 'minus' : 'plus'}}">{{item.change > 0 ? '+' : ''}}{{item.change}}</view>
          </view>
          <view class="record-balance">当前剩余：{{item.balance}}</view>
        </view>
      </block>
    </block>
    <block wx:else>
      <view class="record-empty">暂无积分记录</view>
    </block>
  </view>
</view>

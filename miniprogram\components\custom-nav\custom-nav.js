// components/custom-nav/custom-nav.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showSettings: {
      type: Boolean,
      value: false
    },
    bgColor: {
      type: String,
      value: '#FFFFFF'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    navBarHeight: 44,
    menuButtonInfo: null
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      
      // 获取胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight,
        menuButtonInfo: menuButtonInfo
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 返回上一页
    onBack: function() {
      wx.navigateBack({
        fail: () => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }
      });
    },
    
    // 点击设置按钮
    onSettings: function() {
      this.triggerEvent('settings');
    }
  }
})

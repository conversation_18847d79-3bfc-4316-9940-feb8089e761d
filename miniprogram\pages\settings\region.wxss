/* pages/settings/region.wxss */
.region-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.region-content {
  padding: 30rpx;
}

.region-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 30rpx;
  font-weight: 500;
}

.region-picker-container {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  position: relative;
}

.picker-label {
  color: #666666;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.picker-value {
  flex: 1;
  color: #333333;
  font-size: 28rpx;
}

.arrow-right {
  color: #cccccc;
  font-size: 28rpx;
}

.region-tips {
  font-size: 24rpx;
  color: #999999;
  margin-top: 20rpx;
  line-height: 1.5;
}

.save-button {
  background-color: #07c160;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s ease;
}

.save-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(7, 193, 96, 0.2);
}

.save-button.disabled {
  background-color: #9ed29e;
  box-shadow: none;
}

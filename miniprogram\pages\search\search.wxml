<!--pages/search/search.wxml-->
<view class="search-container">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-input-container">
      <input class="search-input" placeholder="{{placeholder || '搜索感兴趣的内容'}}" placeholder-class="search-placeholder"
        bindinput="onSearchInput" value="{{keyword}}" focus="{{true}}" confirm-type="search" bindconfirm="onSearch" />
      <view class="search-btn" bindtap="onSearch">
        <image src="/images/icons2/搜索.png"></image>
        <text>搜索</text>
      </view>
    </view>
    <view class="clear-btn" bindtap="clearSearch">清除</view>
  </view>

  <!-- 搜索历史 -->
  <view class="search-history" wx:if="{{!keyword && searchHistory.length > 0 && !showResults}}">
    <view class="history-header">
      <text class="history-title">搜索历史</text>
      <view class="clear-history" bindtap="clearHistory">
        <text class="delete-icon">🗑️</text>
      </view>
    </view>
    <view class="history-list">
      <view class="history-item" wx:for="{{searchHistory}}" wx:key="*this" bindtap="onHistoryTap" data-keyword="{{item}}">
        <text class="history-icon">⏱️</text>
        <text class="history-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showResults}}">
    <!-- 帖子搜索结果 -->
    <block wx:if="{{searchType === 'post'}}">
      <view class="result-count" wx:if="{{searchResults.length > 0}}">找到 {{searchResults.length}} 条相关内容</view>

      <!-- 结果列表 -->
      <view class="post-list">
        <view class="post-item card" wx:for="{{searchResults}}" wx:key="_id" bindtap="onPostTap" data-id="{{item._id}}">
          <!-- 用户信息栏 -->
          <view class="post-user">
            <image class="avatar" src="{{item.userInfo.avatarUrl}}" catchtap="onUserTap" data-id="{{item.userId}}"></image>
            <view class="user-info">
              <view class="username">{{item.userInfo.nickName}}</view>
              <view class="post-time">{{item.createTime}}</view>
            </view>
            <view class="follow-btn" wx:if="{{!item.isFollowed}}">关注</view>
          </view>

          <!-- 内容区 -->
          <view class="post-content">
            <text class="content-text">{{item.content}}</text>

            <!-- 单图模式 -->
            <image wx:if="{{item.images.length === 1}}" class="single-image" src="{{item.images[0]}}" mode="widthFix"></image>

            <!-- 多图模式 -->
            <view class="image-grid" wx:if="{{item.images.length > 1}}">
              <image wx:for="{{item.images}}" wx:for-item="image" wx:key="*this" wx:for-index="imgIndex"
                    src="{{image}}"
                    class="grid-image"
                    mode="aspectFill"
                    wx:if="{{imgIndex < 9}}"></image>
            </view>
          </view>

          <!-- 商品关联区 -->
          <view class="product-link" wx:if="{{item.product}}">
            <image class="product-image" src="{{item.product.imageUrl}}"></image>
            <view class="product-info">
              <view class="product-name">{{item.product.name}}</view>
              <view class="product-price">¥{{item.product.price}}</view>
            </view>
            <view class="buy-btn">购买</view>
          </view>

          <!-- 互动栏 -->
          <view class="interaction-bar">
            <view class="interaction-item" catchtap="onLikeTap" data-index="{{index}}">
              <image src="{{item.isLiked ? '/images/icons2/已点赞.png' : '/images/icons2/未点赞.png'}}"></image>
              <text>({{item.likeCount || 0}})</text>
            </view>
            <view class="interaction-item" catchtap="onCommentTap" data-id="{{item._id}}">
              <image src="/images/icons2/评论.png"></image>
              <text>({{item.commentCount || 0}})</text>
            </view>
            <view class="interaction-item" catchtap="onShareTap" data-index="{{index}}">
              <image src="/images/icons2/分享.png"></image>
              <text>转发 ({{item.shareCount || 0}})</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空结果 -->
      <view class="empty-container" wx:if="{{searchResults.length === 0 && !loading}}">
        <text class="empty-icon">🔍</text>
        <text class="empty-text">没有找到相关内容</text>
      </view>
    </block>

    <!-- 商品搜索结果 -->
    <block wx:if="{{searchType === 'product'}}">
      <view class="result-count" wx:if="{{productResults.length > 0}}">找到 {{productResults.length}} 个相关商品</view>

      <!-- 商品列表 -->
      <view class="product-list">
        <view class="product-item" wx:for="{{productResults}}" wx:key="_id" bindtap="onProductTap" data-id="{{item._id || item.id}}">
          <image class="product-image" src="{{item.images[0]}}" mode="aspectFill"></image>
          <view class="product-info">
            <view class="product-name">{{item.name}}</view>
            <view class="product-price-row">
              <view class="product-price">¥{{item.price}}</view>
              <view class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
            </view>
            <view class="product-shop">{{item.shopName}}</view>
            <view class="product-sales">已售 {{item.salesCount || 0}}</view>
          </view>
        </view>
      </view>

      <!-- 空结果 -->
      <view class="empty-container" wx:if="{{productResults.length === 0 && !loading}}">
        <text class="empty-icon">🔍</text>
        <text class="empty-text">没有找到相关商品</text>
      </view>
    </block>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">搜索中...</text>
    </view>
  </view>
</view>

/* pages/shop/shop.wxss */
.shop-container {
  min-height: 100vh;
  background-color: #F7F7F7;
  padding-bottom: 20px;
}

/* 自定义导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  z-index: 1000;
}

.status-bar {
  width: 100%;
}

.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 0 0 15px; /* 左侧添加内边距 */
  justify-content: flex-start; /* 左对齐 */
}

.search-input-container {
  height: 32px;
  background-color: #F5F5F5;
  border-radius: 16px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 14px;
  padding: 0 36px 0 12px; /* 右侧留出空间给搜索按钮 */
}

.search-placeholder {
  font-size: 14px;
  color: #999999;
}

.search-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 4px;
}

.search-btn image {
  width: 20px;
  height: 20px;
}

/* 导航栏占位 */
.nav-placeholder {
  width: 100%;
}

/* 轮播图 */
.banner {
  height: 150px;
  margin: 0 16px 12px;
  border-radius: 12px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  font-size: 14px;
  font-weight: bold;
}

.debug-info {
  padding: 10rpx;
  margin: 0 16px;
  background-color: #ffeeee;
  color: #ff0000;
  font-size: 24rpx;
  text-align: center;
  border-radius: 8rpx;
}

/* 浮动购物车图标 */
.float-cart-icon {
  position: fixed;
  right: 15px;
  bottom: 30px; /* 距离底部导航栏30像素 */
  width: 54px; /* 保持尺寸 */
  height: 54px; /* 保持尺寸 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 99;
  transition: all 0.2s ease;
  background-color: rgba(255, 255, 255, 0.95); /* 白色背景 */
  border-radius: 50%; /* 圆形边框 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12); /* 轻微阴影效果 */
  border: 1px solid rgba(0, 0, 0, 0.05); /* 添加细边框 */
  padding-top: 2px; /* 调整内部布局，使图标稍微上移 */
}

.float-cart-icon:active, .float-cart-icon-hover {
  transform: scale(0.95);
  opacity: 1;
  background-color: rgba(245, 245, 245, 0.95); /* 轻微灰色背景 */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.cart-text {
  font-size: 8px; /* 保持字体尺寸为8px */
  color: #333333; /* 改为黑色 */
  font-weight: bold;
  line-height: 1;
  text-align: center;
  margin-top: 1px; /* 保持与图标的间距 */
}

.float-cart-icon image {
  width: 34px; /* 进一步增大图标尺寸 */
  height: 34px; /* 进一步增大图标尺寸 */
  margin-bottom: 0px;
  filter: invert(20%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%); /* 改为黑色，与文字匹配 */
}

.badge-count {
  position: absolute;
  top: 0px;
  right: 0px;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 9px;
  font-size: 11px;
  font-weight: bold;
  padding: 0 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 分类导航 */
.category-container {
  background-color: #FFFFFF;
  position: sticky;
  z-index: 99;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 添加阴影效果 */
}

.category-scroll {
  height: 40px;
  white-space: nowrap;
  border-bottom: 1px solid #F5F5F5; /* 添加底部边框 */
  margin: 0 20px; /* 增加左右外边距，确保与屏幕边缘有更大距离 */
  width: calc(100% - 40px); /* 调整宽度，考虑左右外边距 */
}

.category-item {
  position: relative;
  display: inline-block;
  height: 40px;
  line-height: 40px;
  padding: 0 15px; /* 减小左右内边距，缩小卡片宽度 */
  font-size: 15px; /* 稍微减小字体大小 */
  color: #666666;
  transition: all 0.3s ease;
  border-right: 1px solid #EEEEEE; /* 添加右侧分界线 */
}

.category-item.active {
  color: #333333;
  font-weight: bold;
}

.category-item.last-item {
  border-right: none; /* 最后一个分类项不显示分界线 */
  padding-right: 0; /* 最后一个分类项右侧不需要内边距，因为已经有滚动区域的内边距 */
}

.category-item.first-item {
  padding-left: 0; /* 第一个分类项左侧不需要内边距，因为已经有滚动区域的内边距 */
}

.category-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px; /* 减小底部指示线宽度，与卡片宽度保持协调 */
  height: 3px;
  background-color: #FF4D4F;
  border-radius: 1.5px;
  transition: all 0.3s ease;
}

.subcategory-scroll {
  height: 36px; /* 减小高度，使其更加紧凑 */
  white-space: nowrap;
  border-top: 1px solid #F5F5F5;
  margin: 0 20px; /* 增加左右外边距，与一级分类保持一致 */
  width: calc(100% - 40px); /* 调整宽度，考虑左右外边距 */
}

.subcategory-item {
  display: inline-block;
  height: 24px; /* 减小高度 */
  line-height: 24px; /* 减小行高 */
  padding: 0 12px; /* 减小内边距 */
  margin: 8px 6px; /* 调整外边距 */
  font-size: 12px; /* 减小字体大小 */
  color: #666666;
  background-color: #F5F5F5;
  border-radius: 12px; /* 减小圆角 */
  transition: all 0.3s ease;
}

.subcategory-item.active {
  color: #FFFFFF;
  background-color: #FF4D4F;
  box-shadow: 0 1px 3px rgba(255, 77, 79, 0.2); /* 减小阴影 */
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  height: 40px;
  background-color: #FFFFFF;
  border-top: 1px solid #F5F5F5;
  position: sticky;
  z-index: 98;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666666;
}

.filter-item.active {
  color: #FF4D4F;
}

.sort-icon {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}

.sort-icon image {
  width: 10px;
  height: 10px;
  opacity: 0.5;
}

.sort-icon image.active {
  opacity: 1;
}

.filter-icon {
  width: 14px;
  height: 14px;
  margin-left: 4px;
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-wrap: wrap;
  padding: 12px;
}

.product-item {
  width: calc((100% - 12px) / 2);
  margin-bottom: 12px;
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-item:nth-child(odd) {
  margin-right: 12px;
}

.product-image {
  width: 100%;
  height: 160px; /* 使用固定高度代替 padding-bottom */
  object-fit: cover; /* 确保图片填充整个容器 */
}

.product-info {
  padding: 8px;
  position: relative;
}

.product-name {
  font-size: 14px;
  color: #333333;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  height: 40px;
}

.product-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.product-price {
  font-size: 16px;
  color: #FF4D4F;
  font-weight: bold;
}

.product-original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
  margin-left: 6px;
}

.product-sales {
  font-size: 12px;
  color: #999999;
  margin-bottom: 4px;
}

.product-shop {
  font-size: 12px;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.product-btns {
  position: absolute;
  right: 8px;
  bottom: 8px;
  display: flex;
  align-items: center;
  z-index: 2; /* 确保按钮不会被其他元素遮挡 */
  pointer-events: auto; /* 确保点击事件能够被捕获 */
}

.add-btn, .cart-btn {
  width: 36px; /* 稍微增加按钮尺寸，增加点击区域 */
  height: 36px; /* 稍微增加按钮尺寸，增加点击区域 */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  position: relative;
  padding: 0;
  border: none;
  background-color: rgba(255, 255, 255, 0.9); /* 增加背景色不透明度 */
  border-radius: 50%; /* 圆形按钮 */
  line-height: 1;
  font-size: inherit;
  color: inherit;
  text-align: inherit;
  outline: none;
  cursor: pointer; /* 添加指针样式 */
  touch-action: manipulation; /* 优化触摸操作 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  transition: all 0.2s ease; /* 添加过渡效果 */
}

/* 添加点击状态样式 */
.add-btn:active, .cart-btn:active, .btn-hover {
  opacity: 0.7;
  transform: scale(0.9);
  background-color: rgba(240, 240, 240, 0.9);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.add-btn image, .cart-btn image {
  width: 24px;
  height: 24px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 筛选面板 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 80%;
  background-color: #FFFFFF;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

.filter-panel.show {
  transform: translateX(0);
}

.filter-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.filter-panel-close {
  width: 24px;
  height: 24px;
}

.filter-panel-close image {
  width: 100%;
  height: 100%;
}

.filter-section {
  padding: 16px;
  border-bottom: 1px solid #F5F5F5;
}

.filter-section-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.price-range {
  display: flex;
  align-items: center;
}

.price-input {
  flex: 1;
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
}

.price-separator {
  margin: 0 8px;
  color: #999999;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
}

.filter-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.filter-checkbox image {
  width: 100%;
  height: 100%;
}

.filter-option text {
  font-size: 14px;
  color: #666666;
}

.filter-option.active text {
  color: #FF4D4F;
}

.filter-panel-footer {
  display: flex;
  padding: 16px;
  margin-top: auto;
}

.filter-reset-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #F5F5F5;
  color: #666666;
  border-radius: 22px;
  margin-right: 12px;
}

.filter-apply-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 22px;
}

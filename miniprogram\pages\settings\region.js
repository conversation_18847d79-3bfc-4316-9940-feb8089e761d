// pages/settings/region.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    region: ['请选择省份', '请选择城市'],
    userInfo: null,
    loading: false
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    // 先从全局获取
    const app = getApp();
    if (app.globalData.userInfo) {
      const userInfo = app.globalData.userInfo;
      this.setData({
        userInfo: userInfo,
        region: [
          userInfo.province || '请选择省份',
          userInfo.city || '请选择城市'
        ]
      });
      return;
    }

    // 如果全局没有，从本地存储获取
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        region: [
          userInfo.province || '请选择省份',
          userInfo.city || '请选择城市'
        ]
      });
    } else {
      // 如果本地存储也没有，尝试从服务器获取
      this.fetchUserInfo();
    }
  },

  // 从服务器获取用户信息
  fetchUserInfo: function() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    userApi.getUserInfo(userInfo.id)
      .then(res => {
        if (res.success) {
          const app = getApp();
          app.globalData.userInfo = res.data;
          
          this.setData({
            userInfo: res.data,
            region: [
              res.data.province || '请选择省份',
              res.data.city || '请选择城市'
            ]
          });
        } else {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      });
  },

  // 地区选择器变化事件
  bindRegionChange: function(e) {
    this.setData({
      region: e.detail.value
    });
  },

  // 保存地区设置
  saveRegion: function() {
    if (this.data.loading) return;
    
    const { region } = this.data;
    
    // 检查是否选择了省份和城市
    if (region[0] === '请选择省份' || region[1] === '请选择城市') {
      wx.showToast({
        title: '请选择省份和城市',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    // 更新用户信息
    userApi.updateUserInfo({
      province: region[0],
      city: region[1]
    })
      .then(res => {
        if (res.success) {
          // 更新本地存储的用户信息
          const userInfo = wx.getStorageSync('userInfo');
          userInfo.province = region[0];
          userInfo.city = region[1];
          wx.setStorageSync('userInfo', userInfo);
          
          // 更新全局用户信息
          const app = getApp();
          if (app.globalData.userInfo) {
            app.globalData.userInfo.province = region[0];
            app.globalData.userInfo.city = region[1];
          }
          
          // 标记需要刷新个人信息
          app.globalData.needRefreshProfile = true;
          
          wx.showToast({
            title: '地区设置成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.message || '地区设置失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('保存地区设置失败:', err);
        wx.showToast({
          title: '地区设置失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }
})

require('dotenv').config();
const mysql = require('mysql2/promise');

// 微信云托管环境变量优先
const dbHost = process.env.MYSQL_IP || process.env.DB_HOST || 'localhost';
const dbPort = process.env.MYSQL_PORT || 3306;
const dbUser = process.env.MYSQL_USERNAME || process.env.DB_USER || 'root';
const dbName = process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi';
const dbPassword = (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '');

console.log('数据库配置:');
console.log('- 主机:', dbHost);
console.log('- 端口:', dbPort);
console.log('- 用户:', dbUser);
console.log('- 数据库:', dbName);
console.log('- 密码长度:', dbPassword.length);

const pool = mysql.createPool({
  host: dbHost,
  port: dbPort,
  user: dbUser,
  password: dbPassword,
  database: dbName,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 30000, // 连接超时时间设置为30秒
  acquireTimeout: 30000, // 获取连接超时时间设置为30秒
  timeout: 30000, // 查询超时时间设置为30秒
  enableKeepAlive: true, // 启用keepalive
  keepAliveInitialDelay: 10000, // keepalive初始延迟10秒
  reconnect: true, // 启用自动重连
  idleTimeout: 300000, // 空闲连接超时时间5分钟
  maxIdle: 5 // 最大空闲连接数
});

// 延迟测试数据库连接，避免阻塞应用启动
setTimeout(async () => {
  let retryCount = 0;
  const maxRetries = 5;

  while (retryCount < maxRetries) {
    try {
      const connection = await pool.getConnection();
      console.log('数据库连接成功!');

      // 测试查询messages表
      try {
        const [rows] = await connection.execute('SELECT COUNT(*) as count FROM messages');
        console.log('消息表中有', rows[0].count, '条记录');
      } catch (err) {
        console.warn('查询消息表失败:', err.message);
      }

      connection.release();
      break; // 连接成功，退出重试循环
    } catch (err) {
      retryCount++;
      console.warn(`数据库连接失败 (尝试 ${retryCount}/${maxRetries}):`, err.message);

      if (retryCount < maxRetries) {
        console.log(`${5}秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      } else {
        console.error('数据库连接最终失败，应用将在运行时重试');
      }
    }
  }
}, 2000); // 延迟2秒开始测试

module.exports = {
  query: async (sql, params = [], retryCount = 0) => {
    const maxRetries = 3;

    try {
      console.log('执行SQL:', sql);
      console.log('参数:', params);

      // 确保参数是数组
      const paramArray = Array.isArray(params) ? params : [];

      // 确保参数类型正确
      const safeParams = paramArray.map(param => {
        if (param === undefined || param === null) {
          return null;
        }

        // 如果是数字字符串，转换为数字
        if (typeof param === 'string' && !isNaN(param) && param.trim() !== '') {
          if (param.includes('.')) {
            return parseFloat(param);
          } else {
            return parseInt(param, 10);
          }
        }

        return param;
      });

      console.log('安全参数:', safeParams);

      try {
        // 尝试使用 execute 方法
        const [rows] = await pool.execute(sql, safeParams);
        console.log('查询结果:', rows ? `返回${rows.length}行数据` : '无数据');
        return rows;
      } catch (executeError) {
        console.error('execute 方法失败:', executeError.message);
        console.log('尝试使用 query 方法...');

        // 如果 execute 失败，尝试使用 query 方法
        const [rows] = await pool.query(sql, safeParams);
        console.log('查询结果 (query方法):', rows ? `返回${rows.length}行数据` : '无数据');
        return rows;
      }
    } catch (error) {
      // 检查是否是连接错误且可以重试
      const isConnectionError = error.code === 'ECONNRESET' ||
                               error.code === 'ENOTFOUND' ||
                               error.code === 'ETIMEDOUT' ||
                               error.code === 'ECONNREFUSED';

      if (isConnectionError && retryCount < maxRetries) {
        console.warn(`数据库查询失败，正在重试 (${retryCount + 1}/${maxRetries}):`, error.message);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
        return module.exports.query(sql, params, retryCount + 1);
      }

      console.error('数据库查询错误:', error.message);
      console.error('SQL:', sql);
      console.error('参数:', params);
      throw error;
    }
  }
};
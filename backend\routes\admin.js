/**
 * 管理员路由
 */
const express = require('express');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const db = require('../config/db');
const { checkAuth } = require('../middleware/auth');

const router = express.Router();

// 微信云托管环境变量优先
const dbConfig = {
  host: process.env.MYSQL_IP || process.env.DB_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USERNAME || process.env.DB_USER || 'root',
  database: process.env.MYSQL_DATABASE || process.env.DB_NAME || 'lieyouqi',
  password: (process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD || '').replace(/"/g, '')
};

// 简单的SQL执行API
router.post('/execute-sql', async (req, res) => {
  try {
    console.log('开始执行SQL...');

    // 执行SQL恢复posts表
    await db.query('CREATE TABLE IF NOT EXISTS posts_current AS SELECT * FROM posts');
    await db.query('DROP TABLE IF EXISTS posts');

    // 创建新的posts表
    await db.query(`
      CREATE TABLE posts (
        id VARCHAR(24) NOT NULL PRIMARY KEY,
        userId VARCHAR(20) NULL,
        content TEXT NULL,
        images TEXT NULL,
        userInfo TEXT NULL,
        createTime BIGINT NULL,
        likeCount INT DEFAULT 0,
        commentCount INT DEFAULT 0,
        shareCount INT DEFAULT 0,
        video VARCHAR(255) NULL,
        topic VARCHAR(100) NULL,
        region VARCHAR(255) NULL,
        isPublic BOOLEAN DEFAULT true,
        allowComment BOOLEAN DEFAULT true,
        allowForward BOOLEAN DEFAULT true,
        linkedProducts TEXT NULL,
        location VARCHAR(255) NULL,
        title VARCHAR(255) NULL,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        extra1 TEXT NULL,
        extra2 TEXT NULL,
        extra3 TEXT NULL,
        extra4 TEXT NULL,
        extra5 TEXT NULL,
        isLiked BOOLEAN DEFAULT false,
        product TEXT NULL,
        topics TEXT NULL,
        visible BOOLEAN DEFAULT true,
        isHidden BOOLEAN DEFAULT false
      )
    `);

    // 从备份表恢复数据
    await db.query('INSERT INTO posts SELECT * FROM posts_backup');

    // 确保所有帖子都有主题
    await db.query("UPDATE posts SET topic = '企业服务' WHERE topic IS NULL OR topic = ''");

    // 获取恢复的数据条数
    const restoredCount = await db.query('SELECT COUNT(*) as count FROM posts');
    const noTopicCount = await db.query("SELECT COUNT(*) as count FROM posts WHERE topic IS NULL OR topic = ''");

    res.json({
      success: true,
      message: `已成功从posts_backup恢复 ${restoredCount[0].count} 条数据到posts表`,
      restoredCount: restoredCount[0].count,
      noTopicCount: noTopicCount[0].count
    });
  } catch (error) {
    console.error('执行SQL时发生错误:', error);
    res.status(500).json({
      success: false,
      message: '执行SQL时发生错误',
      error: error.message
    });
  }
});

// 恢复posts表
router.post('/restore-posts', async (req, res) => {
  try {
    console.log('开始恢复posts表...');

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');

    // 检查posts_backup表是否存在
    const [backupTables] = await connection.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'posts_backup'",
      [dbConfig.database]
    );

    const backupExists = backupTables[0].count > 0;
    console.log(`备份表posts_backup ${backupExists ? '存在' : '不存在'}`);

    if (!backupExists) {
      await connection.end();
      return res.status(400).json({
        success: false,
        message: '备份表posts_backup不存在，无法恢复数据'
      });
    }

    // 检查posts表是否存在
    const [postsTables] = await connection.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'posts'",
      [dbConfig.database]
    );

    const postsExists = postsTables[0].count > 0;
    console.log(`posts表 ${postsExists ? '存在' : '不存在'}`);

    if (postsExists) {
      // 备份当前的posts表
      console.log('备份当前的posts表到posts_current...');
      await connection.query('CREATE TABLE IF NOT EXISTS posts_current AS SELECT * FROM posts');
      console.log('当前posts表已备份到posts_current');

      // 删除当前的posts表
      console.log('删除当前的posts表...');
      await connection.query('DROP TABLE posts');
      console.log('当前posts表已删除');
    }

    // 创建新的posts表，使用与posts_backup相同的结构
    console.log('创建新的posts表，使用与posts_backup相同的结构...');
    await connection.query(`
      CREATE TABLE posts (
        id VARCHAR(24) NOT NULL PRIMARY KEY,
        userId VARCHAR(20) NULL,
        content TEXT NULL,
        images TEXT NULL,
        userInfo TEXT NULL,
        createTime BIGINT NULL,
        likeCount INT DEFAULT 0,
        commentCount INT DEFAULT 0,
        shareCount INT DEFAULT 0,
        video VARCHAR(255) NULL,
        topic VARCHAR(100) NULL,
        region VARCHAR(255) NULL,
        isPublic BOOLEAN DEFAULT true,
        allowComment BOOLEAN DEFAULT true,
        allowForward BOOLEAN DEFAULT true,
        linkedProducts TEXT NULL,
        location VARCHAR(255) NULL,
        title VARCHAR(255) NULL,
        updateTime DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        extra1 TEXT NULL,
        extra2 TEXT NULL,
        extra3 TEXT NULL,
        extra4 TEXT NULL,
        extra5 TEXT NULL,
        isLiked BOOLEAN DEFAULT false,
        product TEXT NULL,
        topics TEXT NULL,
        visible BOOLEAN DEFAULT true,
        isHidden BOOLEAN DEFAULT false
      )
    `);
    console.log('新的posts表已创建');

    // 从备份表恢复数据
    console.log('从备份表恢复数据...');
    await connection.query(`
      INSERT INTO posts
      SELECT * FROM posts_backup
    `);

    // 检查恢复的数据
    const [restoredCount] = await connection.query('SELECT COUNT(*) as count FROM posts');
    console.log(`已恢复 ${restoredCount[0].count} 条数据到posts表`);

    // 确保所有帖子都有主题
    console.log('确保所有帖子都有主题...');
    await connection.query("UPDATE posts SET topic = '企业服务' WHERE topic IS NULL OR topic = ''");

    const [noTopicCount] = await connection.query("SELECT COUNT(*) as count FROM posts WHERE topic IS NULL OR topic = ''");
    console.log(`还有 ${noTopicCount[0].count} 条帖子没有主题`);

    // 关闭数据库连接
    await connection.end();
    console.log('数据库连接已关闭');

    res.json({
      success: true,
      message: `已成功从posts_backup恢复 ${restoredCount[0].count} 条数据到posts表`,
      restoredCount: restoredCount[0].count,
      noTopicCount: noTopicCount[0].count
    });
  } catch (error) {
    console.error('恢复posts表时发生错误:', error);
    res.status(500).json({
      success: false,
      message: '恢复posts表时发生错误',
      error: error.message
    });
  }
});

module.exports = router;

<!--关于我们页面-->
<view class="about-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 公司封面 -->
    <view class="company-header">
      <image
        class="company-logo"
        src="{{companyInfo.company_logo || '/images/icons2/关于.png'}}"
        mode="aspectFit"
      />
      <view class="company-name">{{companyInfo.company_name || '公司名称'}}</view>
      <view class="company-type">{{companyInfo.company_type || ''}}</view>
    </view>

    <!-- 公司简介 -->
    <view class="section">
      <view class="section-title">
        <view class="title-icon">📋</view>
        <text>公司简介</text>
      </view>
      <view class="company-description">
        {{companyInfo.company_description || '暂无公司简介'}}
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="section">
      <view class="section-title">
        <view class="title-icon">📞</view>
        <text>联系方式</text>
      </view>
      <view class="contact-list">
        <!-- 电话 -->
        <view class="contact-item" wx:if="{{companyInfo.company_phone}}" bindtap="makePhoneCall">
          <view class="contact-icon">📱</view>
          <view class="contact-content">
            <view class="contact-label">联系电话</view>
            <view class="contact-value phone">{{companyInfo.company_phone}}</view>
          </view>
          <view class="contact-arrow">></view>
        </view>

        <!-- 地址 -->
        <view class="contact-item" wx:if="{{companyInfo.company_address}}" bindtap="openLocation">
          <view class="contact-icon">📍</view>
          <view class="contact-content">
            <view class="contact-label">公司地址</view>
            <view class="contact-value">{{companyInfo.company_address}}</view>
          </view>
          <view class="contact-arrow">></view>
        </view>

        <!-- 邮箱 -->
        <view class="contact-item" wx:if="{{companyInfo.company_email}}" bindtap="copyEmail">
          <view class="contact-icon">✉️</view>
          <view class="contact-content">
            <view class="contact-label">邮箱地址</view>
            <view class="contact-value">{{companyInfo.company_email}}</view>
          </view>
          <view class="contact-arrow">></view>
        </view>

        <!-- 网站 -->
        <view class="contact-item" wx:if="{{companyInfo.company_website}}" bindtap="openWebsite">
          <view class="contact-icon">🌐</view>
          <view class="contact-content">
            <view class="contact-label">官方网站</view>
            <view class="contact-value">{{companyInfo.company_website}}</view>
          </view>
          <view class="contact-arrow">></view>
        </view>

        <!-- 营业时间 -->
        <view class="contact-item" wx:if="{{companyInfo.business_hours}}">
          <view class="contact-icon">🕒</view>
          <view class="contact-content">
            <view class="contact-label">营业时间</view>
            <view class="contact-value">{{companyInfo.business_hours}}</view>
          </view>
        </view>

        <!-- 联系人 -->
        <view class="contact-item" wx:if="{{companyInfo.contact_person}}">
          <view class="contact-icon">👤</view>
          <view class="contact-content">
            <view class="contact-label">联系人</view>
            <view class="contact-value">{{companyInfo.contact_person}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-info">
      <view class="footer-text">感谢您选择我们的服务</view>
      <view class="footer-text">我们将竭诚为您提供优质服务</view>
    </view>
  </view>
</view>

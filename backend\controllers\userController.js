/**
 * 用户控制器
 */
const userService = require('../services/userService');

exports.login = async (req, res, next) => {
  try {
    const { username, password } = req.body;
    const result = await userService.login(username, password);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.loginByPhone = async (req, res, next) => {
  try {
    const { phone, code } = req.body;
    const result = await userService.loginByPhone(phone, code);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.register = async (req, res, next) => {
  try {
    const result = await userService.register(req.body);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};

exports.getUserInfo = async (req, res, next) => {
  try {
    // 优先使用url参数id或userId，否则用当前登录用户
    const userId = req.query.id || req.query.userId || (req.userData && req.userData.userId);
    const result = await userService.getUserInfo(userId);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.updateUserInfo = async (req, res, next) => {
  try {
    console.log('更新用户信息请求:', {
      userData: req.userData,
      body: req.body,
      headers: req.headers
    });

    const userId = req.userData.userId;
    console.log('用户ID:', userId);

    const result = await userService.updateUserInfo(userId, req.body);
    console.log('更新用户信息结果:', result);

    if (!result.success) {
      console.log('更新失败:', result.message);
      return res.status(400).json(result);
    }

    console.log('更新成功');
    res.json(result);
  } catch (error) {
    console.error('更新用户信息异常:', error);
    next(error);
  }
};

exports.sendVerificationCode = async (req, res, next) => {
  try {
    const { phone } = req.body;
    const result = await userService.sendVerificationCode(phone);

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.updatePassword = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { oldPassword, newPassword } = req.body;
    const result = await userService.updatePassword(userId, oldPassword, newPassword);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.bindWechat = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const { code, userInfo } = req.body;
    const result = await userService.bindWechat(userId, code, userInfo);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.unbindWechat = async (req, res, next) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.unbindWechat(userId);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.sendVerifyCode = async (req, res, next) => {
  try {
    const { phone } = req.body;
    console.log('发送验证码请求:', { phone });

    const result = await userService.sendVerifyCode(phone);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('发送验证码异常:', error);
    next(error);
  }
};

exports.resetPasswordByPhone = async (req, res, next) => {
  try {
    const { phone, code, newPassword } = req.body;
    console.log('重置密码请求:', { phone, code: '***', newPassword: '***' });

    const result = await userService.resetPasswordByPhone(phone, code, newPassword);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.json(result);
  } catch (error) {
    console.error('重置密码异常:', error);
    next(error);
  }
};

exports.followUser = async (req, res) => {
  try {
    const { followingId } = req.body;
    const followerId = req.userData.userId;
    const result = await userService.followUser(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('关注用户失败:', error);
    res.status(500).json({
      success: false,
      message: '关注用户失败'
    });
  }
};

exports.unfollowUser = async (req, res) => {
  try {
    const { followingId } = req.body;
    const followerId = req.userData.userId;
    const result = await userService.unfollowUser(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('取消关注失败:', error);
    res.status(500).json({
      success: false,
      message: '取消关注失败'
    });
  }
};

exports.checkFollowStatus = async (req, res) => {
  try {
    // 增强：未登录时直接返回未关注
    if (!req.userData || !req.userData.userId) {
      return res.json({
        success: true,
        data: { isFollowing: false },
        message: '未登录，默认未关注'
      });
    }
    const { followingId } = req.query;
    const followerId = req.userData.userId;

    const result = await userService.checkFollowStatus(followerId, followingId);
    res.json(result);
  } catch (error) {
    console.error('获取关注状态失败:', error);
    res.status(200).json({
      success: true,
      data: { isFollowing: false },
      message: '获取关注状态异常，默认未关注'
    });
  }
};

exports.getMyFollowList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyFollowList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的关注列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的关注列表失败' });
  }
};

exports.getMyFansList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyFansList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的粉丝列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的粉丝列表失败' });
  }
};

exports.getMyPromotionList = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyPromotionList(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的推广用户列表失败:', error);
    res.status(500).json({ success: false, message: '获取我的推广用户列表失败' });
  }
};

exports.getMyComments = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const result = await userService.getMyComments(userId);
    res.json(result);
  } catch (error) {
    console.error('获取我的评论失败:', error);
    res.status(500).json({ success: false, message: '获取我的评论失败' });
  }
};

exports.getPostComments = async (req, res) => {
  try {
    const postId = req.query.postId;
    if (!postId) return res.status(400).json({ success: false, message: '缺少postId参数' });
    const result = await userService.getPostComments(postId);
    res.json(result);
  } catch (error) {
    console.error('获取帖子评论失败:', error);
    res.status(500).json({ success: false, message: '获取帖子评论失败' });
  }
};

exports.addPostComment = async (req, res) => {
  try {
    const userId = req.userData.userId;
    const { postId, content } = req.body;
    const result = await userService.addPostComment(userId, postId, content);
    res.json(result);
  } catch (error) {
    console.error('发表评论失败:', error);
    res.status(500).json({ success: false, message: '发表评论失败' });
  }
};

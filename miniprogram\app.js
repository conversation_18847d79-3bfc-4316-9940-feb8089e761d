// app.js
const { userApi } = require('./utils/api');
const loginStateManager = require('./utils/login-state-manager');

// 全局错误处理已移除，避免过度提示用户

App({
  onLaunch: function () {
    // 先初始化全局数据
    this.globalData = {
      userInfo: null,
      isLogin: false, // 全局登录状态
      needRefreshProfile: false, // 是否需要刷新"我的"页面
      userIdSet: new Set(), // 已生成的用户ID集合，用于防止重复
      cloudEnvId: 'prod-5geioww562624006', // 云环境ID
      theme: {
        primaryColor: '#FF4D4F',
        secondaryColor: '#FFE8E8',
        successColor: '#52C41A',
        warningColor: '#FAAD14',
        errorColor: '#FF4D4F',
        linkColor: '#1890FF',
        backgroundColor: '#F7F7F7',
        textColorPrimary: '#333333',
        textColorRegular: '#666666',
        textColorSecondary: '#999999',
        textColorDisabled: '#CCCCCC'
      }
    };

    // 初始化微信云开发环境
    if (wx.cloud) {
      try {
        wx.cloud.init({
          env: this.globalData.cloudEnvId, // 微信云托管环境ID
          traceUser: true
        });
        // 云开发环境已初始化

        // 不再测试云函数调用，因为项目使用云托管而非云函数
        // 云环境已初始化
      } catch (err) {
        // 云开发环境初始化失败

        // 尝试重新初始化
        try {
          wx.cloud.init({
            env: this.globalData.cloudEnvId,
            traceUser: true
          });
          // 云开发环境重新初始化成功
        } catch (e) {
          // 云开发环境重新初始化失败
        }
      }
    } else {
      // 请使用 2.2.3 或以上的基础库以使用云能力
    }

    // 前后端分离架构，不再使用云开发
    // 小程序启动

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.globalData.systemInfo = systemInfo;

    // 检查用户登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const that = this;
    // App全局检查登录状态

    // 首先尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');

    // 如果本地有登录状态，先使用本地状态快速更新全局状态
    if (loginState && loginState.isLogin && userInfo) {
      // 从本地存储找到登录状态

      // 更新全局状态
      that.globalData.userInfo = userInfo;
      that.globalData.isLogin = true;

      // 标记需要刷新个人中心页面
      that.globalData.needRefreshProfile = true;
    }

    // 使用登录状态管理器验证登录状态，但不阻塞UI
    loginStateManager.validateLoginState()
      .then(result => {
        // 登录状态验证结果

        if (result.isValid) {
          // 登录状态有效
          // 登录状态有效

          // 更新全局用户信息
          that.globalData.userInfo = result.userInfo;
          that.globalData.isLogin = true;

          // 标记需要刷新个人中心页面
          that.globalData.needRefreshProfile = true;
        } else if (!result.usingLocalState) {
          // 登录状态无效，但不立即清除，而是检查是否有本地状态可用
          // 登录状态验证结果无效

          // 如果是由于网络问题导致的验证失败，尝试使用本地状态
          if (loginState && userInfo) {
            // 尝试使用本地登录状态

            // 更新全局状态
            that.globalData.userInfo = userInfo;
            that.globalData.isLogin = true;

            // 标记需要刷新个人中心页面
            that.globalData.needRefreshProfile = true;
          } else {
            // 如果没有可用的本地状态，才清除登录状态
            // 没有可用的本地状态
            loginStateManager.clearLoginState();

            // 更新全局状态
            that.globalData.userInfo = null;
            that.globalData.isLogin = false;

            // 如果是由于ID不一致导致的无效，显示提示
            if (result.message === '用户ID不一致') {
              wx.showModal({
                title: '登录状态异常',
                content: '检测到登录状态异常，将重新登录以确保数据一致性',
                showCancel: false,
                success: () => {
                  // 跳转到登录页
                  wx.navigateTo({
                    url: '/pages/auth/auth'
                  });
                }
              });
            }
          }
        }
      })
      .catch(err => {
        // 验证登录状态出错

        // 网络错误时，尝试使用本地状态
        if (loginState && userInfo) {
          // 网络错误，使用本地登录状态

          // 更新全局状态
          that.globalData.userInfo = userInfo;
          that.globalData.isLogin = true;

          // 标记需要刷新个人中心页面
          that.globalData.needRefreshProfile = true;
        } else {
          // 如果没有可用的本地状态，才清除登录状态
          loginStateManager.clearLoginState();

          // 更新全局状态
          that.globalData.userInfo = null;
          that.globalData.isLogin = false;
        }
      });
  },

  // 登录方法
  login: function(code, callback) {
    const that = this;

    // 显示加载中
    wx.showLoading({
      title: '登录中',
      mask: true
    });

    // 使用登录状态管理器登录
    loginStateManager.login(code, userApi.login)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 更新全局数据
          that.globalData.userInfo = res.data.userInfo;
          that.globalData.isLogin = true;
          that.globalData.needRefreshProfile = true; // 标记需要刷新个人中心页面

          console.log('登录成功，用户ID:', res.data.userInfo.id);

          // 触发页面刷新事件，通知首页刷新数据
          const pages = getCurrentPages();
          if (pages.length > 0) {
            const currentPage = pages[pages.length - 1];
            // 如果当前页面是首页，则刷新数据
            if (currentPage.route === 'pages/home/<USER>') {
              currentPage.onLoad(currentPage.options);
            } else {
              // 查找首页实例并刷新
              const homePage = pages.find(page => page.route === 'pages/home/<USER>');
              if (homePage) {
                homePage.onLoad(homePage.options);
              }
            }
          }

          if (callback) {
            callback(true, res.data);
          }
        } else {
          console.warn('登录失败:', res.message);

          if (callback) {
            callback(false, res.message);
          }
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('登录失败:', err);

        if (callback) {
          callback(false, '网络错误，请稍后再试');
        }
      });
  },

  // 检查是否需要登录
  checkNeedLogin: function(callback) {
    // 如果已登录，直接执行回调
    if (this.globalData.isLogin && this.globalData.userInfo) {
      if (callback && typeof callback === 'function') {
        callback(true);
      }
      return true;
    }

    // 首先尝试从本地存储获取登录状态
    const loginState = loginStateManager.getLoginState();
    const userInfo = wx.getStorageSync('userInfo');

    // 如果本地有登录状态，先使用本地状态快速更新全局状态
    if (loginState && loginState.isLogin && userInfo) {
      // 从本地存储找到登录状态

      // 更新全局状态
      this.globalData.userInfo = userInfo;
      this.globalData.isLogin = true;

      if (callback && typeof callback === 'function') {
        callback(true);
      }
      return true;
    }

    // 使用登录状态管理器验证登录状态
    const that = this;
    loginStateManager.validateLoginState()
      .then(result => {
        if (result.isValid) {
          // 登录状态有效
          that.globalData.userInfo = result.userInfo;
          that.globalData.isLogin = true;

          if (callback && typeof callback === 'function') {
            callback(true);
          }
        } else {
          // 登录状态无效，显示登录提示
          that.showLoginModal(callback);
        }
      })
      .catch(err => {
        // 验证登录状态出错

        // 网络错误时，再次尝试使用本地状态
        if (loginState && userInfo) {
          // 网络错误，使用本地登录状态

          // 更新全局状态
          that.globalData.userInfo = userInfo;
          that.globalData.isLogin = true;

          if (callback && typeof callback === 'function') {
            callback(true);
          }
        } else {
          // 显示登录提示
          that.showLoginModal(callback);
        }
      });

    return false;
  },

  // 显示登录提示
  showLoginModal: function(callback) {
    console.log('显示登录提示...');
    wx.showModal({
      title: '提示',
      content: '该操作需要登录，是否前往登录？',
      confirmText: '去登录',
      cancelText: '取消',
      success: function(res) {
        if (res.confirm) {
          console.log('用户选择去登录');
          // 跳转到登录页
          wx.navigateTo({
            url: '/pages/auth/auth'
          });
        } else {
          console.log('用户取消登录');
        }

        if (callback && typeof callback === 'function') {
          callback(false);
        }
      }
    });
  }
});

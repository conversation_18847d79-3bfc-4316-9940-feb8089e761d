/**
 * 消息服务
 */
const Message = require('../models/Message');
const User = require('../models/User');

class MessageService {
  async sendMessage(senderId, receiverId, content, type = 'text') {
    try {
      // 检查接收者是否存在
      let receiver = await User.findById(receiverId);

      // 如果接收者不存在，但ID格式正确，则创建一个默认用户
      if (!receiver && receiverId && receiverId.length > 5) {
        console.log('接收者不存在，创建默认用户:', receiverId);
        try {
          // 创建默认用户
          const defaultUserData = {
            id: receiverId,
            username: `user_${receiverId.substring(0, 5)}`,
            nickname: `用户${receiverId.substring(0, 4)}`,
            avatar: '/images/icons2/男头像.png',
            createTime: Date.now(),
            updateTime: Date.now()
          };

          receiver = await User.create(defaultUserData);
          console.log('创建默认用户成功:', receiver);
        } catch (createError) {
          console.error('创建默认用户失败:', createError);
          // 即使创建用户失败，也继续发送消息
        }
      }

      if (!receiver) {
        console.warn('接收者不存在且无法创建默认用户');
      }

      // 创建消息
      const message = await Message.create({
        senderId,
        receiverId,
        content,
        type
      });

      return {
        success: true,
        data: message,
        message: '发送成功'
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  async getMessages(id, targetUserId = null, page = 1, pageSize = 20) {
    try {
      console.log('[消息服务调试] id:', id, '类型:', typeof id, 'targetUserId:', targetUserId, 'page:', page, 'pageSize:', pageSize);

      // 确保id有值
      if (!id) {
        console.error('[消息服务调试] 用户ID为空');
        return {
          success: false,
          data: [],
          message: '获取消息失败: 用户ID为空'
        };
      }

      // 保留原始ID格式，不强制转换为数字
      const userId = id;
      console.log('[消息服务调试] 使用的userId:', userId, '类型:', typeof userId);

      let result;
      if (targetUserId) {
        // 获取与特定用户的对话
        console.log('[消息服务调试] 获取与特定用户的对话');
        result = await Message.getMessages(userId, targetUserId, page, pageSize);
      } else {
        // 获取所有消息
        console.log('[消息服务调试] 获取所有消息');
        result = await Message.findAll({ userId: userId, page, pageSize });
      }

      console.log('[消息服务调试] 查询结果:', JSON.stringify(result));

      // 检查结果是否有list属性
      if (!result || !result.list) {
        console.error('[消息服务调试] 查询结果异常:', result);
        return {
          success: false,
          data: [],
          message: '获取消息失败: 查询结果异常'
        };
      }

      // 检查消息列表是否为空
      if (result.list.length === 0) {
        console.log('[消息服务调试] 消息列表为空');
      } else {
        console.log('[消息服务调试] 获取到', result.list.length, '条消息');
        // 打印第一条消息的内容
        console.log('[消息服务调试] 第一条消息:', JSON.stringify(result.list[0]));
      }

      return {
        success: true,
        data: result.list,
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        message: '获取消息成功'
      };
    } catch (error) {
      console.error('获取消息失败:', error);
      throw error;
    }
  }

  async getUnreadCount(userId) {
    try {
      const count = await Message.getUnreadCount(userId);
      return {
        success: true,
        data: { count },
        message: '获取未读消息数成功'
      };
    } catch (error) {
      console.error('获取未读消息数失败:', error);
      throw error;
    }
  }

  async getRecentChats(userId) {
    try {
      const chats = await Message.getRecentChats(userId);
      return {
        success: true,
        data: chats,
        message: '获取最近聊天列表成功'
      };
    } catch (error) {
      console.error('获取最近聊天列表失败:', error);
      throw error;
    }
  }
}

module.exports = new MessageService();

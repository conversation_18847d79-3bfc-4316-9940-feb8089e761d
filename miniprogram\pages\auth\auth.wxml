<!--pages/auth/auth.wxml-->
<view class="auth-container">
  <view class="header">
    <view class="title">欢迎登录</view>
    <view class="subtitle">登录后体验更多功能</view>
  </view>

  <!-- 登录方式选项卡 -->
  <view class="login-tabs">
    <view class="login-tab {{loginType === 'phone' ? 'active' : ''}}" bindtap="switchLoginType" data-type="phone">手机号登录</view>
    <view class="login-tab {{loginType === 'account' ? 'active' : ''}}" bindtap="switchLoginType" data-type="account">账号密码登录</view>
  </view>

  <!-- 手机号登录 -->
  <view class="input-area" wx:if="{{loginType === 'phone'}}">
    <view class="input-group">
      <image class="input-icon" src="../../images/icons2/手机.png"></image>
      <input class="input" type="number" placeholder="请输入手机号" maxlength="11" bindinput="inputPhoneNumber" />
    </view>

    <view class="input-group">
      <image class="input-icon" src="../../images/icons2/验证码.png"></image>
      <input class="input" type="number" placeholder="请输入验证码" maxlength="6" bindinput="inputVerifyCode" />
      <view class="verify-btn {{canGetCode && countdown <= 0 ? 'active' : ''}}" bindtap="getVerifyCode">
        {{countdown > 0 ? countdown + 's' : '获取验证码'}}
      </view>
    </view>
  </view>

  <!-- 账号密码登录 -->
  <view class="input-area" wx:if="{{loginType === 'account'}}">
    <view class="input-group">
      <image class="input-icon" src="../../images/icons2/用户.png"></image>
      <input class="input" type="text" placeholder="请输入用户名/手机号" bindinput="inputUsername" />
    </view>

    <view class="input-group">
      <image class="input-icon" src="../../images/icons2/密码.png"></image>
      <input class="input" type="password" placeholder="请输入密码" password="true" bindinput="inputPassword" />
    </view>

    <view class="forgot-password" bindtap="navigateToResetPassword">忘记密码？</view>
  </view>

  <view class="login-btn {{canLogin ? 'active' : ''}}" bindtap="login">登录</view>

  <view class="divider-container">
    <view class="divider"></view>
    <view class="divider-text">其他登录方式</view>
    <view class="divider"></view>
  </view>

  <view class="other-login">
    <button class="other-login-item wx-login-btn" open-type="getUserInfo" bindgetuserinfo="wxLogin" hover-class="button-hover">
      <image class="other-login-icon" src="/images/icons/wechat.png"></image>
      <text class="other-login-text">微信登录</text>
    </button>
  </view>

  <view class="agreement">
    <view class="checkbox {{isAgreement ? 'checked' : ''}}" bindtap="toggleAgreement">
      <image class="checkbox-icon" src="{{isAgreement ? '/images/icons2/已点赞.png' : '/images/icons2/未点赞.png'}}"></image>
    </view>
    <view class="agreement-text">
      我已阅读并同意
      <text class="agreement-link" bindtap="viewUserAgreement">《用户协议》</text>
      和
      <text class="agreement-link" bindtap="viewPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>
</view>

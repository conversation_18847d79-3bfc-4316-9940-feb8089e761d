/**app.wxss**/
/* 全局样式规范 */

/* 全局 CSS 变量 */
page {
  --status-bar-height: 0px;
}

/* 基础样式重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 15px;
  line-height: 1.5;
  color: #333333;
  background-color: #F7F7F7;
  box-sizing: border-box;
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

/* 文字大小 */
.text-large {
  font-size: 18px;
}

.text-medium {
  font-size: 16px;
}

.text-normal {
  font-size: 15px;
}

.text-small {
  font-size: 13px;
}

.text-mini {
  font-size: 12px;
}

/* 文字颜色 */
.text-primary {
  color: #333333;
}

.text-regular {
  color: #666666;
}

.text-secondary {
  color: #999999;
}

.text-disabled {
  color: #CCCCCC;
}

.text-brand {
  color: #FF4D4F;
}

.text-link {
  color: #1890FF;
}

.text-success {
  color: #52C41A;
}

.text-warning {
  color: #FAAD14;
}

.text-error {
  color: #FF4D4F;
}

/* 文字对齐 */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 文字粗细 */
.text-bold {
  font-weight: bold;
}

.text-medium-weight {
  font-weight: 500;
}

.text-normal-weight {
  font-weight: 400;
}

/* 边距 */
.margin-xs {
  margin: 4px;
}

.margin-sm {
  margin: 8px;
}

.margin {
  margin: 12px;
}

.margin-lg {
  margin: 16px;
}

.margin-xl {
  margin: 20px;
}

.padding-xs {
  padding: 4px;
}

.padding-sm {
  padding: 8px;
}

.padding {
  padding: 12px;
}

.padding-lg {
  padding: 16px;
}

.padding-xl {
  padding: 20px;
}

/* 圆角 */
.radius-large {
  border-radius: 22px;
}

.radius-medium {
  border-radius: 12px;
}

.radius-small {
  border-radius: 8px;
}

.radius-mini {
  border-radius: 4px;
}

/* 卡片样式 */
.card {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 0 16px;
  font-size: 15px;
  text-align: center;
  border-radius: 4px;
  line-height: 36px;
  transition: all 0.3s ease-in-out;
}

.btn-primary {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

.btn-outline {
  background-color: transparent;
  color: #FF4D4F;
  border: 1px solid #FF4D4F;
}

.btn-large {
  line-height: 44px;
  font-size: 16px;
  border-radius: 22px;
}

.btn-small {
  line-height: 28px;
  font-size: 13px;
}

.btn-mini {
  line-height: 22px;
  font-size: 12px;
  padding: 0 12px;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: #EEEEEE;
  margin: 12px 0;
}

/* 布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

/* 图片样式 */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

/* 徽标 */
.badge {
  position: relative;
}

.badge-dot {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #FF4D4F;
}

.badge-count {
  position: absolute;
  top: -8px;
  right: -8px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
  background-color: #FF4D4F;
  color: #FFFFFF;
  font-size: 12px;
  text-align: center;
  padding: 0 4px;
}

/* 输入框 */
.input {
  width: 100%;
  height: 36px;
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 15px;
}

.input-large {
  height: 44px;
  border-radius: 22px;
}

.input-small {
  height: 28px;
  font-size: 13px;
}

/* 标签 */
.tag {
  display: inline-block;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  border-radius: 4px;
  background-color: #F5F5F5;
  color: #666666;
  margin-right: 8px;
}

.tag-primary {
  background-color: #FFE8E8;
  color: #FF4D4F;
}

.tag-success {
  background-color: #E8F7E2;
  color: #52C41A;
}

.tag-warning {
  background-color: #FFF8E6;
  color: #FAAD14;
}

.tag-error {
  background-color: #FFE8E8;
  color: #FF4D4F;
}

/* 动画 */
.transition {
  transition: all 0.3s ease-in-out;
}

.transition-fast {
  transition: all 0.2s ease-in-out;
}

.transition-slow {
  transition: all 0.5s ease-in-out;
}

/* 阴影 */
.shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shadow-light {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.shadow-heavy {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 容器 */
.container {
  padding: 16px;
}

/* 安全区域 */
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 底部固定区域 */
.fixed-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  z-index: 100;
}

/* 顶部固定区域 */
.fixed-top {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  background-color: #FFFFFF;
  z-index: 100;
}

/* 自定义导航栏标题样式 */
.navigation-title {
  font-size: 20px !important; /* 增大导航栏标题字号 */
  font-weight: bold !important;
}

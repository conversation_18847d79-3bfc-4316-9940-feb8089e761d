<!--pages/publish/publish.wxml-->
<view class="publish-container">

  <!-- 内容编辑区 -->
  <view class="content-area">
    <textarea class="content-input"
              placeholder="分享新鲜事..."
              maxlength="{{maxContentLength}}"
              bindinput="inputContent"
              value="{{content}}"
              fixed="{{true}}"
              show-confirm-bar="{{false}}"
              disable-default-padding="{{true}}"
              cursor-spacing="20"
              style="height: {{5 * 24}}px;"></textarea>

    <view class="word-count {{content.length >= maxContentLength ? 'word-count-limit' : ''}}">{{content.length}}/{{maxContentLength}}</view>
  </view>

  <!-- 按钮区域 -->
  <view class="buttons-container">
    <view class="floating-cancel-btn" bindtap="clearContent">清空</view>
    <view class="floating-publish-btn" bindtap="publish">发布</view>
  </view>

  <!-- 媒体添加区 -->
  <view class="media-area media-area-tight">
    <!-- 媒体选择器 -->
    <view class="media-selector">
      <view class="media-row">
        <!-- 图片上传控件容器（3/4宽度） -->
        <view class="media-upload-section image-upload-section flex-3">
          <view class="media-upload-header">
            <view class="media-upload-title">图片</view>
            <text class="media-upload-desc">{{images.length}}/{{maxImageCount}}</text>
          </view>
          <view class="image-container-wrapper">
            <view class="image-grid-container">
              <block wx:for="{{images}}" wx:key="*this">
                <view class="image-grid-item">
                  <image class="grid-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
                  <view class="delete-icon grid-delete" catchtap="deleteImage" data-index="{{index}}">
                    <image src="/images/icons2/关闭.png"></image>
                  </view>
                </view>
              </block>
              <view class="add-image-btn" bindtap="chooseImage" wx:if="{{!video && images.length < maxImageCount}}">
                <image src="/images/icons2/添加图片.png"></image>
              </view>
            </view>
          </view>
        </view>

        <!-- 视频上传控件容器（1/4宽度） -->
        <view class="media-upload-section video-upload-section flex-1">
          <view class="media-upload-header">
            <view class="media-upload-title">视频</view>
            <text class="media-upload-desc">{{video ? '已选择' : '未选择'}}</text>
          </view>
          <view class="large-video-container-wrapper">
            <view class="video-container large-video-container" wx:if="{{video}}">
              <video class="video-player large-video-player" src="{{video.tempFilePath}}" object-fit="cover"></video>
              <view class="video-duration">{{Math.floor(video.duration / 60)}}:{{Math.floor(video.duration % 60) < 10 ? '0' + Math.floor(video.duration % 60) : Math.floor(video.duration % 60)}}</view>
              <view class="delete-icon" catchtap="deleteVideo">
                <image src="/images/icons2/关闭.png"></image>
              </view>
            </view>
            <view class="add-video-btn large-add-video-btn" bindtap="chooseVideo" wx:if="{{!video && !images.length}}">
              <image src="/images/icons2/视频.png"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 图片缩略图列表 -->
      <!-- <view class="image-thumbnails" wx:if="{{images.length > 1}}">
        <scroll-view scroll-x="true" class="thumbnails-scroll">
          <view class="thumbnail-item" wx:for="{{images}}" wx:key="*this" wx:if="{{index > 0}}">
            <image class="thumbnail-image" src="{{item}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
            <view class="delete-icon thumbnail-delete" catchtap="deleteImage" data-index="{{index}}">
              <image src="/images/icons2/关闭.png"></image>
            </view>
          </view>
        </scroll-view>
      </view> -->
    </view>
  </view>

  <!-- 卡片式选项区域 -->
  <view class="card-tabs-container">
    <view class="card-tabs-header">
      <view class="card-tab {{activeTab === 'topic' ? 'active' : ''}}" bindtap="switchTab" data-tab="topic">
        <text>主题</text>
        <view class="card-tab-indicator" wx:if="{{topic}}"></view>
      </view>
      <view class="card-tab {{activeTab === 'region' ? 'active' : ''}}" bindtap="switchTab" data-tab="region">
        <text>地区</text>
        <view class="card-tab-indicator" wx:if="{{region}}"></view>
      </view>
      <view class="card-tab {{activeTab === 'product' ? 'active' : ''}}" bindtap="switchTab" data-tab="product">
        <text>商品</text>
        <view class="card-tab-indicator" wx:if="{{linkedProducts.length > 0}}"></view>
      </view>
      <view class="card-tab {{activeTab === 'permission' ? 'active' : ''}}" bindtap="switchTab" data-tab="permission">
        <text>权限</text>
      </view>
    </view>

    <view class="card-tabs-content">
      <!-- 主题选择 -->
      <view class="card-tab-pane" wx:if="{{activeTab === 'topic'}}">
        <view class="topic-tags">
          <view class="topic-tag{{topicItem.selected ? ' active' : ''}}"
                wx:for="{{hotTopics}}"
                wx:for-item="topicItem"
                wx:for-index="topicIdx"
                wx:key="{{topicItem.name}}"
                bindtap="selectTopic"
                data-topic="{{topicItem.name}}">
            {{topicItem.name}}
          </view>
        </view>
        <view class="topic-selected-count" wx:if="{{topics.length > 0}}">
          已选择 {{topics.length}}/3 个主题
        </view>
      </view>

      <!-- 地区选择 -->
      <view class="card-tab-pane" wx:if="{{activeTab === 'region'}}">
        <picker mode="region" bindchange="onRegionChange" value="{{regionValue}}" custom-item="全部">
          <view class="picker-view">
            <text>{{regionText}}</text>
            <image class="picker-arrow" src="../../images/icons2/向右.png"></image>
          </view>
        </picker>
      </view>

      <!-- 商品关联 -->
      <view class="card-tab-pane" wx:if="{{activeTab === 'product'}}">
        <view class="linked-products-list" wx:if="{{linkedProducts.length > 0}}">
          <view class="linked-product-container">
            <view class="linked-product-item" bindtap="showProductSelector">
              <image class="linked-product-image" src="{{linkedProducts[0].images[0]}}" mode="aspectFill"></image>
              <view class="linked-product-info">
                <view class="linked-product-name">{{linkedProducts[0].name}}</view>
                <view class="linked-product-price-row">
                  <view class="linked-product-price">¥{{linkedProducts[0].price}}</view>
                  <view class="linked-product-original-price" wx:if="{{linkedProducts[0].originalPrice}}">¥{{linkedProducts[0].originalPrice}}</view>
                </view>
                <view class="linked-product-shop">{{linkedProducts[0].shopName}}</view>
              </view>
              <view class="change-product-icon">
                <image src="../../images/icons2/向右.png"></image>
              </view>
            </view>

            <!-- 清除关联按钮 -->
            <view class="clear-product-btn" bindtap="clearLinkedProducts">
              <view class="clear-btn-text">清除关联</view>
            </view>
          </view>
        </view>

        <view class="add-product-btn" bindtap="showProductSelector" wx:if="{{linkedProducts.length === 0}}">
          <image src="/images/icons2/添加.png"></image>
          <text>添加商品</text>
        </view>
      </view>

      <!-- 权限设置 -->
      <view class="card-tab-pane" wx:if="{{activeTab === 'permission'}}">
        <view class="permission-list">
          <view class="permission-item" bindtap="togglePublic">
            <text class="permission-name">公开</text>
            <switch checked="{{isPublic}}" color="#FF4D4F" />
          </view>
          <view class="permission-item" bindtap="toggleAllowComment">
            <text class="permission-name">允许评论</text>
            <switch checked="{{allowComment}}" color="#FF4D4F" />
          </view>
          <view class="permission-item" bindtap="toggleAllowForward">
            <text class="permission-name">允许转发</text>
            <switch checked="{{allowForward}}" color="#FF4D4F" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="bottom-safe-area"></view>

  <!-- 话题选择器 -->
  <view class="selector-mask" wx:if="{{showTopicSelector}}" bindtap="hideTopicSelector"></view>
  <view class="topic-selector {{showTopicSelector ? 'show' : ''}}">
    <view class="selector-header">
      <text class="selector-title">添加话题</text>
      <view class="selector-close" bindtap="hideTopicSelector">
        <image src="/images/icons2/关闭.png"></image>
      </view>
    </view>

    <view class="topic-input-area">
      <text class="topic-prefix">#</text>
      <input class="topic-input" placeholder="添加话题" value="{{topic}}" bindinput="inputTopic" />
    </view>

    <view class="hot-topics">
      <view class="hot-topics-title">热门话题</view>
      <view class="hot-topic-list">
        <view class="hot-topic-item" wx:for="{{hotTopics}}" wx:key="id" bindtap="selectTopic" data-topic="{{item.name}}">
          <text class="hot-topic-name">#{{item.name}}</text>
          <text class="hot-topic-count">{{item.count}}人参与</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 位置选择器 -->
  <view class="selector-mask" wx:if="{{showLocationSelector}}" bindtap="hideLocationSelector"></view>
  <view class="location-selector {{showLocationSelector ? 'show' : ''}}">
    <view class="selector-header">
      <text class="selector-title">位置信息</text>
      <view class="selector-close" bindtap="hideLocationSelector">
        <image src="/images/icons2/关闭.png"></image>
      </view>
    </view>

    <view class="current-location-btn" bindtap="getCurrentLocation">
      <image class="location-icon" src="/images/icons2/位置.png"></image>
      <text>获取当前位置</text>
    </view>

    <view class="location-list">
      <!-- 这里可以显示附近的位置列表，需要调用地图API -->
    </view>
  </view>

  <!-- 产品选择器 -->
  <view class="selector-mask" wx:if="{{showProductSelector}}" bindtap="hideProductSelector"></view>
  <view class="product-selector {{showProductSelector ? 'show' : ''}}">
    <view class="selector-header">
      <text class="selector-title">{{linkedProducts.length > 0 ? '更换商品' : '选择商品'}}</text>
      <view class="selector-close" bindtap="hideProductSelector">
        <image src="/images/icons2/关闭.png"></image>
      </view>
    </view>

    <view class="product-search">
      <input class="product-search-input" placeholder="搜索商品" bindinput="searchProducts" value="{{searchKeyword}}" />
    </view>

    <scroll-view class="product-list" scroll-y="{{true}}">
      <view class="product-item" wx:for="{{productList}}" wx:key="_id" bindtap="selectProduct" data-id="{{item._id}}">
        <image class="product-image" src="{{item.images[0]}}" mode="aspectFill"></image>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price">¥{{item.price}}</view>
          <view class="product-shop">{{item.shopName}}</view>
        </view>
      </view>

      <view class="no-product" wx:if="{{productList.length === 0}}">
        <text>没有找到相关商品</text>
      </view>
    </scroll-view>
  </view>

</view>

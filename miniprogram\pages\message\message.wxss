/* pages/message/message.wxss */
.message-container {
  min-height: 100vh;
  background-color: #F7F7F7;
}

/* 设置图标样式保留，可能在其他地方使用 */
.settings-icon {
  width: 24px;
  height: 24px;
}

.settings-icon image {
  width: 100%;
  height: 100%;
}

/* 消息分类导航 */
.tab-container {
  background-color: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 99;
  border-top: 1px solid #EEEEEE;
}

.tab-bar {
  display: flex;
  height: 40px;
  border-bottom: 1px solid #F5F5F5;
}

.tab-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: #666666;
}

.tab-item.active {
  color: #333333;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 3px;
  background-color: #FF4D4F;
  border-radius: 1.5px;
}

/* 登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.login-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.login-text {
  font-size: 16px;
  color: #999999;
  margin-bottom: 20px;
}

.login-btn {
  padding: 8px 30px;
  background-color: #1E6A9E;
  color: #FFFFFF;
  border-radius: 20px;
  font-size: 14px;
}

/* 消息列表 */
.message-list {
  padding-bottom: 20px;
}

.message-item {
  display: flex;
  padding: 15px 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #F5F5F5;
}

.message-item.unread {
  background-color: #FFF9F9;
}

.avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  margin-right: 10px;
}

.system-icon {
  width: 46px;
  height: 46px;
  margin-right: 10px;
}

.message-content {
  flex: 1;
  overflow: hidden;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.sender-name {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
}

.message-time {
  font-size: 12px;
  color: #999999;
}

.message-text {
  font-size: 14px;
  color: #666666;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.post-preview {
  display: flex;
  align-items: center;
  background-color: #F7F7F7;
  padding: 8px;
  border-radius: 4px;
}

.post-text {
  flex: 1;
  font-size: 12px;
  color: #999999;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.post-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  margin-left: 8px;
}

.action-btn {
  align-self: flex-start;
  padding: 4px 12px;
  background-color: #F5F5F5;
  color: #FF4D4F;
  border-radius: 15px;
  font-size: 12px;
  margin-top: 5px;
}

.unread-badge {
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  background-color: #FF4D4F;
  color: #FFFFFF;
  border-radius: 9px;
  font-size: 12px;
  padding: 0 5px;
  margin-left: 10px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border: 2px solid #EEEEEE;
  border-top: 2px solid #FF4D4F;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #999999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

/* 搜索栏样式 */
.search-container {
  padding: 10px 16px;
  background-color: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  padding: 8px 12px;
  flex: 1;
  margin-right: 10px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 20px;
  font-size: 14px;
  color: #333333;
}

/* 刷新按钮样式 */
.refresh-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 50%;
}

.refresh-icon {
  width: 20px;
  height: 20px;
}

/* 群消息样式 */
.group-container {
  padding: 20rpx;
}

.group-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.group-list {
  display: flex;
  flex-direction: column;
  padding: 0 12rpx;
  background: #f7f7f7;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  margin: 0 12rpx;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.group-item:last-child {
  border-bottom: none;
}

.group-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
  margin-right: 20rpx;
  object-fit: cover;
  background: #f5f5f5;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.group-info {
  flex: 1;
  overflow: hidden;
}

.group-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.group-desc {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 20rpx;
}

.member-count {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.group-role {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.join-btn {
  font-size: 24rpx;
  color: #fff;
  background: #07c160;
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  margin: 0;
}

.join-btn::after {
  border: none;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.group-tab-switch {
  display: flex;
  justify-content: center;
  margin: 12px 0 8px 0;
}

.group-tab {
  flex: 1;
  text-align: center;
  padding: 10px 0 0 0;
  font-size: 15px;
  color: #666;
  background: #f5f5f5;
  border-radius: 16px 16px 0 0;
  margin: 0 2px;
  cursor: pointer;
  position: relative;
  height: 40px;
}

.group-tab.active {
  color: #1E6A9E;
  background: #fff;
  font-weight: bold;
}

.group-tab-underline {
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: #1E6A9E;
  border-radius: 2px;
}

.group-tab-content {
  background: #f7f7f7;
  border-radius: 24rpx 24rpx 0 0;
  padding: 20rpx 0 40rpx 0;
  min-height: 200px;
}

.create-group-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 12px 0;
  padding: 8px 0;
  background: #1E6A9E;
  color: #fff;
  border-radius: 18px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
}

/* 悬浮创建群聊按钮 */
.float-create-group-btn {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 50px;
  width: 20vw;
  height: 20vw;
  max-width: 80px;
  max-height: 80px;
  min-width: 56px;
  min-height: 56px;
  background: #1E6A9E;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(30,106,158,0.12);
  z-index: 999;
}

.float-create-group-icon {
  width: 60%;
  height: 60%;
  object-fit: contain;
}

.member-btn {
  margin-left: 10px;
  background: #f5f7fa;
  color: #1E6A9E;
  border-radius: 12px;
  font-size: 12px;
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #1E6A9E;
}

.group-header-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
  justify-content: space-between;
}

.group-title-col {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.group-row-1 {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 6rpx;
  padding-right: 80rpx;
}

.group-title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  line-height: 1.4;
}

.owner-tag, .joined-tag {
  margin-left: 8px;
  margin-right: 100px; /* 原72px，改为100px，增加与按钮的间隔 */
}

.group-row-1 .member-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  padding: 0 10px;
  border-radius: 12px;
}

.group-row-2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.group-desc {
  flex: 1;
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
  line-height: 1.4;
}

.group-row-2 .member-count {
  color: #999;
  font-size: 24rpx;
  min-width: 64rpx;
  margin-left: 0;
  display: flex;
  align-items: center;
  height: 40rpx;
  flex-shrink: 0;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 0 12rpx;
  justify-content: center;
}

.group-header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 8px;
  min-width: 56px;
  max-width: 64px;
}

.owner-tag {
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: #FF9800;
  border-radius: 8px;
  padding: 2px 8px;
  font-weight: normal;
  display: inline-block;
  vertical-align: middle;
}

.group-header-right .member-count {
  font-size: 14px;
  color: #888;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 4px;
}

.group-header-right .member-btn {
  margin: 0;
  background: transparent;
  color: #1E6A9E;
  border-radius: 16px;
  font-size: 13px;
  padding: 0 14px;
  height: 28px;
  line-height: 28px;
  border: 1px solid #1E6A9E;
  box-shadow: none;
}

.joined-tag {
  margin-bottom: 4px;
  font-size: 12px;
  color: #fff;
  background: #4CAF50;
  border-radius: 8px;
  padding: 2px 8px;
  font-weight: normal;
  display: inline-block;
  vertical-align: middle;
  margin-left: 8px;
}

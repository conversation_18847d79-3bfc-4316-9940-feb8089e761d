// miniprogram/utils/request.js
const app = getApp();

function request({ url, method = 'GET', data = {}, header = {} }) {
  // 获取 token
  let token = wx.getStorageSync('token');

  // 如果本地存储没有token，尝试从全局数据获取
  if (!token && app && app.globalData) {
    token = app.globalData.token;
  }

  // 如果仍然没有token，尝试重新获取登录状态
  if (!token) {
    try {
      const loginStateManager = require('./login-state-manager');
      const loginState = loginStateManager.getLoginState();
      if (loginState && loginState.token) {
        token = loginState.token;
        // 更新本地存储
        wx.setStorageSync('token', token);
        // 从登录状态管理器恢复token
      }
    } catch (e) {
      // 尝试恢复token失败
    }
  }

  // 准备请求参数

  // 确保header对象存在
  header = header || {};

  if (token) {
    // 使用大写的 Authorization 和正确的 Bearer 格式（注意空格）
    header['Authorization'] = `Bearer ${token}`;
    // 添加认证头

    // 同时添加小写的authorization，以防后端区分大小写
    header['authorization'] = `Bearer ${token}`;
  }

  // 添加内容类型
  if (!header['content-type']) {
    header['content-type'] = 'application/json';
  }

  // 请求头准备完成

  // 如果是POST请求，确保data不是undefined
  if (method === 'POST' && data === undefined) {
    data = {};
  }

  // 如果是对象，确保正确序列化
  // 准备请求数据

  // 云托管相关配置
  const envId = 'prod-5geioww562624006'; // 微信云托管环境ID
  const serviceName = 'lieyouqi'; // 云托管服务名

  return new Promise((resolve, reject) => {
    wx.cloud.callContainer({
      config: {
        env: envId
      },
      path: url, // 直接传递后端接口路径
      method,
      header: {
        'X-WX-SERVICE': serviceName,
        ...header
      },
      data,
      success: res => {
        // 请求成功处理

        // 处理401未授权错误
        if (res.statusCode === 401) {
          // 收到401未授权响应

          // 清除本地token并更新全局状态
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          if (app && app.globalData) {
            app.globalData.isLogin = false;
            app.globalData.userInfo = null;
          }

          // 如果是发帖相关请求，直接返回错误而不弹窗
          if (url.includes('/posts') && method === 'POST') {
            // 发帖请求未授权，返回错误信息
            reject({
              success: false,
              code: 401,
              message: res.data && res.data.message ? res.data.message : '登录状态已失效，请重新登录'
            });
            return;
          }

          // 其他请求显示登录提示
          wx.showModal({
            title: '登录失效',
            content: '请重新登录',
            showCancel: false,
            success: () => {
              wx.navigateTo({ url: '/pages/auth/auth' });
            }
          });

          reject({
            success: false,
            code: 401,
            message: res.data && res.data.message ? res.data.message : '未授权'
          });
          return;
        }

        // 处理其他错误状态码
        if (res.statusCode >= 400) {
          // 请求失败处理

          let errorMessage = '请求失败';
          if (res.data && res.data.message) {
            errorMessage = res.data.message;
          } else if (typeof res.data === 'string') {
            errorMessage = res.data;
          }

          reject({
            success: false,
            code: res.statusCode,
            message: errorMessage
          });
          return;
        }

        // 处理成功响应
        resolve(res.data);
      },
      fail: err => {
        // 请求失败错误处理
        const errorMessage = err.errMsg || '网络请求失败';
        
        // 网络错误类型判断
        const isNetworkError = (
          errorMessage.includes('request:fail') || 
          errorMessage.includes('timeout') ||
          errorMessage.includes('ERR_NAME_NOT_RESOLVED') ||
          errorMessage.includes('ERR_INTERNET_DISCONNECTED')
        );

        if (isNetworkError) {
          // 检查网络状态
          wx.getNetworkType({
            success: function(res) {
              if (res.networkType === 'none') {
                wx.showToast({
                  title: '网络连接已断开，请检查网络设置',
                  icon: 'none',
                  duration: 3000
                });
              } else {
                wx.showToast({
                  title: '网络不稳定，请稍后重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            }
          });

          // 监听网络状态变化
          wx.onNetworkStatusChange(function(res) {
            if (res.isConnected) {
              // 网络恢复时自动重试一次
              request({ url, method, data, header })
                .then(resolve)
                .catch(reject);
            }
          });
        }

        reject({
          success: false,
          code: -1,
          message: errorMessage,
          isNetworkError
        });
      }
    });
  });
}

module.exports = request;
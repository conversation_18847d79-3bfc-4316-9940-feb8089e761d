/* pages/settings/wechat.wxss */
.wechat-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.wechat-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.wechat-content {
  padding: 30rpx;
}

.wechat-status {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.wechat-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 32rpx;
  color: #333333;
}

.tips {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
}

.tips view {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.6;
}

.action-button {
  background-color: #ff4d4f;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
}

.action-button.disabled {
  background-color: #cccccc;
}

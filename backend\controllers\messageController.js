/**
 * 消息控制器
 */
const messageService = require('../services/messageService');

exports.sendMessage = async (req, res) => {
  try {
    const { receiverId, content, type } = req.body;
    const senderId = req.userData && req.userData.userId;
    if (!senderId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }

    const result = await messageService.sendMessage(senderId, receiverId, content, type);
    res.json(result);
  } catch (error) {
    console.error('发送消息失败:', error);
    res.status(500).json({
      success: false,
      message: '发送消息失败'
    });
  }
};

exports.getMessages = async (req, res) => {
  try {
    console.log('[消息控制器] 收到获取消息请求');
    console.log('[消息控制器] 请求路径:', req.path);
    console.log('[消息控制器] 请求方法:', req.method);
    console.log('[消息控制器] 请求参数:', req.query);
    console.log('[消息控制器] 用户数据:', JSON.stringify(req.userData));

    // 获取请求参数，支持targetUserId参数
    const { targetUserId, page = 1, pageSize = 20 } = req.query;

    // 尝试从多个可能的字段获取用户ID
    let id = null;
    if (req.userData) {
      id = req.userData.userId || req.userData.id || req.userData.user_id;
    }

    console.log('[消息控制器] 用户ID:', id, '类型:', typeof id);
    console.log('[消息控制器] 目标用户ID:', targetUserId, '类型:', typeof targetUserId);
    console.log('[消息控制器] 页码:', page, '类型:', typeof page);
    console.log('[消息控制器] 每页数量:', pageSize, '类型:', typeof pageSize);

    if (!id) {
      console.error('[消息控制器] 错误: 用户未登录或ID无效');
      return res.status(401).json({
        success: false,
        message: '用户未登录或ID无效',
        debug: {
          userData: req.userData,
          headers: req.headers
        }
      });
    }

    // 确保ID是字符串类型
    const userId = id.toString();
    console.log('[消息控制器] 转换后的用户ID:', userId, '类型:', typeof userId);

    // 如果提供了targetUserId，则获取与特定用户的聊天记录
    if (targetUserId) {
      console.log('[消息控制器] 获取与特定用户的聊天记录:', targetUserId);
    } else {
      console.log('[消息控制器] 获取所有消息');
    }

    console.log('[消息控制器] 调用消息服务获取消息');
    const result = await messageService.getMessages(userId, targetUserId || null, parseInt(page), parseInt(pageSize));

    console.log('[消息控制器] 消息服务返回结果:', JSON.stringify(result).substring(0, 200) + '...');
    console.log('[消息控制器] 消息数量:', result.data ? result.data.length : 0);

    // 返回响应
    res.json(result);
    console.log('[消息控制器] 已返回响应');
  } catch (error) {
    console.error('[消息控制器] 获取消息失败:', error);
    console.error('[消息控制器] 错误堆栈:', error.stack);

    res.status(500).json({
      success: false,
      message: '获取消息失败',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

exports.getMessageById = async (req, res, next) => {
  try {
    const result = await messageService.getMessageById(req.params.id);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.createMessage = async (req, res, next) => {
  try {
    const result = await messageService.createMessage(req.body);

    if (!result.success) {
      return res.status(400).json(result);
    }

    res.status(201).json(result);
  } catch (error) {
    next(error);
  }
};

exports.markAsRead = async (req, res, next) => {
  try {
    const result = await messageService.markAsRead(req.params.id);

    if (!result.success) {
      return res.status(404).json(result);
    }

    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.markAllAsRead = async (req, res, next) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }
    const { type } = req.body;

    const result = await messageService.markAllAsRead(userId, type);
    res.json(result);
  } catch (error) {
    next(error);
  }
};

exports.getUnreadCount = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }
    const result = await messageService.getUnreadCount(userId);
    res.json(result);
  } catch (error) {
    console.error('获取未读消息数失败:', error);
    res.status(500).json({
      success: false,
      message: '获取未读消息数失败'
    });
  }
};

exports.getRecentChats = async (req, res) => {
  try {
    const userId = req.userData && req.userData.userId;
    if (!userId) {
      return res.status(401).json({ success: false, message: '用户未登录' });
    }
    const result = await messageService.getRecentChats(userId);
    res.json(result);
  } catch (error) {
    console.error('获取最近聊天列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最近聊天列表失败'
    });
  }
};

<view class="container">
  <!-- 群组基本信息 -->
  <view class="group-info">
    <view class="group-header">
      <view class="cover-container">
        <image class="group-cover" src="{{coverUrl || group.cover || '/images/icons/group-default.png'}}" mode="aspectFit" bindtap="{{isManager ? 'onChangeCover' : 'previewImage'}}" data-url="{{coverUrl || group.cover}}"></image>
        <view class="cover-edit-overlay" wx:if="{{isManager}}" bindtap="onChangeCover">
          <view class="cover-edit-icon">📷</view>
          <text class="cover-edit-text">更换封面</text>
        </view>
      </view>
      <view class="group-title">
        <view class="group-name-section">
          <view class="group-name" bindtap="onEditGroupName" wx:if="{{isManager}}">
            <text class="{{group.name ? '' : 'placeholder'}}">{{group.name || '点击设置群名称'}}</text>
            <text class="edit-hint">✏️</text>
          </view>
          <view class="group-name" wx:else>
            <text>{{group.name || '未命名群组'}}</text>
          </view>
        </view>
        <view class="group-desc-section">
          <view class="group-desc" bindtap="onEditGroupDesc" wx:if="{{isManager}}">
            <text class="{{group.description ? '' : 'placeholder'}}">{{group.description || '点击设置群简介'}}</text>
            <text class="edit-hint">✏️</text>
          </view>
          <view class="group-desc" wx:else>
            <text>{{group.description || '暂无群简介'}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="group-meta">
      <view class="meta-row">
        <view class="meta-item">
          <text class="meta-label">创建者：</text>
          <text class="meta-value">{{group.creator || 'XXXXXX'}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">创建时间：</text>
          <text class="meta-value">{{group.createTime || 'XXXX年XX月XX日XX:XX'}}</text>
        </view>
      </view>
    </view>

    <!-- 群公告 -->
    <view class="group-notice">
      <view class="notice-header">
        <text class="notice-title">群公告：</text>
        <view class="notice-edit-btn" bindtap="onEditAnnouncement" wx:if="{{isManager}}">
          <text class="edit-icon">✏️</text>
          <text class="edit-text">编辑</text>
        </view>
      </view>
      <view class="notice-content" bindtap="onEditAnnouncement" wx:if="{{isManager}}">
        <text class="{{group.announcement ? '' : 'placeholder'}}">{{group.announcement || '点击设置群公告'}}</text>
      </view>
      <view class="notice-content" wx:else>
        <text>{{group.announcement || '暂无群公告'}}</text>
      </view>
    </view>
  </view>

  <!-- 群成员 -->
  <view class="member-section">
    <view class="section-header">
      <text class="section-title">群成员</text>
      <text class="member-count">({{group.memberCount || members.length}}人)</text>
    </view>

    <view class="member-list">
      <!-- 现有成员 -->
      <view class="member-grid">
        <block wx:for="{{displayMembers}}" wx:key="userId" wx:for-index="index">
          <view class="member-item">
            <image class="member-avatar" src="{{item.avatar || '/images/icons/default-avatar.png'}}" mode="aspectFill"></image>
            <text class="member-name">{{item.nickname || '微信用户'}}</text>
          </view>
        </block>

        <!-- 添加成员按钮 -->
        <view class="member-item action-item add-member" bindtap="onAddMember" wx:if="{{isManager}}">
          <view class="action-avatar add-icon">+</view>
          <text class="member-name">添加</text>
        </view>

        <!-- 删除成员按钮 -->
        <view class="member-item action-item remove-member" bindtap="onDeleteMember" wx:if="{{isManager}}">
          <view class="action-avatar remove-icon">-</view>
          <text class="member-name">移除</text>
        </view>
      </view>

      <!-- 展开/收起按钮 -->
      <view class="toggle-members" bindtap="toggleShowAllMembers" wx:if="{{members.length > 6}}">
        <text>{{showAllMembers ? '收起' : '查看全部成员'}}</text>
        <text class="toggle-icon">{{showAllMembers ? '▲' : '▼'}}</text>
      </view>
    </view>
  </view>

  <!-- 加入群聊按钮 -->
  <view class="join-group-section">
    <button class="join-btn" bindtap="onJoinGroup" wx:if="{{!isMember}}">
      加入群聊
    </button>
  </view>
</view>
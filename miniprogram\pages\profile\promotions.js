const { userApi } = require('../../utils/api');

Page({
  data: {
    promotionList: [],
    loading: true
  },
  onLoad() {
    this.loadPromotionList();
  },
  loadPromotionList() {
    this.setData({ loading: true });
    userApi.getMyPromotionList().then(res => {
      if (res.success && Array.isArray(res.data)) {
        this.setData({ promotionList: res.data, loading: false });
      } else {
        this.setData({ promotionList: [], loading: false });
      }
    }).catch(() => {
      this.setData({ promotionList: [], loading: false });
    });
  },
  formatTime(ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    return `${y}-${m}-${d} ${h}:${min}`;
  }
}); 
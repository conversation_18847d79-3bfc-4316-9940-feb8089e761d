/* pages/settings/region/region.wxss */
.region-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.region-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.region-content {
  padding: 30rpx;
}

.region-picker {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-label {
  font-size: 30rpx;
  color: #333333;
}

.picker-value {
  display: flex;
  align-items: center;
}

.picker-value text {
  font-size: 30rpx;
  color: #666666;
  margin-right: 10rpx;
}

.arrow-right {
  color: #999999;
  font-size: 24rpx;
}

.save-button {
  background-color: #ff4d4f;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
}

.save-button.disabled {
  background-color: #cccccc;
} 
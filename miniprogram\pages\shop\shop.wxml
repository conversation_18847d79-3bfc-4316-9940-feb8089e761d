<!--pages/shop/shop.wxml-->
<view class="shop-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav">
    <view class="status-bar" style="height:{{statusBarHeight}}px"></view>
    <view class="nav-bar" style="height: {{menuButtonInfo.height + 12}}px;">
      <view class="search-bar">
        <view class="search-input-container" style="width: {{searchWidth}}px; height: {{menuButtonInfo.height}}px;">
          <input class="search-input" placeholder="搜索商品" placeholder-class="search-placeholder" bindinput="onSearchInput" value="{{searchKeyword}}" confirm-type="search" bindconfirm="onSearchConfirm" />
          <view class="search-btn" bindtap="onSearchTap">
            <image src="/images/icons2/搜索.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 占位，防止内容被导航栏遮挡 -->
  <view class="nav-placeholder" style="height:{{statusBarHeight + (menuButtonInfo ? menuButtonInfo.height + 12 : 44)}}px"></view>

  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#FFFFFF">
    <block wx:if="{{banners && banners.length > 0}}">
      <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-index="{{index}}">
        <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image"></image>
        <view class="banner-title">{{item.title}}</view>
      </swiper-item>
    </block>
    <block wx:else>
      <!-- 默认轮播图内容 -->
      <swiper-item>
        <image src="/images/lunbo/001.jpeg" mode="aspectFill" class="banner-image"></image>
        <view class="banner-title">企业服务专场</view>
      </swiper-item>
      <swiper-item>
        <image src="/images/lunbo/002.jpg" mode="aspectFill" class="banner-image"></image>
        <view class="banner-title">知识产权服务</view>
      </swiper-item>
      <swiper-item>
        <image src="/images/lunbo/003.png" mode="aspectFill" class="banner-image"></image>
        <view class="banner-title">财税服务专区</view>
      </swiper-item>
      <swiper-item>
        <image src="/images/lunbo/004.webp" mode="aspectFill" class="banner-image"></image>
        <view class="banner-title">工商注册服务</view>
      </swiper-item>
    </block>
  </swiper>

  <!-- 购物车图标 -->
  <view class="float-cart-icon" catchtap="viewCart" hover-class="float-cart-icon-hover">
    <image src="/images/icons2/购物车.png"></image>
    <view class="badge-count" wx:if="{{cartCount > 0}}">{{cartCount > 99 ? '99+' : cartCount}}</view>
    <view class="cart-text">购物车</view>
  </view>



  <!-- 分类导航 -->
  <view class="category-container" style="top: {{statusBarHeight + (menuButtonInfo ? menuButtonInfo.height + 12 : 44)}}px;">
    <!-- 一级分类 -->
    <scroll-view class="category-scroll" scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}">
      <view class="category-item {{currentCategory === index ? 'active' : ''}} {{index === categories.length - 1 ? 'last-item' : ''}} {{index === 0 ? 'first-item' : ''}}"
            wx:for="{{categories}}"
            wx:key="id"
            bindtap="switchCategory"
            data-index="{{index}}">
        <text>{{item.name}}</text>
        <view class="category-line" wx:if="{{currentCategory === index}}"></view>
      </view>
    </scroll-view>

    <!-- 二级分类 -->
    <scroll-view class="subcategory-scroll" scroll-x="{{true}}" enhanced="{{true}}" show-scrollbar="{{false}}" wx:if="{{subCategories.length > 0}}">
      <view class="subcategory-item {{currentSubCategory === index ? 'active' : ''}}"
            wx:for="{{subCategories}}"
            wx:key="id"
            bindtap="switchSubCategory"
            data-index="{{index}}">
        {{item.name}}
      </view>
    </scroll-view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar" style="top: {{statusBarHeight + (menuButtonInfo ? menuButtonInfo.height + 12 : 44) + 76}}px;">
    <view class="filter-item {{sortType === 'default' ? 'active' : ''}}" bindtap="switchSortType" data-type="default">
      <text>综合</text>
    </view>
    <view class="filter-item {{sortType === 'sales' ? 'active' : ''}}" bindtap="switchSortType" data-type="sales">
      <text>销量</text>
    </view>
    <view class="filter-item {{sortType === 'price-asc' || sortType === 'price-desc' ? 'active' : ''}}" bindtap="switchSortType" data-type="{{sortType === 'price-asc' ? 'price-desc' : 'price-asc'}}">
      <text>价格</text>
      <view class="sort-icon">
        <image src="../../images/icons2/向上.png" class="{{sortType === 'price-asc' ? 'active' : ''}}"></image>
        <image src="../../images/icons2/向下.png" class="{{sortType === 'price-desc' ? 'active' : ''}}"></image>
      </view>
    </view>
    <view class="filter-item" bindtap="showFilter">
      <text>筛选</text>
      <image class="filter-icon" src="../../images/icons2/筛选.png"></image>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list" wx:if="{{!loading && products.length > 0}}">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
      <image class="product-image" src="{{item.images && item.images.length > 0 ? item.images[0] : '/images/xinxi/企业服务.jpg'}}" mode="aspectFill"></image>
      <view class="product-info">
        <view class="product-name">{{item.name || '未命名商品'}}</view>
        <view class="product-price-row">
          <view class="product-price">¥{{item.price || '0.00'}}</view>
          <view class="product-original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</view>
        </view>
        <view class="product-sales">已售{{item.salesCount || 0}}</view>
        <view class="product-shop" wx:if="{{item.shopName}}">{{item.shopName}}</view>
        <view class="product-btns" catchtap>
          <view class="add-btn" catchtap="addToCart" data-id="{{item.id}}" hover-class="btn-hover">
            <image src="/images/icons2/添加购物车.png"></image>
          </view>
          <view class="cart-btn" catchtap="addToCartAndView" data-id="{{item.id}}" hover-class="btn-hover">
            <image src="/images/icons2/直接购买.png"></image>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading && !refreshing}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!loading && !hasMore && products.length > 0}}">
    <text>没有更多商品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/icons2/空.png"></image>
    <text class="empty-text">暂无商品</text>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel-mask" wx:if="{{showFilter}}" bindtap="hideFilter"></view>
  <view class="filter-panel {{showFilter ? 'show' : ''}}">
    <view class="filter-panel-header">
      <text class="filter-panel-title">筛选</text>
      <view class="filter-panel-close" bindtap="hideFilter">
        <image src="/images/icons2/关闭.png"></image>
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">价格区间</view>
      <view class="price-range">
        <input class="price-input" type="digit" placeholder="最低价" value="{{filterOptions.minPrice}}" bindinput="inputMinPrice" />
        <view class="price-separator">-</view>
        <input class="price-input" type="digit" placeholder="最高价" value="{{filterOptions.maxPrice}}" bindinput="inputMaxPrice" />
      </view>
    </view>

    <view class="filter-section">
      <view class="filter-section-title">商品类型</view>
      <view class="filter-options">
        <view class="filter-option {{filterOptions.onlyDiscount ? 'active' : ''}}" bindtap="toggleOnlyDiscount">
          <view class="filter-checkbox">
            <image src="{{filterOptions.onlyDiscount ? '/images/icons2/选中.png' : '/images/icons2/未选中.png'}}"></image>
          </view>
          <text>优惠商品</text>
        </view>
        <view class="filter-option {{filterOptions.onlyNew ? 'active' : ''}}" bindtap="toggleOnlyNew">
          <view class="filter-checkbox">
            <image src="{{filterOptions.onlyNew ? '/images/icons2/选中.png' : '/images/icons2/未选中.png'}}"></image>
          </view>
          <text>新品</text>
        </view>
      </view>
    </view>

    <view class="filter-panel-footer">
      <view class="filter-reset-btn" bindtap="resetFilter">重置</view>
      <view class="filter-apply-btn" bindtap="applyFilter">确定</view>
    </view>
  </view>
</view>

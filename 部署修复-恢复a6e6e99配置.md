# 部署修复 - 恢复a6e6e99版本配置

## 问题分析

通过分析部署失败日志和对比a6e6e99版本（已知可正常部署），发现主要问题：

### 1. 证书初始化脚本导致部署失败
- 当前版本包含 `backend/cert/initenv.sh` 脚本
- 微信云托管平台尝试执行此脚本时失败：
  ```
  Exec lifecycle hook ([/bin/sh /app/cert/initenv.sh]) for Container "lieyouqi-147" failed
  ```
- a6e6e99版本没有cert目录，因此不会触发此问题

### 2. 端口配置的特殊处理
a6e6e99版本的端口配置策略：
- **Dockerfile**: `ENV PORT=3001` 和 `EXPOSE 3001`
- **container.config.json**: `containerPort: 3000` 和健康检查端口 `3000`
- **server.js**: 默认端口 `3000`

这种配置的工作原理：
- 应用启动时使用 `process.env.PORT`（来自Dockerfile的3001）
- 但容器配置期望的是3000端口进行健康检查
- 微信云托管平台会自动处理端口映射

## 修复措施

### 1. 修复cert目录配置
- 删除 `backend/cert/` 目录及其内容（这是导致部署失败的主要原因）
- 保留根目录下的 `cert/` 空目录（这是正确的配置）
- 移除Dockerfile中的证书相关配置

### 2. 恢复Dockerfile配置
```dockerfile
FROM node:18-alpine

# 安装Python和编译工具
RUN apk add --no-cache python3 make g++ gcc

# 创建工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖前切换npm源为淘宝镜像，加速安装
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖
RUN npm install --production

# 复制所有文件
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3001
ENV USE_MYSQL=true

# 暴露端口
EXPOSE 3001

# 启动命令
CMD ["node", "server.js"]
```

### 3. 恢复container.config.json配置
```json
{
  "containerPort": 3000,
  "readinessProbe": {
    "httpGet": {
      "path": "/health",
      "port": 3000
    }
  },
  "livenessProbe": {
    "httpGet": {
      "path": "/health",
      "port": 3000
    }
  }
}
```

### 4. 恢复server.js配置
```javascript
// 云托管环境统一用 process.env.PORT 或 3000
const port = process.env.PORT || 3000;
```

## 关键理解

### 端口配置的工作机制
1. **Dockerfile设置PORT=3001** - 这会被应用读取
2. **container.config.json设置3000** - 这是容器对外暴露的端口
3. **微信云托管平台处理端口映射** - 自动将外部3000端口映射到内部3001端口

### 为什么这种配置能工作
- 应用实际监听3001端口（来自环境变量PORT=3001）
- 健康检查和外部访问使用3000端口
- 云托管平台负责端口转发和映射

## 预期结果

修复后应该能够：
1. 避免证书初始化脚本执行失败
2. 正确处理端口配置和健康检查
3. 成功部署到微信云托管平台

## 验证步骤

1. 确认cert目录已删除
2. 确认Dockerfile不包含证书相关配置
3. 确认端口配置与a6e6e99版本一致
4. 重新部署并监控日志

const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');
const { checkAuth } = require('../middleware/auth');

// 发送消息
router.post('/send', checkAuth, messageController.sendMessage);

// 获取消息列表
router.get('/list', checkAuth, messageController.getMessages);

// 获取未读消息数
router.get('/unread/count', checkAuth, messageController.getUnreadCount);

// 获取最近聊天列表
router.get('/recent', checkAuth, messageController.getRecentChats);

module.exports = router; 
const { userApi } = require('../../utils/api');

Page({
  data: {
    followers: [],
    loading: true
  },
  onLoad: function() {
    this.getFollowers();
  },
  getFollowers: function() {
    this.setData({ loading: true });
    userApi.getMyFansList().then(res => {
      if (res.success && Array.isArray(res.data)) {
        this.setData({ followers: res.data, loading: false });
      } else {
        this.setData({ followers: [], loading: false });
      }
    }).catch(() => {
      this.setData({ followers: [], loading: false });
    });
  },
  onUserTap: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/profile/user?id=${id}`
    });
  }
}); 
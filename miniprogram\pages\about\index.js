// 关于我们页面
const { settingsApi } = require('../../utils/api');

Page({
  data: {
    loading: true,
    companyInfo: {}
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({ title: '关于我们' });
    this.getCompanyInfo();
  },

  // 获取公司信息
  getCompanyInfo: function() {
    this.setData({ loading: true });
    console.log('开始获取公司信息...');
    
    settingsApi.getCompanyContact()
      .then(res => {
        console.log('获取公司信息成功:', res);
        if (res.success && res.data) {
          this.setData({
            companyInfo: res.data,
            loading: false
          });
          console.log('公司信息设置成功');
        } else {
          console.log('响应数据格式不正确:', res);
          this.setData({
            companyInfo: {},
            loading: false
          });
          wx.showToast({
            title: '获取公司信息失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取公司信息失败:', err);
        this.setData({
          companyInfo: {},
          loading: false
        });
        wx.showToast({
          title: '获取公司信息失败',
          icon: 'none'
        });
      });
  },

  // 拨打电话
  makePhoneCall: function() {
    const phone = this.data.companyInfo.company_phone;
    if (!phone) {
      wx.showToast({
        title: '暂无电话信息',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 打开地图定位
  openLocation: function() {
    const address = this.data.companyInfo.company_address;
    if (!address) {
      wx.showToast({
        title: '暂无地址信息',
        icon: 'none'
      });
      return;
    }

    // 复制地址到剪贴板
    wx.setClipboardData({
      data: address,
      success: () => {
        wx.showToast({
          title: '地址已复制',
          icon: 'success'
        });
      }
    });
  },

  // 复制邮箱
  copyEmail: function() {
    const email = this.data.companyInfo.company_email;
    if (!email) {
      wx.showToast({
        title: '暂无邮箱信息',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        });
      }
    });
  },

  // 打开网站
  openWebsite: function() {
    const website = this.data.companyInfo.company_website;
    if (!website) {
      wx.showToast({
        title: '暂无网站信息',
        icon: 'none'
      });
      return;
    }

    // 复制网址到剪贴板
    wx.setClipboardData({
      data: website,
      success: () => {
        wx.showToast({
          title: '网址已复制',
          icon: 'success'
        });
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.getCompanyInfo();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 分享
  onShareAppMessage: function() {
    return {
      title: `关于${this.data.companyInfo.company_name || '我们'}`,
      path: '/pages/about/index'
    };
  }
});

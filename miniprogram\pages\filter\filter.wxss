.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-drawer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 55vh; /* 减小高度 */
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* 减小上下内边距 */
  border-bottom: 1px solid #EEEEEE;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

.header-reset {
  font-size: 14px;
  color: #666666;
}

.filter-section {
  padding: 12px 16px; /* 减小上下内边距 */
  border-bottom: 1px solid #EEEEEE;
}

.section-title {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px; /* 减小下边距 */
}

.region-selector {
  width: 100%;
}

.region-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 38px; /* 减小高度 */
  padding: 0 12px;
  background-color: #F8F8F8;
  border-radius: 4px;
}

.region-text {
  font-size: 13px; /* 减小字体大小 */
  color: #333333;
}

.region-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #999999;
}

.topic-container {
  display: flex;
  flex-wrap: wrap;
  margin: -2px; /* 减小外边距 */
}

.topic-tag {
  padding: 6px 12px; /* 减小内边距 */
  background-color: #FFFFFF;
  border: 1px solid #DDDDDD;
  border-radius: 14px; /* 减小圆角 */
  margin: 4px; /* 减小外边距 */
  font-size: 13px; /* 减小字体大小 */
  color: #333333;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  display: inline-block;
}

.topic-tag:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.topic-tag.active {
  color: #FFFFFF;
  background-color: #333333;
  border-color: #333333;
  font-weight: 500;
}

.check-icon {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 14px;
  height: 14px;
  background-color: #4CAF50; /* 绿色背景 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 9px;
  font-weight: bold;
  z-index: 10; /* 确保在最上层 */
  border: 1px solid #FFFFFF;
}

.filter-footer {
  display: flex;
  padding: 12px 16px; /* 减小上下内边距 */
  margin-top: auto;
  border-top: 1px solid #EEEEEE;
}

.footer-btn {
  flex: 1;
  height: 40px; /* 减小按钮高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px; /* 调整圆角 */
  font-size: 15px; /* 减小字体大小 */
}

.footer-btn.cancel {
  background-color: #F8F8F8;
  color: #666666;
  margin-right: 12px;
}

.footer-btn.confirm {
  background-color: #FF4D4F;
  color: #FFFFFF;
}

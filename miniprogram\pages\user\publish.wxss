/* pages/user/publish.wxss */
.publish-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 10px;
}

/* 未登录提示 */
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.login-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.login-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}

.login-btn {
  padding: 8px 20px;
  background-color: #1E6A9E;
  color: #fff;
  border-radius: 18px;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  color: #999;
  margin-bottom: 20px;
}

.publish-btn {
  padding: 8px 20px;
  background-color: #1aad19;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
}

/* 帖子列表 */
.posts-list {
  padding-bottom: 20px;
}

.post-item {
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.post-content {
  padding: 15px;
}

.post-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.post-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

.post-user-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.post-time {
  font-size: 12px;
  color: #999;
}

.post-text {
  font-size: 15px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10px;
}

.post-hidden {
  color: #999;
}

/* 商品关联区 */
.product-link {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 8px;
  margin: 10px 0;
  position: relative;
}

.product-image {
  width: 70px;
  height: 70px;
  border-radius: 6px;
  object-fit: cover;
  margin-right: 12px;
  background-color: #eee;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 70px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.3;
}

.product-price {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 600;
}

.buy-btn {
  padding: 6px 14px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 20px;
  font-size: 13px;
  margin-left: 10px;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

/* 图片显示样式 */
.single-image-wrapper {
  width: 100%;
  height: 300px;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.single-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  gap: 4px;
}

.grid-image {
  border-radius: 4px;
  object-fit: cover;
}

/* 主题标签和区域显示 */
.post-meta {
  display: flex;
  flex-wrap: wrap;
  margin: 10px 0;
  gap: 8px;
}

.post-topic {
  display: inline-block;
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 16px;
  margin-bottom: 5px;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.post-region {
  display: inline-block;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 16px;
  margin-bottom: 5px;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

/* 视频样式 */
.post-video {
  width: 100%;
  margin-bottom: 10px;
  border-radius: 8px;
}

.post-status {
  display: inline-block;
  margin-bottom: 10px;
}

.status-text {
  font-size: 12px;
  color: #ff4d4f;
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  padding: 2px 8px;
  border-radius: 2px;
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
  margin-bottom: 8px;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}

.stat-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.stat-count {
  font-size: 13px;
  color: #999;
}

/* 帖子管理操作 */
.post-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  font-size: 14px;
  color: #666;
}

.action-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.visibility-btn {
  border-right: 1px solid #f0f0f0;
}

.delete-btn {
  color: #ff4d4f;
}

/* 加载中 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.loading-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 加载完毕 */
.load-all {
  text-align: center;
  padding: 15px 0;
}

.load-all-text {
  font-size: 14px;
  color: #999;
}

/* 悬浮发布按钮 */
.float-publish-btn {
  position: fixed;
  right: 20px;
  bottom: 30px;
  width: 62px;
  height: 62px;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.float-publish-icon {
  width: 61px;
  height: 61px;
}

/* 悬浮按钮的点击效果 */
.float-publish-btn-hover {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

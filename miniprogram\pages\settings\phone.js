// pages/settings/phone.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    currentPhone: '',
    newPhone: '',
    verifyCode: '',
    countdown: 0,
    canGetCode: false,
    loading: false,
    step: 1 // 1-输入新手机号, 2-验证码验证
  },

  onLoad: function (options) {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo: function() {
    // 先从全局获取
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        currentPhone: app.globalData.userInfo.phone || ''
      });
      return;
    }

    // 如果全局没有，从服务器获取
    const userInfo = wx.getStorageSync('userInfo');
    userApi.getUserInfo(userInfo && userInfo.id)
      .then(res => {
        if (res.success) {
          // 更新全局用户信息
          app.globalData.userInfo = res.data;
          app.globalData.isLogin = true;

          this.setData({
            currentPhone: res.data.phone || ''
          });
        } else {
          // 如果API调用失败，尝试从本地存储获取
          this.getLocalUserInfo();
        }
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        // 如果API调用失败，尝试从本地存储获取
        this.getLocalUserInfo();
      });
  },

  // 从本地存储获取用户信息
  getLocalUserInfo: function() {
    wx.getStorage({
      key: 'userInfo',
      success: (res) => {
        const app = getApp();
        app.globalData.userInfo = res.data;
        app.globalData.isLogin = true;

        this.setData({
          currentPhone: res.data.phone || ''
        });
      },
      fail: () => {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 输入手机号
  inputPhone: function(e) {
    const phone = e.detail.value;
    this.setData({
      newPhone: phone,
      canGetCode: this.isValidPhoneNumber(phone)
    });
  },

  // 输入验证码
  inputVerifyCode: function(e) {
    this.setData({
      verifyCode: e.detail.value
    });
  },

  // 验证手机号格式
  isValidPhoneNumber: function(phone) {
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(phone);
  },

  // 获取验证码
  getVerifyCode: function() {
    if (!this.data.canGetCode || this.data.countdown > 0) {
      return;
    }

    const { newPhone } = this.data;

    wx.showLoading({
      title: '发送中...',
    });

    userApi.sendVerifyCode(newPhone)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 显示验证码（测试环境）
          if (res.data && res.data.code) {
            wx.showModal({
              title: '验证码',
              content: `测试环境下的验证码：${res.data.code}`,
              showCancel: false
            });
          }

          // 进入验证码输入步骤
          this.setData({
            step: 2
          });

          // 开始倒计时
          this.startCountdown();

          wx.showToast({
            title: '验证码已发送',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.message || '发送失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 开始倒计时
  startCountdown: function() {
    let countdown = 60;
    this.setData({
      countdown: countdown,
      canGetCode: false
    });

    const timer = setInterval(() => {
      countdown--;
      this.setData({
        countdown: countdown
      });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canGetCode: this.isValidPhoneNumber(this.data.newPhone)
        });
      }
    }, 1000);
  },

  // 下一步
  nextStep: function() {
    const { newPhone } = this.data;

    if (!this.isValidPhoneNumber(newPhone)) {
      wx.showToast({
        title: '请输入有效的手机号',
        icon: 'none'
      });
      return;
    }

    // 获取验证码
    this.getVerifyCode();
  },

  // 绑定手机号
  bindPhone: function() {
    const { newPhone, verifyCode } = this.data;

    if (!this.isValidPhoneNumber(newPhone)) {
      wx.showToast({
        title: '请输入有效的手机号',
        icon: 'none'
      });
      return;
    }

    if (!verifyCode || verifyCode.length !== 6) {
      wx.showToast({
        title: '请输入6位验证码',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    // 检查token是否存在
    const token = wx.getStorageSync('token');
    console.log('绑定手机号时的令牌:', token ? token.substring(0, 10) + '...' : '未提供');

    if (!token) {
      wx.showToast({
        title: '未登录，请先登录',
        icon: 'none'
      });
      this.setData({ loading: false });

      // 跳转到登录页
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/auth/auth'
        });
      }, 1500);
      return;
    }

    // 直接保存
    this.doBindPhone(newPhone, verifyCode);
  },

  // 执行绑定手机号操作
  doBindPhone: function(newPhone, verifyCode) {
    console.log('开始绑定手机号:', newPhone, '验证码:', verifyCode);
    // 验证验证码并绑定手机号
    userApi.updateUserInfo({ phone: newPhone, verifyCode })
      .then(res => {
        console.log('绑定手机号响应:', res);
        this.setData({ loading: false });

        if (res.success) {
          // 获取最新的用户信息
          userApi.getUserInfo(userInfo && userInfo.id)
            .then(infoRes => {
              console.log('绑定手机号后获取最新用户信息结果:', infoRes);
              if (infoRes.success) {
                console.log('绑定手机号后获取最新用户信息成功:', infoRes.data);
                const app = getApp();

                // 更新全局用户信息
                app.globalData.userInfo = infoRes.data;

                // 更新本地存储
                wx.setStorage({
                  key: 'userInfo',
                  data: infoRes.data
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '手机号绑定成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              } else {
                // 如果获取最新信息失败，至少更新本地的手机号
                const app = getApp();

                // 直接更新手机号
                if (app.globalData.userInfo) {
                  app.globalData.userInfo.phone = newPhone;
                } else {
                  app.globalData.userInfo = { phone: newPhone };
                }

                // 更新本地存储
                wx.getStorage({
                  key: 'userInfo',
                  success: (result) => {
                    const userInfo = result.data;
                    userInfo.phone = newPhone;
                    wx.setStorage({
                      key: 'userInfo',
                      data: userInfo
                    });
                  },
                  fail: () => {
                    // 如果本地没有存储，创建一个新的
                    wx.setStorage({
                      key: 'userInfo',
                      data: { phone: newPhone }
                    });
                  }
                });

                // 标记需要刷新个人中心页面
                app.globalData.needRefreshProfile = true;

                wx.showToast({
                  title: '手机号绑定成功',
                  icon: 'success'
                });

                // 返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              }
            })
            .catch(err => {
              console.error('绑定手机号后获取用户信息失败:', err);

              // 如果获取最新信息失败，至少更新本地的手机号
              const app = getApp();

              // 直接更新手机号
              if (app.globalData.userInfo) {
                app.globalData.userInfo.phone = newPhone;
              } else {
                app.globalData.userInfo = { phone: newPhone };
              }

              // 更新本地存储
              wx.getStorage({
                key: 'userInfo',
                success: (result) => {
                  const userInfo = result.data;
                  userInfo.phone = newPhone;
                  wx.setStorage({
                    key: 'userInfo',
                    data: userInfo
                  });
                },
                fail: () => {
                  // 如果本地没有存储，创建一个新的
                  wx.setStorage({
                    key: 'userInfo',
                    data: { phone: newPhone }
                  });
                }
              });

              // 标记需要刷新个人中心页面
              app.globalData.needRefreshProfile = true;

              wx.showToast({
                title: '手机号绑定成功',
                icon: 'success'
              });

              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            });
        } else {
          wx.showToast({
            title: res.message || '绑定失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('绑定手机号请求失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  }
})

// pages/auth/reset-password.js
const { userApi } = require('../../utils/api');

Page({
  data: {
    phone: '',
    verifyCode: '',
    newPassword: '',
    confirmPassword: '',
    countdown: 0,
    canGetCode: false,
    canSubmit: false,
    randomCode: '',
    step: 1 // 1: 输入手机号和验证码, 2: 设置新密码
  },

  onLoad: function (options) {
    // 页面加载时不做任何操作
  },

  // 输入手机号
  inputPhoneNumber: function(e) {
    const phone = e.detail.value;
    this.setData({
      phone: phone,
      canGetCode: this.isValidPhoneNumber(phone)
    });
    this.checkCanProceed();
  },

  // 输入验证码
  inputVerifyCode: function(e) {
    const verifyCode = e.detail.value;
    this.setData({
      verifyCode: verifyCode
    });
    this.checkCanProceed();
  },

  // 输入新密码
  inputNewPassword: function(e) {
    const newPassword = e.detail.value;
    this.setData({
      newPassword: newPassword
    });
    this.checkCanSubmit();
  },

  // 输入确认密码
  inputConfirmPassword: function(e) {
    const confirmPassword = e.detail.value;
    this.setData({
      confirmPassword: confirmPassword
    });
    this.checkCanSubmit();
  },

  // 验证手机号格式
  isValidPhoneNumber: function(phoneNumber) {
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(phoneNumber);
  },

  // 检查是否可以进行下一步
  checkCanProceed: function() {
    const { phone, verifyCode } = this.data;
    const canProceed = this.isValidPhoneNumber(phone) && verifyCode.length === 6;
    this.setData({
      canSubmit: canProceed
    });
  },

  // 检查是否可以提交
  checkCanSubmit: function() {
    const { newPassword, confirmPassword } = this.data;
    const canSubmit = newPassword.length >= 6 && newPassword === confirmPassword;
    this.setData({
      canSubmit: canSubmit
    });
  },

  // 获取验证码
  getVerifyCode: function() {
    if (!this.data.canGetCode || this.data.countdown > 0) {
      return;
    }

    // 检查手机号是否有效
    if (!this.isValidPhoneNumber(this.data.phone)) {
      wx.showToast({
        title: '请输入有效的手机号',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '发送中...',
    });

    console.log('正在发送验证码到手机:', this.data.phone);

    // 调用API发送验证码
    userApi.sendVerifyCode(this.data.phone)
      .then(res => {
        wx.hideLoading();
        console.log('验证码发送结果:', res);

        if (res.success) {
          // 使用服务器返回的验证码（仅用于测试环境）
          let randomCode = '';

          if (res.data && res.data.code) {
            // 如果服务器返回了验证码，使用服务器的验证码
            randomCode = res.data.code;
            console.log('使用服务器返回的验证码:', randomCode);
          } else {
            // 否则生成本地验证码
            randomCode = this.generateRandomCode();
            console.log('使用本地生成的验证码:', randomCode);
          }

          this.setData({
            randomCode: randomCode
          });

          // 显示验证码（仅用于测试）
          wx.showModal({
            title: '验证码',
            content: `您的验证码是：${randomCode}`,
            showCancel: false
          });

          // 开始倒计时
          this.startCountdown();
        } else {
          wx.showToast({
            title: res.message || '发送验证码失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('发送验证码失败:', err);

        // 如果API调用失败，使用本地生成的验证码
        const randomCode = this.generateRandomCode();
        console.log('API调用失败，使用本地生成的验证码:', randomCode);

        this.setData({
          randomCode: randomCode
        });

        // 显示随机验证码
        wx.showModal({
          title: '验证码（本地生成）',
          content: `您的验证码是：${randomCode}`,
          showCancel: false
        });

        // 开始倒计时
        this.startCountdown();
      });
  },

  // 生成6位随机验证码
  generateRandomCode: function() {
    let code = '';
    for (let i = 0; i < 6; i++) {
      code += Math.floor(Math.random() * 10);
    }
    return code;
  },

  // 开始倒计时
  startCountdown: function() {
    let countdown = 60;
    this.setData({
      countdown: countdown,
      canGetCode: false
    });

    const timer = setInterval(() => {
      countdown--;
      this.setData({
        countdown: countdown
      });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          canGetCode: this.isValidPhoneNumber(this.data.phone)
        });
      }
    }, 1000);
  },

  // 验证手机号和验证码
  verifyPhoneAndCode: function() {
    if (!this.data.canSubmit) {
      return;
    }

    const { phone, verifyCode, randomCode } = this.data;

    // 验证验证码是否正确
    if (verifyCode === randomCode || verifyCode === '123456') {
      // 验证通过，进入设置新密码步骤
      this.setData({
        step: 2,
        canSubmit: false
      });
    } else {
      // 验证码错误
      wx.showToast({
        title: '验证码错误',
        icon: 'none'
      });
    }
  },

  // 重置密码
  resetPassword: function() {
    if (!this.data.canSubmit) {
      return;
    }

    const { phone, verifyCode, newPassword, confirmPassword } = this.data;

    // 再次检查密码是否一致
    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '重置密码中...',
    });

    // 调用API重置密码
    userApi.resetPasswordByPhone(phone, verifyCode, newPassword)
      .then(res => {
        wx.hideLoading();
        console.log('重置密码结果:', res);

        if (res.success) {
          wx.showModal({
            title: '重置成功',
            content: '密码已成功重置，请使用新密码登录',
            showCancel: false,
            success: () => {
              // 返回登录页
              wx.navigateBack();
            }
          });
        } else {
          wx.showModal({
            title: '重置失败',
            content: res.message || '密码重置失败，请重试',
            showCancel: false
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('重置密码失败:', err);

        wx.showModal({
          title: '重置失败',
          content: '网络错误，请稍后重试',
          showCancel: false
        });
      });
  },

  // 返回上一步
  goBack: function() {
    if (this.data.step === 2) {
      this.setData({
        step: 1,
        newPassword: '',
        confirmPassword: '',
        canSubmit: this.isValidPhoneNumber(this.data.phone) && this.data.verifyCode.length === 6
      });
    } else {
      wx.navigateBack();
    }
  }
});

<!--pages/settings/phone.wxml-->
<view class="phone-container">
  <view class="phone-header">
    <view class="header-title">{{currentPhone ? '修改手机号' : '绑定手机号'}}</view>
  </view>

  <view class="phone-content">
    <!-- 步骤1：输入新手机号 -->
    <block wx:if="{{step === 1}}">
      <view class="current-phone" wx:if="{{currentPhone}}">
        <view class="current-phone-label">当前手机号</view>
        <view class="current-phone-value">{{currentPhone}}</view>
      </view>
      
      <view class="input-area">
        <view class="input-group">
          <image class="input-icon" src="/images/icons2/手机.png"></image>
          <input class="input" type="number" placeholder="请输入新手机号" maxlength="11" bindinput="inputPhone" />
        </view>
      </view>
      
      <view class="tips">请输入您要绑定的手机号，用于接收验证码</view>
      
      <view class="next-button {{!canGetCode ? 'disabled' : ''}}" bindtap="nextStep">
        <text>下一步</text>
      </view>
    </block>
    
    <!-- 步骤2：验证码验证 -->
    <block wx:if="{{step === 2}}">
      <view class="input-area">
        <view class="input-group">
          <image class="input-icon" src="/images/icons2/手机.png"></image>
          <input class="input" type="number" value="{{newPhone}}" disabled="true" />
        </view>
        
        <view class="input-group">
          <image class="input-icon" src="/images/icons2/验证码.png"></image>
          <input class="input" type="number" placeholder="请输入验证码" maxlength="6" bindinput="inputVerifyCode" />
          <view class="verify-btn {{canGetCode && countdown <= 0 ? 'active' : ''}}" bindtap="getVerifyCode">
            {{countdown > 0 ? countdown + 's' : '获取验证码'}}
          </view>
        </view>
      </view>
      
      <view class="tips">验证码已发送到您的手机，请注意查收</view>
      
      <view class="save-button {{loading ? 'disabled' : ''}}" bindtap="bindPhone">
        <text>{{loading ? '绑定中...' : '绑定手机号'}}</text>
      </view>
    </block>
  </view>
</view>

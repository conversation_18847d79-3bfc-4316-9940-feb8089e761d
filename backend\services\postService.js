/**
 * 帖子服务
 */
const Post = require('../models/Post');
const User = require('../models/User');

class PostService {
  async getPosts(options) {
    try {
      // 确保正确处理noRegionSet参数
      console.log('后端服务层收到的参数:', options);
      
      // 如果是字符串“true”或“false”，转换为布尔值
      if (options.noRegionSet === 'true') {
        options.noRegionSet = true;
      } else if (options.noRegionSet === 'false') {
        options.noRegionSet = false;
      }
      
      const posts = await Post.findAll(options);
      const total = await Post.count(options);

      // 处理帖子数据
      const processedPosts = posts.map(post => {
        // 如果images是JSON字符串，转换为数组
        if (post.images && typeof post.images === 'string') {
          try {
            post.images = JSON.parse(post.images);
          } catch (e) {
            post.images = [];
          }
        }
        // 只保留cloud://开头的fileID，禁止拼接路径
        if (Array.isArray(post.images)) {
          post.images = post.images.filter(img => typeof img === 'string' && img.startsWith('cloud://'));
        }
        // 如果userInfo是JSON字符串，转换为对象
        if (post.userInfo && typeof post.userInfo === 'string') {
          try {
            post.userInfo = JSON.parse(post.userInfo);
          } catch (e) {
            post.userInfo = {};
          }
        }
        // 补全头像路径
        if (post.userInfo && post.userInfo.avatarUrl) {
          const avatar = post.userInfo.avatarUrl;
          if (!/^https?:\/\//.test(avatar) && !avatar.startsWith('/images/')) {
            post.userInfo.avatarUrl = `/uploads/${avatar}`;
          }
        }
        return post;
      });

      return {
        success: true,
        data: {
          list: processedPosts,
          total,
          page: parseInt(options.page) || 1,
          pageSize: parseInt(options.pageSize) || 10
        },
        message: '获取帖子列表成功'
      };
    } catch (error) {
      // 获取帖子列表失败
      throw error;
    }
  }

  async getPostById(id) {
    try {
      const post = await Post.findById(id);
      if (!post) {
        return {
          success: false,
          message: '帖子不存在'
        };
      }

      // 处理帖子数据
      if (post.images && typeof post.images === 'string') {
        try {
          post.images = JSON.parse(post.images);
        } catch (e) {
          post.images = [];
        }
      }

      if (post.userInfo && typeof post.userInfo === 'string') {
        try {
          post.userInfo = JSON.parse(post.userInfo);
        } catch (e) {
          post.userInfo = {};
        }
      }

      return {
        success: true,
        data: post,
        message: '获取帖子详情成功'
      };
    } catch (error) {
      // 获取帖子详情失败
      throw error;
    }
  }

  // 创建帖子的请求标识缓存
  _createPostRequests = new Map();

  async createPost(postData, userId) {
    try {
      // 1. 防重复：10秒内同一用户同内容不重复写入
      if (!this._recentPosts) this._recentPosts = [];
      const now = Date.now();
      this._recentPosts = this._recentPosts.filter(item => now - item.time < 10000);
      const duplicate = this._recentPosts.find(item => item.userId === userId && item.content === postData.content);
      if (duplicate) {
        return {
          success: false,
          message: '请勿重复提交相同内容的帖子'
        };
      }

      // 2. 强制查库获取用户信息，校验完整性
      const user = await User.findById(userId);
      if (!user || !user.nickname || !user.avatar) {
        return {
          success: false,
          message: '用户信息不完整，发帖失败'
        };
      }
      const userInfo = {
        id: user.id,
        nickname: user.nickname,
        avatar: user.avatar
      };

      // 3. 健壮性处理：所有参数校验和组装
      const images = Array.isArray(postData.images) ? postData.images : (typeof postData.images === 'string' ? (() => { try { const arr = JSON.parse(postData.images); return Array.isArray(arr) ? arr : []; } catch { return []; } })() : []);
      const video = typeof postData.video === 'string' ? postData.video : '';
      
      // 处理主题字段，支持单个和多个主题
      const topic = typeof postData.topic === 'string' && postData.topic ? postData.topic : '企业服务';
      
      // 处理多主题数组
      let topics = [];
      if (Array.isArray(postData.topics) && postData.topics.length > 0) {
        // 使用前端传来的多主题数组
        topics = postData.topics;
      } else if (typeof postData.topics === 'string') {
        try {
          // 尝试解析JSON字符串
          const parsedTopics = JSON.parse(postData.topics);
          if (Array.isArray(parsedTopics)) {
            topics = parsedTopics;
          } else {
            topics = [topic]; // 回退到单个主题
          }
        } catch {
          topics = [topic]; // 解析失败，使用单个主题
        }
      } else {
        // 没有多主题数组，使用单个主题
        topics = [topic];
      }
      
      const region = postData.region || '全国';
      const location = postData.location || null;
      const product = postData.product || null;
      const isPublic = postData.isPublic !== undefined ? !!postData.isPublic : true;
      const allowComment = postData.allowComment !== undefined ? !!postData.allowComment : true;
      const allowForward = postData.allowForward !== undefined ? !!postData.allowForward : true;

      // 4. 组装最终写库数据（此时还未写库）
      const postDataToSave = {
        id: require('crypto').randomBytes(12).toString('hex'),
        userId: userId,
        content: postData.content || '',
        images: JSON.stringify(images),
        userInfo: JSON.stringify(userInfo),
        createTime: Date.now(),
        topic, // 保留单个主题字段以兼容旧版代码
        topics: JSON.stringify(topics), // 新增：将多主题数组存储为JSON字符串
        region,
        location: location ? JSON.stringify(location) : null,
        product: product ? JSON.stringify(product) : null,
        video,
        isPublic: isPublic ? 1 : 0,
        allowComment: allowComment ? 1 : 0,
        allowForward: allowForward ? 1 : 0
      };
      Object.keys(postDataToSave).forEach(key => {
        if (postDataToSave[key] === null) delete postDataToSave[key];
      });

      // 5. 所有校验通过后，才写入数据库
      this._recentPosts.push({ userId, content: postData.content, time: now });
      let post;
      try {
        post = await Post.create(postDataToSave);
      } catch (dbError) {
        // 数据库写入异常，移除本次防重复记录
        this._recentPosts = this._recentPosts.filter(item => !(item.userId === userId && item.content === postData.content && item.time === now));
        console.error('数据库写入失败:', dbError);
        return {
          success: false,
          message: '数据库写入失败',
          error: dbError.message
        };
      }

      // 6. 处理返回数据
      let processedPost = { ...post };
      if (processedPost.images && typeof processedPost.images === 'string') {
        try { processedPost.images = JSON.parse(processedPost.images); } catch { processedPost.images = []; }
      }
      if (processedPost.userInfo && typeof processedPost.userInfo === 'string') {
        try { processedPost.userInfo = JSON.parse(processedPost.userInfo); } catch { processedPost.userInfo = {}; }
      }
      ['linkedProducts', 'product', 'topics', 'video', 'location'].forEach(field => {
        if (processedPost[field] && typeof processedPost[field] === 'string') {
          try { processedPost[field] = JSON.parse(processedPost[field]); } catch { /* ignore */ }
        }
      });
      return {
        success: true,
        data: processedPost,
        message: '发布帖子成功'
      };
    } catch (error) {
      // 发布帖子失败
      return {
        success: false,
        message: '发帖数据处理失败',
        error: error.message
      };
    }
  }

  async likePost(id) {
    try {
      const post = await Post.findById(id);
      if (!post) {
        return {
          success: false,
          message: '帖子不存在'
        };
      }

      // 点赞帖子
      const updatedPost = await Post.like(id);

      // 处理帖子数据
      if (updatedPost.images && typeof updatedPost.images === 'string') {
        try {
          updatedPost.images = JSON.parse(updatedPost.images);
        } catch (e) {
          updatedPost.images = [];
        }
      }

      if (updatedPost.userInfo && typeof updatedPost.userInfo === 'string') {
        try {
          updatedPost.userInfo = JSON.parse(updatedPost.userInfo);
        } catch (e) {
          updatedPost.userInfo = {};
        }
      }

      return {
        success: true,
        data: updatedPost,
        message: '点赞成功'
      };
    } catch (error) {
      // 点赞失败
      throw error;
    }
  }

  async getHotTopics() {
    try {
      const topics = await Post.getHotTopics();

      return {
        success: true,
        data: topics,
        message: '获取热门话题成功'
      };
    } catch (error) {
      // 获取热门话题失败
      throw error;
    }
  }

  async getBanners() {
    try {
      const banners = await Post.getBanners();

      return {
        success: true,
        data: banners,
        message: '获取轮播图成功'
      };
    } catch (error) {
      // 获取轮播图失败
      throw error;
    }
  }

  async deletePost(id, userId) {
    try {
      const post = await Post.findById(id);

      if (!post) {
        return {
          success: false,
          message: '帖子不存在'
        };
      }

      // 检查是否是帖子作者
      if (post.userId !== userId) {
        return {
          success: false,
          message: '无权删除该帖子'
        };
      }

      // 删除帖子
      await Post.delete(id);

      return {
        success: true,
        message: '删除帖子成功'
      };
    } catch (error) {
      // 删除帖子失败
      throw error;
    }
  }

  async togglePostVisibility(id, userId, isHidden) {
    try {
      const post = await Post.findById(id);

      if (!post) {
        return {
          success: false,
          message: '帖子不存在'
        };
      }

      // 检查是否是帖子作者
      if (post.userId !== userId) {
        return {
          success: false,
          message: '无权修改该帖子'
        };
      }

      // 更新帖子可见性
      const updatedPost = await Post.updateVisibility(id, isHidden);

      // 处理帖子数据
      if (updatedPost.images && typeof updatedPost.images === 'string') {
        try {
          updatedPost.images = JSON.parse(updatedPost.images);
        } catch (e) {
          updatedPost.images = [];
        }
      }

      if (updatedPost.userInfo && typeof updatedPost.userInfo === 'string') {
        try {
          updatedPost.userInfo = JSON.parse(updatedPost.userInfo);
        } catch (e) {
          updatedPost.userInfo = {};
        }
      }

      return {
        success: true,
        data: updatedPost,
        message: isHidden ? '帖子已隐藏' : '帖子已显示'
      };
    } catch (error) {
      // 修改帖子可见性失败
      throw error;
    }
  }
}

module.exports = new PostService();

# 部署失败修正说明

## 问题分析

根据部署失败日志分析，主要问题是微信云托管平台的证书管理机制与我们的配置冲突。

### 根本原因
微信云托管平台会强制注入证书管理脚本，无论代码中是否包含cert目录，都会自动创建`/app/cert/initenv.sh`并执行。问题是缺少`update-ca-certificates`命令。

### 最新发现
从部署失败0041.txt日志可以看出：
1. 微信云托管会强制创建cert目录和initenv.sh脚本
2. 脚本执行时报错：`update-ca-certificates: not found`
3. 即使删除cert目录，平台仍会自动注入

### 解决方案
1. 在Dockerfile中添加ca-certificates包
2. 提供自己的initenv.sh脚本，避免被平台覆盖
3. 确保脚本有正确的权限

## 修正措施

### 1. 添加必要的证书支持
- ✅ 在Dockerfile中添加ca-certificates包
- ✅ 创建自己的initenv.sh脚本
- ✅ 设置脚本执行权限

### 2. 保持简单的配置
- ✅ 使用简单的Dockerfile（node:18-alpine）
- ✅ 移除复杂的生命周期钩子
- ✅ 统一端口配置

### 3. 应对微信云托管强制注入
- ✅ 提供兼容的initenv.sh脚本
- ✅ 确保update-ca-certificates命令可用
- ✅ 脚本快速成功退出

## 关键修改文件

1. **backend/Dockerfile**
   - 使用node:18-alpine基础镜像
   - 添加ca-certificates包支持证书管理
   - 设置initenv.sh脚本权限
   - 保持简单的构建流程

2. **backend/cert/initenv.sh** (新增)
   - 提供兼容微信云托管的证书脚本
   - 检查并使用update-ca-certificates命令
   - 快速成功退出

3. **backend/server.js**
   - 保持简单版本
   - 使用3000端口
   - 移除复杂的初始化逻辑

4. **backend/container.config.json**
   - 移除lifecycleHook配置
   - 统一使用3000端口
   - 保持简单的健康检查配置

## 预期效果

1. **解决证书命令缺失问题**：添加ca-certificates包，提供update-ca-certificates命令
2. **兼容微信云托管注入**：提供自己的initenv.sh脚本，避免被平台覆盖
3. **快速启动**：脚本简单快速，避免超时问题
4. **配置一致性**：端口配置统一，避免连接问题

## 部署建议

1. 确保数据库服务已启动
2. 检查环境变量配置正确
3. 监控容器启动日志，关注证书初始化过程
4. 验证健康检查端点响应

## 监控要点

- 证书初始化应该成功完成，无"not found"错误
- 容器启动时间应在60秒内完成
- 健康检查应在90秒内开始响应
- 数据库连接正常
- 无SIGKILL错误（错误代码137）

## 关键改进

本次修正的核心策略是：
1. **接受微信云托管的证书管理机制**，而不是试图绕过它
2. **提供必要的依赖**（ca-certificates包）
3. **提供兼容的脚本**，确保证书初始化能够成功完成
4. **保持配置简单**，减少其他潜在问题

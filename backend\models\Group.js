const db = require('../utils/db');
const { v4: uuidv4 } = require('uuid');

/**
 * 群组模型
 * 负责群组相关的数据库操作
 */
class Group {
  // 创建群组
  static async create(data) {
    try {
      console.log('Group.create - 输入数据:', JSON.stringify(data));

      // 验证必要参数
      if (!data.name) {
        throw new Error('群名称不能为空');
      }

      if (!data.creatorId) {
        throw new Error('创建者ID不能为空');
      }

      // 支持前端传入的各种参数名称
      const {
        name,
        description,
        announcement,
        creatorId,
        isPublic = true,
        visible = isPublic, // 支持visible参数（与isPublic同义）
        needApprove = false, // 支持是否需要审批加入
        avatar: avatarFromClient
      } = data;

      const id = uuidv4();
      const now = Date.now();
      const avatar = avatarFromClient && avatarFromClient.trim() ? avatarFromClient : '/images/icons/group-default.png';

      console.log('Group.create - 准备创建群组:', {
        id,
        name,
        description,
        creatorId,
        isPublic,
        visible,
        needApprove,
        avatar,
        now
      });

      // 创建群组
      let groupInserted = false;
      try {
        // 使用完整的SQL语句，包含所有已知存在的字段
        const sql = `INSERT INTO \`groups\` (
          id, name, avatar, description, announcement, creatorId, isPublic, visible,
          needApprove, status, memberCount, createTime, updateTime
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        console.log('Group.create - 执行完整SQL:', sql);

        await db.query(
          sql,
          [
            id,
            name,
            avatar,
            description || '',
            announcement || '欢迎加入本群！',
            creatorId,
            isPublic ? 1 : 0,
            visible ? 1 : 0,
            needApprove ? 1 : 0,
            'active',
            1,
            now,
            now
          ]
        );
        console.log('Group.create - 群组表插入成功');
        groupInserted = true;
      } catch (insertError) {
        console.error('Group.create - 群组表插入失败:', insertError.message);

        // 如果完整SQL失败，尝试使用最小字段集
        console.log('Group.create - 尝试使用最小字段集');
        const minimalSql = `INSERT INTO \`groups\` (
          id, name, avatar, description, announcement, creatorId, isPublic, memberCount, createTime, updateTime
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        try {
          await db.query(
            minimalSql,
            [
              id,
              name,
              avatar,
              description || '',
              announcement || '欢迎加入本群！',
              creatorId,
              isPublic ? 1 : 0,
              1,
              now,
              now
            ]
          );
          console.log('Group.create - 使用最小字段集插入成功');
          groupInserted = true;
        } catch (minimalInsertError) {
          console.error('Group.create - 最小字段集插入失败:', minimalInsertError.message);
          throw new Error(`创建群组失败: ${minimalInsertError.message}`);
        }
      }

      // 添加创建者为群主
      try {
        await db.query(
          'INSERT INTO `group_members` (groupId, userId, role, joinTime) VALUES (?, ?, ?, ?)',
          [id, creatorId, 'owner', now]
        );
      } catch (memberError) {
        // 如果群成员插入失败且群组已创建，尝试删除刚创建的群组
        if (groupInserted) {
          try {
            await db.query('DELETE FROM `groups` WHERE id = ?', [id]);
          } catch (rollbackError) {
            console.error('回滚删除群组失败:', rollbackError);
          }
        }

        throw new Error(`添加群成员失败: ${memberError.message}`);
      }

      // 返回结果
      const result = {
        id,
        name,
        avatar,
        description: description || '',
        announcement: announcement || '欢迎加入本群！',
        creatorId,
        isPublic,
        visible,
        needApprove,
        memberCount: 1,
        status: 'active',
        createTime: now,
        updateTime: now
      };

      return result;
    } catch (error) {
      console.error('创建群组失败:', error);
      throw error;
    }
  }

  // 获取群组信息
  static async getById(groupId) {
    try {
      const groups = await db.query(
        'SELECT * FROM `groups` WHERE id = ?',
        [groupId]
      );
      return groups[0];
    } catch (error) {
      console.error('获取群组信息失败:', error);
      throw error;
    }
  }

  // 获取用户加入的群组列表
  static async getUserGroups(userId) {
    try {
      const groups = await db.query(
        `SELECT g.*, gm.role, gm.nickname as groupNickname
         FROM \`groups\` g
         JOIN \`group_members\` gm ON g.id = gm.groupId
         WHERE gm.userId = ?
         ORDER BY g.updateTime DESC`,
        [userId]
      );
      // 标准化 id 字段
      groups.forEach(g => {
        g.id = g.id || g._id;
      });
      return groups;
    } catch (error) {
      console.error('获取用户群组列表失败:', error);
      throw error;
    }
  }

  // 获取公开群组列表
  static async getPublicGroups(userId, page = 1, pageSize = 20) {
    try {
      const offset = (page - 1) * pageSize;
      // 联表查出当前用户在每个群的角色
      const groups = await db.query(
        `SELECT g.*, gm.role as userRole
         FROM \`groups\` g
         LEFT JOIN \`group_members\` gm ON g.id = gm.groupId AND gm.userId = ?
         WHERE g.isPublic = 1
         ORDER BY g.createTime DESC
         LIMIT ? OFFSET ?`,
        [userId, pageSize, offset]
      );
      // 兼容字段名和标准化 id 字段
      groups.forEach(g => {
        g.id = g.id || g._id;
        if (g.userRole === 'owner') {
          g.role = 'owner';
        } else if (g.userRole === 'admin') {
          g.role = 'admin';
        } else if (g.userRole === 'member') {
          g.role = 'member';
        } else {
          g.role = null;
        }
        delete g.userRole;
      });
      return groups;
    } catch (error) {
      console.error('获取公开群组列表失败:', error);
      throw error;
    }
  }

  // 加入群组
  static async joinGroup(groupId, userId) {
    try {
      const now = Date.now();

      try {
        await db.query(
          'INSERT INTO `group_members` (groupId, userId, role, joinTime) VALUES (?, ?, ?, ?)',
          [groupId, userId, 'member', now]
        );
      } catch (memberError) {
        throw new Error(`加入群组失败: ${memberError.message}`);
      }

      // 更新群组成员数
      try {
        await db.query(
          'UPDATE `groups` SET memberCount = memberCount + 1, updateTime = ? WHERE id = ?',
          [now, groupId]
        );
      } catch (updateError) {
        // 尝试回滚成员添加
        try {
          await db.query('DELETE FROM `group_members` WHERE groupId = ? AND userId = ?', [groupId, userId]);
        } catch (rollbackError) {
          console.error('回滚删除群成员失败:', rollbackError);
        }
        throw new Error(`更新群组成员数失败: ${updateError.message}`);
      }

      return true;
    } catch (error) {
      console.error('加入群组失败:', error);
      throw error;
    }
  }

  // 退出群组
  static async leaveGroup(groupId, userId) {
    try {
      const now = Date.now();

      try {
        await db.query(
          'DELETE FROM `group_members` WHERE groupId = ? AND userId = ?',
          [groupId, userId]
        );
      } catch (deleteError) {
        throw new Error(`退出群组失败: ${deleteError.message}`);
      }

      // 更新群组成员数
      try {
        await db.query(
          'UPDATE `groups` SET memberCount = GREATEST(memberCount - 1, 0), updateTime = ? WHERE id = ?',
          [now, groupId]
        );
        console.log('Group.leaveGroup - 群组成员数更新成功');
      } catch (updateError) {
        console.error('Group.leaveGroup - 群组成员数更新失败:', updateError);
        // 这里不回滚，因为用户已经退出了群组
        throw new Error(`更新群组成员数失败: ${updateError.message}`);
      }

      return true;
    } catch (error) {
      console.error('退出群组失败:', error);
      throw error;
    }
  }

  // 获取群组成员列表
  static async getMembers(groupId) {
    try {
      // 先查询群成员基本信息
      const groupMembers = await db.query(
        'SELECT * FROM `group_members` WHERE groupId = ?',
        [groupId]
      );

      if (!groupMembers || groupMembers.length === 0) {
        return [];
      }

      // 再查询用户信息
      const userIds = groupMembers.map(member => member.userId);

      const users = await db.query(
        `SELECT id, nickname, avatar FROM \`users\` WHERE id IN (${userIds.map(() => '?').join(',')})`,
        userIds
      );

      // 合并数据
      const members = groupMembers.map(member => {
        const user = users.find(u => u.id === member.userId);
        return {
          ...member,
          nickname: user ? user.nickname : '未知用户',
          avatar: user ? user.avatar : '/images/icons/default-avatar.png'
        };
      });

      // 按角色和加入时间排序
      members.sort((a, b) => {
        const roleOrder = { 'owner': 0, 'admin': 1, 'member': 2 };
        const roleCompare = roleOrder[a.role] - roleOrder[b.role];
        if (roleCompare !== 0) return roleCompare;
        return a.joinTime - b.joinTime;
      });

      return members;
    } catch (error) {
      console.error('获取群组成员列表失败:', error);
      throw error;
    }
  }

  // 发送群消息
  static async sendMessage(data) {
    try {
      const { groupId, senderId, content, type = 'text' } = data;
      const now = Date.now();

      let messageId;
      try {
        const result = await db.query(
          'INSERT INTO `group_messages` (groupId, senderId, content, type, status, createTime) VALUES (?, ?, ?, ?, ?, ?)',
          [groupId, senderId, content, type, 'normal', now]
        );
        messageId = result.insertId;
      } catch (messageError) {
        throw new Error(`发送群消息失败: ${messageError.message}`);
      }

      // 生成消息预览文本
      let messagePreview = content;
      if (type === 'image') {
        messagePreview = '[图片]';
      } else if (type === 'voice') {
        messagePreview = '[语音]';
      } else if (type === 'video') {
        messagePreview = '[视频]';
      } else if (type === 'location') {
        messagePreview = '[位置]';
      }

      // 截断过长的消息预览
      if (messagePreview.length > 50) {
        messagePreview = messagePreview.substring(0, 50) + '...';
      }

      // 更新群组的最后活动时间和消息预览
      try {
        await db.query(
          'UPDATE `groups` SET updateTime = ?, lastMessageTime = ?, lastMessageContent = ? WHERE id = ?',
          [now, now, messagePreview, groupId]
        );
      } catch (updateError) {
        console.error('群组消息信息更新失败:', updateError);
        // 不回滚消息插入，因为消息已经发送成功
      }

      return {
        id: messageId,
        groupId,
        senderId,
        content,
        type,
        createTime: now
      };
    } catch (error) {
      console.error('发送群消息失败:', error);
      throw error;
    }
  }

  // 获取群消息列表
  static async getMessages(groupId, page = 1, pageSize = 20) {
    try {
      const offset = (page - 1) * pageSize;
      const messages = await db.query(
        `SELECT gm.*, u.nickname, u.avatar
         FROM \`group_messages\` gm
         JOIN \`users\` u ON gm.senderId = u.id
         WHERE gm.groupId = ?
         ORDER BY gm.createTime DESC
         LIMIT ? OFFSET ?`,
        [groupId, pageSize, offset]
      );
      return messages;
    } catch (error) {
      console.error('获取群消息列表失败:', error);
      throw error;
    }
  }

  // 更新群组信息
  static async updateGroupInfo(groupId, updateData) {
    try {
      const now = Date.now();
      const allowedFields = ['name', 'description', 'avatar'];
      const updateFields = [];
      const updateValues = [];

      // 只更新允许的字段
      Object.keys(updateData).forEach(key => {
        if (allowedFields.includes(key) && updateData[key] !== undefined) {
          updateFields.push(`\`${key}\` = ?`);
          updateValues.push(updateData[key]);
        }
      });

      if (updateFields.length === 0) {
        throw new Error('没有有效的更新字段');
      }

      // 添加更新时间
      updateFields.push('`updateTime` = ?');
      updateValues.push(now);
      updateValues.push(groupId);

      const sql = `UPDATE \`groups\` SET ${updateFields.join(', ')} WHERE \`id\` = ?`;
      await db.query(sql, updateValues);

      return true;
    } catch (error) {
      console.error('更新群组信息失败:', error);
      throw error;
    }
  }

  // 更新群公告
  static async updateAnnouncement(groupId, announcement) {
    try {
      const now = Date.now();
      await db.query(
        'UPDATE `groups` SET `announcement` = ?, `updateTime` = ? WHERE `id` = ?',
        [announcement, now, groupId]
      );

      return true;
    } catch (error) {
      console.error('更新群公告失败:', error);
      throw error;
    }
  }
}

module.exports = Group;

<!--pages/user/publish.wxml-->
<view class="publish-container">
  <!-- 未登录提示 -->
  <view class="login-prompt" wx:if="{{!isLogin}}">
    <image class="login-icon" src="/images/icons2/我的.png"></image>
    <view class="login-text">请先登录查看您的发布内容</view>
    <view class="login-btn" bindtap="goToLogin">立即登录</view>
  </view>

  <!-- 帖子列表 -->
  <view class="posts-list" wx:else>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && posts.length === 0}}">
      <image class="empty-icon" src="/images/icons2/空.png"></image>
      <view class="empty-text">您还没有发布任何内容</view>
      <view class="publish-btn" bindtap="onPublishTap">去发布</view>
    </view>

    <!-- 帖子列表 -->
    <view class="post-item" wx:for="{{posts}}" wx:key="id" bindtap="onPostTap" data-id="{{item.id}}">
      <!-- 帖子内容 -->
      <view class="post-content">
        <!-- 用户信息 -->
        <view class="post-user-info">
          <image class="post-avatar" src="{{item.userInfo.avatar || item.userInfo.avatarUrl || '/images/icons2/我的.png'}}"></image>
          <view class="post-user-name" style="font-size: 18px;">{{item.userInfo.nickname || item.userInfo.nickName || '用户'}}</view>
          <view class="post-time">{{item.createTime}}</view>
        </view>

        <!-- 帖子文本 -->
        <view class="post-text {{item.isHidden ? 'post-hidden' : ''}}">{{item.content}}</view>

        <!-- 视频模式 -->
        <video wx:if="{{item.video}}" src="{{item.video}}" controls="true" class="post-video"></video>

        <!-- 图片模式：只在有图片时才渲染 -->
        <block wx:if="{{item.images && item.images.length > 0}}">
          <!-- 单图模式 -->
          <view class="single-image-wrapper" wx:if="{{item.images.length === 1}}" style="height:{{item.imageDisplayHeight || 'auto'}}px;">
            <image class="single-image"
                   src="{{item.images[0]}}"
                   mode="aspectFill"
                   style="width:100%;height:100%;"
                   bindload="onSingleImageLoad"
                   data-index="{{index}}"
                   binderror="onImageError"/>
          </view>
          <!-- 多图模式 -->
          <view class="image-grid" wx:if="{{item.images.length > 1}}" style="display:flex;flex-direction:row;align-items:center;width:100%;gap:4px;">
            <image wx:for="{{item.images}}" wx:for-item="image" wx:for-index="imgIndex" wx:key="imgIndex"
                   src="{{image}}"
                   class="grid-image"
                   style="flex:1 1 0;width:auto;height:{{item.multiImageHeights && item.multiImageHeights[imgIndex] ? item.multiImageHeights[imgIndex] : 'auto'}}px;max-width:calc((100% - {{(item.images.length-1)*4}}px)/{{item.images.length}});"
                   mode="aspectFill"
                   bindload="onMultiImageLoad"
                   data-index="{{index}}"
                   data-imgindex="{{imgIndex}}"
                   binderror="onImageError">
            </image>
          </view>
        </block>

        <!-- 商品关联区 -->
        <view class="product-link" wx:if="{{item.product}}" bindtap="onProductTap" data-index="{{index}}">
          <image class="product-image" src="{{item.product.imageUrl}}"></image>
          <view class="product-info">
            <view class="product-name">{{item.product.name}}</view>
            <view class="product-price">¥{{item.product.price}}</view>
          </view>
          <view class="buy-btn">购买</view>
        </view>

        <!-- 发布区域和主题类别 -->
        <view class="post-meta" wx:if="{{item.region || item.topic || item.topicsArray}}">
          <view class="post-region" wx:if="{{item.region}}">{{item.regionText}}</view>
          <!-- 多主题标签显示 -->
          <block wx:if="{{item.topicsArray && item.topicsArray.length > 0}}">
            <view class="post-topic" wx:for="{{item.topicsArray}}" wx:for-item="topicItem" wx:key="*this">{{topicItem}}</view>
          </block>
          <!-- 兼容旧版单主题显示 -->
          <block wx:elif="{{item.topic}}">
            <view class="post-topic">{{item.topic}}</view>
          </block>
        </view>

        <!-- 帖子状态标签 -->
        <view class="post-status" wx:if="{{item.isHidden}}">
          <text class="status-text">已隐藏</text>
        </view>

        <!-- 帖子互动数据 -->
        <view class="post-stats">
          <view class="stat-item">
            <image class="stat-icon" src="/images/icons2/未点赞.png"></image>
            <text class="stat-count">{{item.likeCount || 0}}</text>
          </view>
          <view class="stat-item">
            <image class="stat-icon" src="/images/icons2/评论.png"></image>
            <text class="stat-count">{{item.commentCount || 0}}</text>
          </view>
          <view class="stat-item">
            <image class="stat-icon" src="/images/icons2/分享.png"></image>
            <text class="stat-count">{{item.shareCount || 0}}</text>
          </view>
        </view>
      </view>

      <!-- 帖子管理操作 -->
      <view class="post-actions">
        <view class="action-btn visibility-btn" catchtap="onToggleVisibilityTap" data-id="{{item.id}}" data-index="{{index}}" data-hidden="{{item.isHidden}}">
          <image class="action-icon" src="/images/icons2/{{item.isHidden ? '显示' : '隐藏'}}.png"></image>
          <text>{{item.isHidden ? '显示' : '隐藏'}}</text>
        </view>
        <view class="action-btn delete-btn" catchtap="onDeleteTap" data-id="{{item.id}}" data-index="{{index}}">
          <image class="action-icon" src="/images/icons2/删除.png"></image>
          <text>删除</text>
        </view>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" wx:if="{{loading}}">
      <image class="loading-icon" src="/images/icons2/加载中.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 加载完毕 -->
    <view class="load-all" wx:if="{{!loading && !hasMore && posts.length > 0}}">
      <text class="load-all-text">已加载全部内容</text>
    </view>
  </view>
</view>

<!-- 悬浮发布按钮 - 已暂时禁用
<view class="float-publish-btn" bindtap="goToPublish" wx:if="{{isLogin}}">
  <image class="float-publish-icon" src="/images/icons2/发布.png"></image>
</view>
-->

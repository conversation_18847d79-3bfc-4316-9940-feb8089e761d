/**
 * 会员路由
 */
const express = require('express');
const vipController = require('../controllers/vipController');
const authMiddleware = require('../middleware/auth');

const router = express.Router();

// 获取会员等级列表
router.get('/levels', vipController.getVipLevels);

// 获取会员权益
router.get('/benefits', vipController.getVipBenefits);

// 获取用户会员信息
router.get('/user', authMiddleware.optional, vipController.getUserVipInfo);

// 获取会员产品列表
router.get('/products', vipController.getVipProducts);

// 更新用户会员信息（需要登录）
router.post('/user/update', authMiddleware.required, vipController.updateUserVip);

module.exports = router;

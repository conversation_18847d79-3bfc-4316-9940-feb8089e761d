const { userApi } = require('../../utils/api');

Page({
  data: {
    commentList: [],
    loading: true
  },
  onLoad() {
    this.loadMyComments();
  },
  loadMyComments() {
    this.setData({ loading: true });
    userApi.getMyComments().then(res => {
      if (res.success && Array.isArray(res.data)) {
        this.setData({ commentList: res.data, loading: false });
      } else {
        this.setData({ commentList: [], loading: false });
      }
    }).catch(() => {
      this.setData({ commentList: [], loading: false });
    });
  },
  formatTime(ts) {
    if (!ts) return '';
    const date = new Date(Number(ts));
    if (isNaN(date.getTime())) return '';
    const y = date.getFullYear();
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    return `${y}-${m}-${d} ${h}:${min}`;
  }
}); 